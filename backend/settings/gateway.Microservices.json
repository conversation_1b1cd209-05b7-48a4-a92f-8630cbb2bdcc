{"Gateway": {"EnabledModules": ["itmctr"], "DeploymentMode": "Microservices", "ModuleConfigs": {"itmctr": {"Name": "itmctr", "Enabled": true, "Settings": {"EnableProjectTracking": true, "EnableReporting": true}, "Dependencies": ["rbac", "dynamicform", "files"]}}}, "ServiceCall": {"Mode": "Remote", "TimeoutMs": 30000, "RetryCount": 3, "EnableCache": true, "CacheExpirationSeconds": 300}, "Endpoint": [{"Name": "Rbac", "Url": "http://rbac-service:8080"}, {"Name": "RbacMgt", "Url": "http://rbac-mgt-service:8080"}, {"Name": "Files", "Url": "http://files-service:8080"}, {"Name": "FilesMgt", "Url": "http://files-mgt-service:8080"}, {"Name": "DynamicForm", "Url": "http://dynamic-form-service:8080"}, {"Name": "DynamicFormMgt", "Url": "http://dynamic-form-mgt-service:8080"}, {"Name": "Messaging", "Url": "http://messaging-service:8080"}, {"Name": "MessagingMgt", "Url": "http://messaging-mgt-service:8080"}, {"Name": "Logging", "Url": "http://logging-service:8080"}, {"Name": "LoggingMgt", "Url": "http://logging-mgt-service:8080"}], "Cors": {"Origins": ["*"]}, "Database": {"ConnectionString": "Host=postgres-cluster;Port=5432;Database=xj_framework_itmctr;Username=postgres;Password=******;", "DatabaseType": "PostgreSQL"}}
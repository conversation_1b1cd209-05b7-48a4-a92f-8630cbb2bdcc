{"Modules": {"EnabledModules": {"Rbac": false, "RbacMgt": false, "Messaging": false, "MessagingMgt": false, "DynamicForm": false, "DynamicFormMgt": false, "Files": false, "FilesMgt": false, "Logging": false, "Itmctr": true, "ItmctrMgt": true}}, "ServiceProxy": {"DefaultCallMode": "Remote", "ServiceConfigs": {"IUserService": {"CallMode": "Remote", "EnableCache": true, "CacheExpirationSeconds": 300}, "IMessageService": {"CallMode": "Remote", "EnableCache": false}, "IFormService": {"CallMode": "Remote", "EnableCache": true, "CacheExpirationSeconds": 600}, "IFileInfoService": {"CallMode": "Remote", "EnableCache": false}}}, "Database": {"ConnectionString": "Host=postgres-cluster;Port=5432;Database=xj_framework_itmctr;Username=postgres;Password=******;", "DatabaseType": "PostgreSQL"}, "Cors": {"Origins": ["*"]}, "Endpoint": [{"Name": "Rbac", "Url": "http://rbac-service:8080"}, {"Name": "RbacMgt", "Url": "http://rbac-mgt-service:8080"}, {"Name": "Messaging", "Url": "http://messaging-service:8080"}, {"Name": "MessagingMgt", "Url": "http://messaging-mgt-service:8080"}, {"Name": "DynamicForm", "Url": "http://dynamic-form-service:8080"}, {"Name": "DynamicFormMgt", "Url": "http://dynamic-form-mgt-service:8080"}, {"Name": "Files", "Url": "http://files-service:8080"}, {"Name": "FilesMgt", "Url": "http://files-mgt-service:8080"}, {"Name": "Logging", "Url": "http://logging-service:8080"}]}
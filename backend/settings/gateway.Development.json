{"Gateway": {"EnabledModules": ["rbac", "messaging", "dynamicform", "files", "logging", "itmctr"], "DeploymentMode": "Monolith", "ModuleConfigs": {"rbac": {"Name": "rbac", "Enabled": true, "Settings": {"EnableJwtAuth": true, "TokenExpirationMinutes": 60}, "Dependencies": []}, "messaging": {"Name": "messaging", "Enabled": true, "Settings": {"EnableSms": true, "EnableEmail": true}, "Dependencies": ["rbac"]}, "dynamicform": {"Name": "dynamicform", "Enabled": true, "Settings": {"EnableFormValidation": true, "MaxFormSize": 1048576}, "Dependencies": ["rbac"]}, "files": {"Name": "files", "Enabled": true, "Settings": {"MaxFileSize": 10485760, "AllowedExtensions": [".jpg", ".png", ".pdf", ".doc", ".docx"]}, "Dependencies": ["rbac"]}, "logging": {"Name": "logging", "Enabled": true, "Settings": {"LogLevel": "Information", "EnableDatabaseLogging": true}, "Dependencies": []}, "itmctr": {"Name": "itmctr", "Enabled": true, "Settings": {"EnableProjectTracking": true, "EnableReporting": true}, "Dependencies": ["rbac", "dynamicform", "files"]}}}, "ServiceCall": {"Mode": "Auto", "TimeoutMs": 30000, "RetryCount": 3, "EnableCache": false, "CacheExpirationSeconds": 300}, "Cors": {"Origins": ["*"]}, "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=xj_framework_dev;Username=postgres;Password=******;", "DatabaseType": "PostgreSQL"}}
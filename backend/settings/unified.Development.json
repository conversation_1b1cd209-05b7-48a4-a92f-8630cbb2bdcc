{"Modules": {"EnabledModules": {"Rbac": true, "RbacMgt": true, "Messaging": true, "MessagingMgt": true, "DynamicForm": true, "DynamicFormMgt": true, "Files": true, "FilesMgt": true, "Logging": true, "Itmctr": true, "ItmctrMgt": true}}, "ServiceProxy": {"DefaultCallMode": "Auto", "ServiceConfigs": {"IUserService": {"CallMode": "Local", "EnableCache": false}, "IMessageService": {"CallMode": "Local", "EnableCache": false}, "IFormService": {"CallMode": "Local", "EnableCache": false}, "IFileInfoService": {"CallMode": "Local", "EnableCache": false}}}, "Database": {"ConnectionString": "Host=localhost;Port=5432;Database=xj_framework_unified;Username=postgres;Password=******;", "DatabaseType": "PostgreSQL"}, "Cors": {"Origins": ["*"]}, "Endpoint": [{"Name": "Rbac", "Url": "http://localhost:5001"}, {"Name": "RbacMgt", "Url": "http://localhost:5002"}, {"Name": "Messaging", "Url": "http://localhost:5003"}, {"Name": "MessagingMgt", "Url": "http://localhost:5004"}, {"Name": "DynamicForm", "Url": "http://localhost:5005"}, {"Name": "DynamicFormMgt", "Url": "http://localhost:5006"}, {"Name": "Files", "Url": "http://localhost:5007"}, {"Name": "FilesMgt", "Url": "http://localhost:5008"}, {"Name": "Logging", "Url": "http://localhost:5009"}, {"Name": "Itmctr", "Url": "http://localhost:5010"}, {"Name": "ItmctrMgt", "Url": "http://localhost:5011"}]}
<Project>
  <PropertyGroup>
    <MsTestVersion>3.6.1</MsTestVersion>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="Humanizer.Core" Version="2.14.1" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore" Version="8.0.11" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Abstractions" Version="8.0.11" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.11" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Proxies" Version="8.0.11" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.11" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.11" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.17" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Caching.Memory" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Binder" Version="8.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Primitives" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Http" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.Json" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Configuration.UserSecrets" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Console" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.3" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageVersion Include="Microsoft.AspNetCore.OpenApi" Version="7.0.13" />
    <PackageVersion Include="SkiaSharp" Version="3.116.1" />
    <PackageVersion Include="SkiaSharp.NativeAssets.Linux.NoDependencies" Version="3.116.1" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.7.1" />
    <PackageVersion Include="Asp.Versioning.Mvc.ApiExplorer" Version="7.1.0" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageVersion Include="Microsoft.ApplicationInsights" Version="2.22.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.11.1" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="MSTest.TestAdapter" Version="$(MsTestVersion)" />
    <PackageVersion Include="MSTest.TestFramework" Version="$(MsTestVersion)" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.10" />
    <PackageVersion Include="Serilog.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageVersion Include="Serilog.AspNetCore" Version="8.0.3" />
    <PackageVersion Include="Serilog.Settings.Configuration" Version="8.0.4" />
    <PackageVersion Include="Serilog" Version="4.1.0" />
    <PackageVersion Include="Serilog.Sinks.Console" Version="6.0.0" />
    <PackageVersion Include="Serilog.Sinks.MSSqlServer" Version="6.3.0" />
    <PackageVersion Include="SharpZipLib" Version="1.4.2" />
    <PackageVersion Include="Shouldly" Version="4.2.1" />
    <PackageVersion Include="DotNetCore.NPOI" Version="1.2.3" />
    <PackageVersion Include="DotNetCore.NPOI.Core" Version="1.2.3" />
    <PackageVersion Include="StackExchange.Redis" Version="2.8.16" />
    <PackageVersion Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
    <PackageVersion Include="Npgsql" Version="9.0.3" />
    <PackageVersion Include="Scriban" Version="5.9.1" />
    <PackageVersion Include="Dapper" Version="2.1.28" />
    <PackageVersion Include="MySql.Data" Version="8.3.0" />
  </ItemGroup>
</Project>
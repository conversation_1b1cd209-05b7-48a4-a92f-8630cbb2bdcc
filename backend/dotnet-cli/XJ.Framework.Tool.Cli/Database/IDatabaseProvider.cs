using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;

namespace XJ.Framework.Tool.Cli.Database;

/// <summary>
/// 数据库提供者接口
/// </summary>
public interface IDatabaseProvider : IDisposable
{
    /// <summary>
    /// 创建数据库连接
    /// </summary>
    /// <returns>数据库连接</returns>
    IDbConnection CreateConnection();

    /// <summary>
    /// 获取所有表信息
    /// </summary>
    /// <param name="schema">数据库架构</param>
    /// <returns>表信息列表</returns>
    Task<IEnumerable<TableInfo>> GetTablesAsync(string schema);

    /// <summary>
    /// 获取表的列信息
    /// </summary>
    /// <param name="tableName">表名</param>
    /// <param name="schema">数据库架构</param>
    /// <returns>列信息列表</returns>
    Task<IEnumerable<ColumnInfo>> GetColumnsAsync(string tableName, string schema);
} 

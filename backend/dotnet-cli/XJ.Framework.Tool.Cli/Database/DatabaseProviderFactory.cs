using System;

namespace XJ.Framework.Tool.Cli.Database;

/// <summary>
/// 数据库提供者工厂
/// </summary>
public static class DatabaseProviderFactory
{
    /// <summary>
    /// 创建数据库提供者
    /// </summary>
    /// <param name="dbType">数据库类型</param>
    /// <param name="connectionString">连接字符串</param>
    /// <returns>数据库提供者</returns>
    public static IDatabaseProvider Create(string dbType, string connectionString)
    {
        return dbType.ToLower() switch
        {
            "sqlserver" => new SqlServerDatabaseProvider(connectionString),
            "postgresql" => new PostgreSqlDatabaseProvider(connectionString),
            "mysql" => new MySqlDatabaseProvider(connectionString),
            _ => throw new ArgumentException($"不支持的数据库类型: {dbType}")
        };
    }
} 

using System;
using System.CommandLine;
using System.CommandLine.Invocation;
using System.Threading.Tasks;

namespace XJ.Framework.Tool.Cli.Commands;

public class EntityCommand : Command
{
    private readonly Option<string> _connectionStringOption;
    private readonly Option<string> _dbTypeOption;
    private readonly Option<string> _schemaOption;
    private readonly Option<string> _domainNameOption;
    private readonly Option<string> _outputOption;
    private readonly Option<bool> _initialStructureOption;
    private readonly Option<bool> _includeMgtOption;
    private readonly Option<bool> _overrideOption;

    public EntityCommand() : base("entity", "Generate entity code from database")
    {
        _connectionStringOption = new Option<string>("--connection-string", "Database connection string") { IsRequired = true };
        _dbTypeOption = new Option<string>("--db-type", () => "sqlserver", "Database type (sqlserver/postgresql/mysql)");
        _schemaOption = new Option<string>("--schema", () => "dbo", "Database schema");
        _domainNameOption = new Option<string>("--domain-name", "Domain name for generated code") { IsRequired = true };
        _outputOption = new Option<string>("--output", () => ".", "Output directory for generated code");
        _initialStructureOption = new Option<bool>("--initial-structure", () => false, "Whether to generate initial structure");
        _includeMgtOption = new Option<bool>("--include-mgt", () => false, "Whether to include management project");
        _overrideOption = new Option<bool>("--override", () => false, "Whether to override existing files");

        AddOption(_connectionStringOption);
        AddOption(_dbTypeOption);
        AddOption(_schemaOption);
        AddOption(_domainNameOption);
        AddOption(_outputOption);
        AddOption(_initialStructureOption);
        AddOption(_includeMgtOption);
        AddOption(_overrideOption);

        this.SetHandler(async (InvocationContext context) =>
        {
            var connectionString = context.ParseResult.GetValueForOption(_connectionStringOption);
            var dbType = context.ParseResult.GetValueForOption(_dbTypeOption);
            var schema = context.ParseResult.GetValueForOption(_schemaOption);
            var domainName = context.ParseResult.GetValueForOption(_domainNameOption);
            var output = context.ParseResult.GetValueForOption(_outputOption);
            var initialStructure = context.ParseResult.GetValueForOption(_initialStructureOption);
            var includeMgt = context.ParseResult.GetValueForOption(_includeMgtOption);
            var @override = context.ParseResult.GetValueForOption(_overrideOption);

            await HandleCommand(connectionString!, dbType!, schema!, domainName!, output!, initialStructure, includeMgt, @override);
        });
    }

    private async Task<int> HandleCommand(string connectionString, string dbType, string schema, string domainName, string output, bool initialStructure, bool includeMgt, bool @override)
    {
        try
        {
            Console.WriteLine("开始生成代码...");
            Console.WriteLine($"连接字符串: {connectionString}");
            Console.WriteLine($"数据库类型: {dbType}");
            Console.WriteLine($"架构: {schema}");
            Console.WriteLine($"领域名称: {domainName}");
            Console.WriteLine($"输出目录: {output}");
            Console.WriteLine($"是否初始化结构: {initialStructure}");
            Console.WriteLine($"是否包含管理项目: {includeMgt}");
            Console.WriteLine($"是否覆盖已存在文件: {@override}");

            var generator = new CodeGenerator(connectionString, dbType, schema, domainName, output, initialStructure, includeMgt, @override);
            await generator.GenerateAsync();
            
            Console.WriteLine("代码生成完成！");
            return 0;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"生成代码时发生错误: {ex.Message}");
            return 1;
        }
    }
} 
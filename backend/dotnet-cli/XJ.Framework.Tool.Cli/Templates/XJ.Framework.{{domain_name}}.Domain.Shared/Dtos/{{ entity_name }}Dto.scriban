{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Domain.Shared.Dtos;

/// <summary>
/// {{ entity_name }} DTO
/// </summary>
public class {{ entity_name }}Dto : BaseDto<{{ key_type }}>
{
    {{~ for property in properties ~}}
    /// <summary>
    {{~ for line in property.comment | string.split '\n' ~}}
    /// {{ line }}
    {{~ end ~}}
    /// </summary>
    public {{ property.type }}{{ if property.nullable }}?{{ end }} {{ property.name }} { get; set; }{{ if !property.nullable && property.type == "string" }} = null!;{{ end }}

    {{~ end ~}}
} 
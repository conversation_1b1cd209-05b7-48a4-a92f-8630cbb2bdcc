{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Application.Mappers;

/// <summary>
/// {{ entity_name }} Profile
/// </summary>
public class {{ entity_name }}Profile : Profile
{
    public {{ entity_name }}Profile()
    {
        CreateMap<{{ entity_name }}Entity, {{ entity_name }}Dto>();
        CreateMap<{{ entity_name }}OperationDto, {{ entity_name }}Entity>();
        {{~ if has_audit_fields || has_soft_delete ~}}
        {{~ end ~}}
    }
} 
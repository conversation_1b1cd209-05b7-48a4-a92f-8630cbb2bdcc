<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.{{domain_name}}.Application.Contract\XJ.Framework.{{domain_name}}.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.{{domain_name}}.EntityFrameworkCore\XJ.Framework.{{domain_name}}.EntityFrameworkCore.csproj"/>
    </ItemGroup>

</Project> 
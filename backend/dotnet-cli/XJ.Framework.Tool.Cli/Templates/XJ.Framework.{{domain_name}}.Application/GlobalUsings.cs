
global using AutoMapper;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using XJ.Framework.Library.Application;
global using XJ.Framework.Library.Application.Services;
global using XJ.Framework.Library.Domain.UOW;
global using XJ.Framework.Library.Domain.Id;
global using XJ.Framework.Library.Domain.Shared.Interfaces;
global using XJ.Framework.{{domain_name}}.Application.Contract.OperationDtos;
global using XJ.Framework.{{domain_name}}.Domain.Entities;
global using XJ.Framework.{{domain_name}}.Domain.Shared.Dtos;
global using XJ.Framework.{{domain_name}}.Application.Contract.Interfaces;
global using XJ.Framework.{{domain_name}}.Application.Contract.QueryCriteria;
global using XJ.Framework.{{domain_name}}.Domain.Repositories.Interfaces;
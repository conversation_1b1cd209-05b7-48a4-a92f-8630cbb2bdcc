{{~ 
# 使用从 CodeGenerator 传入的变量
func check_audit_fields
    audit_fields = ["created_time", "created_by", "last_modified_time", "last_modified_by"]
    has_audit_fields = true
    
    for field in audit_fields
        field_found = false
        for column in columns
            if column.original_name == field
                field_found = true
                break
            end
        end
        if !field_found
            has_audit_fields = false
            break
        end
    end
    has_audit_fields
end

func check_soft_delete
    has_soft_delete = false
    for column in columns
        if column.original_name == "is_deleted"
            has_soft_delete = true
            break
        end
    end
    has_soft_delete
end

func is_entity_editable
    check_audit_fields || check_soft_delete
end
~}} 
<Project Sdk="Microsoft.NET.Sdk.Web">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi.Mgt\XJ.Framework.Library.WebApi.Mgt.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi\XJ.Framework.Library.WebApi.csproj"/>
        <ProjectReference Include="..\XJ.Framework.{{domain_name}}.Application\XJ.Framework.{{domain_name}}.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.{{domain_name}}.Domain.Shared\XJ.Framework.{{domain_name}}.Domain.Shared.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <Folder Include="Controllers\" />
    </ItemGroup>

</Project> 
{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Application.Contract.OperationDtos;

/// <summary>
/// {{ entity_name }} 操作 DTO
/// </summary>
public class {{ entity_name }}OperationDto : BaseOperationDto
{
    {{~ for property in properties ~}}
    /// <summary>
    {{~ for line in property.comment | string.split '\n' ~}}
    /// {{ line }}
    {{~ end ~}}
    /// </summary>
    public {{ property.type }}{{ if property.nullable }}?{{ end }} {{ property.name }} { get; set; }{{ if !property.nullable && property.type == "string" }} = null!;{{ end }}

    {{~ end ~}}
} 
<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.{{domain_name}}.Domain.Shared\XJ.Framework.{{domain_name}}.Domain.Shared.csproj"/>
        <ProjectReference Include="..\XJ.Framework.{{domain_name}}.Domain\XJ.Framework.{{domain_name}}.Domain.csproj"/>
    </ItemGroup>

</Project> 
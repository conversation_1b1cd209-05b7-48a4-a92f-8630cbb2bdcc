{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.WebApi.Controllers;

/// <summary>
/// {{ entity_name }} 控制器
/// </summary>
{{ if !include_mgt }}
public class {{ entity_name }}Controller : BaseEditableAppController<{{ key_type }}, {{ entity_name }}Dto, {{ entity_name }}OperationDto, I{{ entity_name }}Service, {{ entity_name }}QueryCriteria>
{
    public {{ entity_name }}Controller(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync({{ entity_name }}OperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:{{ key_type | string.downcase }}}")]
    public async Task<{{ entity_name }}Dto?> GetAsync({{ key_type }} id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:{{ key_type | string.downcase }}}")]
    public async Task<bool> UpdateAsync({{ key_type }} id, {{ entity_name }}OperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync({{ key_type }} id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<{{ key_type }}, {{ entity_name }}Dto>> GetPageAsync([FromQuery] PagedQueryCriteria<{{ entity_name }}QueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<{{ entity_name }}Dto>> GetListAsync([FromQuery] {{ entity_name }}QueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
{{ else }}
public class {{ entity_name }}Controller : BaseAppController<{{ key_type }}, {{ entity_name }}Dto, I{{ entity_name }}Service, {{ entity_name }}QueryCriteria>
{
    public {{ entity_name }}Controller(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    

    [HttpGet("{id:{{ key_type | string.downcase }}}")]
    public async Task<{{ entity_name }}Dto?> GetAsync({{ key_type }} id)
    {
        return await Service.GetByIdAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<{{ key_type }}, {{ entity_name }}Dto>> GetPageAsync([FromQuery] PagedQueryCriteria<{{ entity_name }}QueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<{{ entity_name }}Dto>> GetListAsync([FromQuery] {{ entity_name }}QueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
{{ end }}
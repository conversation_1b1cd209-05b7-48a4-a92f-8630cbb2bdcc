{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.EntityFrameworkCore.Repositories;

{{  if has_soft_delete }}
/// <summary>
/// {{ entity_name }} 仓储实现
/// </summary>
public class {{ entity_name }}Repository : BaseSoftDeleteRepository<{{ domain_name }}DbContext, {{ key_type }}, {{ entity_name }}Entity>, I{{ entity_name }}Repository
{
    public {{ entity_name }}Repository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}
{{ else if has_audit_fields }}
/// <summary>
/// {{ entity_name }} 仓储实现
/// </summary>
public class {{ entity_name }}Repository : BaseAuditRepository<{{ domain_name }}DbContext, {{ key_type }}, {{ entity_name }}Entity>, I{{ entity_name }}Repository
{
    public {{ entity_name }}Repository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}

{{ else }}
/// <summary>
/// {{ entity_name }} 仓储实现
/// </summary>
public class {{ entity_name }}Repository : BaseEditableRepository<{{ domain_name }}DbContext, {{ key_type }}, {{ entity_name }}Entity>, I{{ entity_name }}Repository
{
    public {{ entity_name }}Repository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}
{{ end }}
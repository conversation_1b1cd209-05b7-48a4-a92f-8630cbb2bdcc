<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>xj-tool</ToolCommandName>
    <PackageId>xj.framework.tool.cli</PackageId>
    <Version>1.0.0</Version>
    <Authors>XJ Framework</Authors>
    <Description>XJ Framework CLI Tool</Description>
    <PackageOutputPath>./nupkg</PackageOutputPath>
    <GeneratePackageOnBuild>true</GeneratePackageOnBuild>
    <Configurations>Debug;Release;Quickly</Configurations>
    <Platforms>AnyCPU</Platforms>
  </PropertyGroup>

  <PropertyGroup Condition=" '$(Configuration)' == 'Quickly' ">
    <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Humanizer.Core" />
    <PackageReference Include="Microsoft.Data.SqlClient" />
    <PackageReference Include="Npgsql" />
    <PackageReference Include="MySql.Data" />
    <PackageReference Include="Scriban" />
    <PackageReference Include="System.CommandLine" />
    <PackageReference Include="Dapper" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Templates\**\*.*">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <Pack>true</Pack>
    </None>
    <Compile Remove="Templates\**\*.cs" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Templates\XJ.Framework.{{domain_name}}.Domain\Repositories\" />
  </ItemGroup>

</Project> 
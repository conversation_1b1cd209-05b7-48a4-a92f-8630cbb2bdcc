#!/bin/bash

# 定义颜色
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查是否安装了 .NET SDK
if ! command -v dotnet &> /dev/null; then
    echo -e "${RED}错误: 未找到 .NET SDK${NC}"
    echo "请先安装 .NET SDK: https://dotnet.microsoft.com/download"
    exit 1
fi

# 获取脚本所在目录的绝对路径
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
CLI_PROJECT_DIR="$SCRIPT_DIR/XJ.Framework.Tool.Cli"

echo -e "${GREEN}开始安装 XJ Framework CLI 工具...${NC}"

# 检查项目目录是否存在
if [ ! -d "$CLI_PROJECT_DIR" ]; then
    echo -e "${RED}错误: 未找到项目目录 $CLI_PROJECT_DIR${NC}"
    exit 1
fi

# 进入项目目录
cd "$CLI_PROJECT_DIR"

# 清理之前的构建
echo "清理之前的构建..."
rm -rf bin obj nupkg

# 还原包
echo "还原 NuGet 包..."
dotnet restore
if [ $? -ne 0 ]; then
    echo -e "${RED}错误: 包还原失败${NC}"
    exit 1
fi

# 构建项目
echo "正在构建项目..."
dotnet build -c Release
if [ $? -ne 0 ]; then
    echo -e "${RED}错误: 项目构建失败${NC}"
    exit 1
fi

# 卸载已存在的工具（如果存在）
echo "检查是否存在旧版本..."
if dotnet tool list -g | grep -q "xj.framework.tool.cli"; then
    echo "正在卸载旧版本..."
    dotnet tool uninstall -g xj.framework.tool.cli
fi

# 安装工具
echo "正在安装工具..."
dotnet tool install --global --add-source ./nupkg xj.framework.tool.cli

if [ $? -eq 0 ]; then
    echo -e "${GREEN}安装成功！${NC}"
    echo "现在可以使用 'xj-tool' 命令来使用该工具"
    echo -e "${YELLOW}提示: 如果命令未找到，请确保 ~/.dotnet/tools 在您的 PATH 环境变量中${NC}"
else
    echo -e "${RED}安装失败${NC}"
    echo "请检查以下内容："
    echo "1. 是否有足够的权限"
    echo "2. .NET SDK 版本是否兼容"
    echo "3. NuGet 包是否正确生成"
    ls -la ./nupkg
    exit 1
fi 
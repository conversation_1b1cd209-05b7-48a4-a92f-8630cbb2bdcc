<Project>
    <PropertyGroup>
        <IsTestProject Condition="$(MSBuildProjectFullPath.Contains('tests')) and ($(MSBuildProjectName.EndsWith('.Tests')) or $(MSBuildProjectName.EndsWith('.Tests.Lib')))">true</IsTestProject>

        <IsPackable Condition="$(MSBuildProjectFullPath.Contains('tests')) and $(MSBuildProjectName.EndsWith('.Tests'))">false</IsPackable>

        <IsMgt Condition="$(MSBuildProjectName.EndsWith('.WebApi.Mgt'))">true</IsMgt>

        <IsWebApi Condition="($(MSBuildProjectName.EndsWith('.WebApi.Mgt')) or $(MSBuildProjectName.EndsWith('.WebApi'))) and $(MSBuildProjectFullPath.Contains('domains'))">true</IsWebApi>

        <IsEndpoint Condition="($(MSBuildProjectName.EndsWith('.WebApi.Mgt')) or $(MSBuildProjectName.EndsWith('.WebApi'))) and $(MSBuildProjectFullPath.Contains('domains'))">true</IsEndpoint>


        <IsQuickBuild Condition="'$(Configuration)' == 'Quickly'">true</IsQuickBuild>

    </PropertyGroup>

    <PropertyGroup>
        <Configurations>Debug;Release;Quickly</Configurations>
        <Platforms>AnyCPU</Platforms>
    </PropertyGroup>
    <PropertyGroup Condition=" '$(Configuration)' == 'Quickly' ">
        <WarningsAsErrors/>
        <Optimize Condition=" '$(Optimize)' == '' ">true</Optimize>
    </PropertyGroup>

    <ItemGroup>
        <Content Include="$(SolutionDir)/settings/**/*.json" CopyToOutputDirectory="Always" Link="settings/%(Filename)%(Extension)" Condition="'$(IsWebApi)' == 'true'"/>
    </ItemGroup>
    <!--    <Target Name="CopyAppSettings" BeforeTargets="BeforeBuild" Condition="'$(IsWebApi)' == 'true'">-->
    <!--        <Copy SourceFiles="$(SolutionDir)/settings/endpoint.Development.json" DestinationFolder="./" SkipUnchangedFiles="true" />-->
    <!--    </Target>-->


    <ItemGroup>
        <PackageReference Condition="'$(IsTestProject)' == 'true'" Include="coverlet.collector">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>

        <PackageReference Condition="'$(IsMgt)' == 'true'" Include="Microsoft.EntityFrameworkCore.Design">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
    </ItemGroup>

    <PropertyGroup Condition="'$(IsEndpoint)' == 'true' and '$(IsQuickBuild)' == 'true'">
        <PublishDir>$(SolutionDir)/publish/$(MSBuildProjectName)</PublishDir>
    </PropertyGroup>
</Project>

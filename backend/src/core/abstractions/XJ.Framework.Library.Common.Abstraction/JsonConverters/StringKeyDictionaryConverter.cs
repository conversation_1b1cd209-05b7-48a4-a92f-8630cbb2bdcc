using System.Text.Json;
using System.Text.Json.Serialization;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.JsonConverters;

public class StringKeyDictionaryConverter<TValue, TDictionary> : JsonConverter<TDictionary>
    where TDictionary : IDictionary<string, TValue>, new()
{
    private static readonly Dictionary<JsonValueKind, string> _tokenTypeToTypeDesp = new()
    {
        { JsonValueKind.String, typeof(string).AssemblyQualifiedName! },
        { JsonValueKind.True, typeof(bool).AssemblyQualifiedName! },
        { JsonValueKind.False, typeof(bool).AssemblyQualifiedName! }
    };

    public override TDictionary? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        TDictionary target = new();

        using (var doc = JsonDocument.ParseValue(ref reader))
        {
            foreach (var element in doc.RootElement.EnumerateArray())
            {
                var key = element.GetProperty("Key").GetString()!;

                var valueElement = element.GetProperty("Value");

                // 先按照TokenKind和 type 的映射进行初始化
                _tokenTypeToTypeDesp.TryGetValue(valueElement.ValueKind, out var typeDesp);

                // 如果有ValueType属性，就用ValueType属性的值来创建类型, 否则就用 string 类型
                if (element.TryGetProperty("ValueType", out var valueTypeElement))
                    typeDesp = valueTypeElement.GetString()!;

                var valueType = TypeCreator.GetTypeInfo(typeDesp!, false);

                var value = (TValue?)JsonSerializer.Deserialize(valueElement.GetRawText(), valueType, options)!;

                target.Add(key, value);
            }
        }

        return target;
    }

    public override void Write(Utf8JsonWriter writer, TDictionary value, JsonSerializerOptions options)
    {
        writer.WriteStartArray();

        foreach (var kv in value)
        {
            writer.WriteStartObject();

            writer.WriteString("Key", kv.Key);

            writer.WritePropertyName("Value");

            var valueType = typeof(TValue);

            if (kv.Value != null)
            {
                valueType = kv.Value.GetType();

                JsonSerializer.Serialize(writer, kv.Value, kv.Value.GetType(), options);
            }
            else
                JsonSerializer.Serialize(writer, kv.Value, options);

            // 如果是 string 或 bool 类型，就不写ValueType属性
            if (valueType != typeof(string) && valueType != typeof(bool))
                writer.WriteString("ValueType", valueType.AssemblyQualifiedName);

            writer.WriteEndObject();
        }

        writer.WriteEndArray();
    }
}
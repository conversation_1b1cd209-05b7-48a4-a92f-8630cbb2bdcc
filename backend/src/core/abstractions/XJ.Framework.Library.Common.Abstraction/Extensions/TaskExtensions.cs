using Exception = System.Exception;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class TaskExtensions
{
    public static Task Run(this Task task)
    {
        task.NullCheck(nameof(task));

        task.Start();

        return task;
    }

    public static Task AsTask(this Action action)
    {
        action.NullCheck(nameof(action));

        return new Task(action);
    }

    public static Task AsTask(this Action action, CancellationToken cancellationToken)
    {
        action.NullCheck(nameof(action));

        return new Task(action, cancellationToken);
    }

    public static Task AsSuppressTask(this Action action)
    {
        action.NullCheck(nameof(action));

        using (ExecutionContext.SuppressFlow())
            return new Task(action);
    }

    public static Task AsSuppressTask(this Action action, CancellationToken cancellationToken)
    {
        action.NullCheck(nameof(action));

        using (ExecutionContext.SuppressFlow())
            return new Task(action);
    }

    public static IEnumerable<Task> Start(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck(nameof(tasks));

        tasks.ForEach(task => task.Start());

        return tasks;
    }

    public static IEnumerable<Task> WaitAll(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck(nameof(tasks));

        Task.WaitAll(tasks.ToArray());

        return tasks;
    }

    public static IEnumerable<Task> WaitAny(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck(nameof(tasks));

        Task.WaitAny(tasks.ToArray());

        return tasks;
    }

    public static async Task WhenAll(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck(nameof(tasks));

        await Task.WhenAll(tasks.ToArray());
    }

    public static async Task WhenAny(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck(nameof(tasks));

        await Task.WhenAny(tasks.ToArray());
    }

    public static Thread SuppressStart(this Thread thread)
    {
        thread.NullCheck(nameof(thread));

        using (ExecutionContext.SuppressFlow())
            thread.Start();

        return thread;
    }

    /// <summary>
    /// 等待任何成功的的任务，并且获取某一个返回值。
    /// 如果全部失败，则抛出异常。异常信息是所有信息的集合。
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="tasks"></param>
    /// <returns></returns>
    public static async Task<T> WhenAnySuccessfulTask<T>(this IEnumerable<Task<T>> tasks)
    {
        tasks.NullCheck();

        IEnumerable<Task> remains = tasks;
        Task? resultTask = null;

        while (remains.Any())
        {
            var completed = await Task.WhenAny(remains);

            if (completed.IsCompletedSuccessfully == false)
            {
                remains = remains.FilterNotSuccessTasks(completed);
            }
            else
            {
                resultTask = completed;
                break;
            }
        }

        if (resultTask == null)
            tasks.CheckExceptions();

        return await (Task<T>)resultTask!;
    }

    public static void CheckExceptions(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck();

        List<Exception> exceptions = CollectExceptions(tasks);

        if (exceptions.Any())
        {
            // exceptions.ForEach(ex => Console.WriteLine(ex.Message));
            throw new AggregateException(exceptions);
        }

        if (tasks.Any(task => task.IsCanceled))
            throw new TaskCanceledException($"任务被中止了");
    }

    public static List<Exception> CollectExceptions(this IEnumerable<Task> tasks)
    {
        tasks.NullCheck();

        List<Exception> result = new();

        foreach (var task in tasks)
        {
            if (task.Exception != null)
                result.Add(task.Exception.GetRealException()!);
        }

        return result;
    }

    private static List<Task> FilterNotSuccessTasks(this IEnumerable<Task> tasks, Task exceptTask)
    {
        List<Task> result = new();

        foreach (var item in tasks)
        {
            if (item != exceptTask)
                result.Add(item);
        }

        return result;
    }
}
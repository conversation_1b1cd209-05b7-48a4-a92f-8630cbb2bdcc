using System.Collections.Specialized;
using System.Text;
using System.Web;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class UriExtensions
{
    /// <summary>
    /// 得到Url中不带参数的部分，也就是？左边的部分。
    /// 如果存在#home等 bookmark，则去掉参数，保留 bookmark
    /// </summary>
    /// <param name="uriString"></param>
    /// <returns></returns>
    public static string GetUrlWithoutParameters(this string? uriString)
    {
        var result = uriString;

        if (uriString.IsNotEmpty())
        {
            var bookmark = uriString!.GetBookmarkStringInUrl();

            var uriWithoutBookmark = uriString!;

            if (bookmark.IsNotEmpty())
                uriWithoutBookmark = uriString!.Substring(0, uriString.Length - bookmark.Length);

            var startIndex = uriWithoutBookmark.IndexOf("?");
            //int startIndex = uriString.IndexOf("?");

            if (startIndex >= 0)
                result = uriWithoutBookmark.Substring(0, startIndex);
            else
                result = uriWithoutBookmark;

            result += bookmark;
        }

        return result!;
    }

    /// <summary>
    /// 得到Url中不带参数的部分，也就是？左边的部分。
    /// 如果存在#home等 bookmark，则去掉参数，保留 bookmark
    /// </summary>
    /// <param name="url"></param>
    /// <returns></returns>
    public static Uri GetUrlWithoutParameters(this Uri url)
    {
        url.NullCheck();

        var result = url.ToString().GetUrlWithoutParameters();

        return new Uri(result, UriKind.RelativeOrAbsolute);
    }

    /// <summary>
    /// 字符串转换为uri，如果字符串为空串，返回null
    /// </summary>
    /// <param name="uriString"></param>
    /// <returns></returns>
    public static Uri? ToUri(this string uriString)
    {
        Uri? result = null;

        if (uriString.IsNotEmpty())
            result = new Uri(uriString, UriKind.RelativeOrAbsolute);

        return result;
    }

    #region CombineUrlParams

    /// <summary>
    /// 将参数重新组合成Url
    /// </summary>
    /// <param name="uri">url</param>
    /// <param name="encoding">字符编码，如果为null，表示不用Encode</param>
    /// <param name="requestParamsArray">参数集合的数组</param>
    /// <returns>补充了参数的url</returns>
    public static Uri CombineUrlParams(this Uri uri, Encoding? encoding,
        params NameValueCollection[] requestParamsArray)
    {
        uri.NullCheck();

        return new Uri(CombineUrlParams(uri.ToString(), encoding, requestParamsArray), UriKind.RelativeOrAbsolute);
    }

    /// <summary>
    /// 将参数重新组合成Url
    /// </summary>
    /// <param name="uri">url</param>
    /// <param name="requestParamsArray">参数集合的数组</param>
    /// <returns>补充了参数的url</returns>
    public static Uri CombineUrlParams(this Uri uri, params NameValueCollection[] requestParamsArray)
    {
        return CombineUrlParams(uri, Encoding.UTF8, requestParamsArray);
    }

    /// <summary>
    /// 将参数重新组合成Url
    /// </summary>
    /// <param name="uri">url</param>
    /// <param name="encodeParams">是否对参数进行Encode</param>
    /// <param name="requestParamsArray">参数集合的数组</param>
    /// <returns>补充了参数的url</returns>
    public static Uri CombineUrlParams(this Uri uri, bool encodeParams, params NameValueCollection[] requestParamsArray)
    {
        Uri? result = null;

        if (encodeParams)
            result = CombineUrlParams(uri, Encoding.UTF8, requestParamsArray);
        else
            result = CombineUrlParams(uri, null, requestParamsArray);

        return result;
    }

    /// <summary>
    /// 将参数重新组合成Url
    /// </summary>
    /// <param name="uriString">url</param>
    /// <param name="encoding">字符编码，如果为null，表示不用Encode</param>
    /// <param name="requestParamsArray">参数集合的数组</param>
    /// <returns>补充了参数的 url</returns>
    public static string CombineUrlParams(this string uriString, Encoding? encoding,
        params NameValueCollection[] requestParamsArray)
    {
        uriString.NullCheck();
        requestParamsArray.NullCheck();

        var requestParams = MergeParamsCollection(requestParamsArray);

        StringBuilder strB = new(1024);

        var leftPart = uriString.GetUrlWithoutParameters();
        var bookmark = leftPart.GetBookmarkStringInUrl();
        leftPart = leftPart.RemoveBookmarkStringInUrl();

        for (var i = 0; i < requestParams.Count; i++)
        {
            if (i == 0)
                strB.Append('?');
            else
                strB.Append('&');

            //徐磊修改  2012/3/5
            if (encoding == null)
            {
                strB.Append(requestParams.Keys[i]);
                strB.Append("=");
                strB.Append(requestParams[i]);
            }
            else
            {
                strB.Append(HttpUtility.UrlEncode(requestParams.Keys[i], encoding));
                strB.Append("=");
                strB.Append(HttpUtility.UrlEncode(requestParams[i], encoding));
            }
        }

        return leftPart + strB.ToString() + bookmark;
    }

    /// <summary>
    /// 将参数重新组合成Url
    /// </summary>
    /// <param name="uriString">url</param>
    /// <param name="requestParamsArray">参数集合的数组</param>
    /// <returns>补充了参数的url</returns>
    public static string CombineUrlParams(this string uriString, params NameValueCollection[] requestParamsArray)
    {
        return CombineUrlParams(uriString, Encoding.UTF8, requestParamsArray);
    }

    /// <summary>
    /// 是否需要Encode
    /// </summary>
    /// <param name="uriString"></param>
    /// <param name="encodeParams"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static string CombineUrlParams(this string uriString, bool encodeParams,
        params NameValueCollection[] requestParamsArray)
    {
        var result = string.Empty;

        if (encodeParams)
            result = CombineUrlParams(uriString, Encoding.UTF8, requestParamsArray);
        else
            result = CombineUrlParams(uriString, null, requestParamsArray);

        return result;
    }

    #endregion CombineUrlParams

    #region AppendUrlParams

    /// <summary>
    /// 在已经有的url后面添加参数集合
    /// </summary>
    /// <param name="uri"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static Uri AppendUrlParams(this Uri uri, params NameValueCollection[] requestParamsArray)
    {
        return AppendUrlParams(uri, Encoding.UTF8, requestParamsArray);
    }

    /// <summary>
    /// 在已经有的url后面添加参数集合
    /// </summary>
    /// <param name="uri"></param>
    /// <param name="encodeParams"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static Uri AppendUrlParams(this Uri uri, bool encodeParams, params NameValueCollection[] requestParamsArray)
    {
        Uri result;

        if (encodeParams)
            result = AppendUrlParams(uri, Encoding.UTF8, requestParamsArray);
        else
            result = AppendUrlParams(uri, null, requestParamsArray);

        return result;
    }

    /// <summary>
    /// 在已经有的url后面添加参数集合
    /// </summary>
    /// <param name="uri"></param>
    /// <param name="encoding"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static Uri AppendUrlParams(this Uri uri, Encoding? encoding, params NameValueCollection[] requestParamsArray)
    {
        uri.NullCheck();

        return new Uri(AppendUrlParams(uri.ToString(), encoding, requestParamsArray), UriKind.RelativeOrAbsolute);
    }

    /// <summary>
    /// 在已经有的url后面添加参数集合，默认使用utf8编码
    /// </summary>
    /// <param name="uriString"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static string AppendUrlParams(this string uriString, params NameValueCollection[] requestParamsArray)
    {
        return AppendUrlParams(uriString, Encoding.UTF8, requestParamsArray);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="uriString"></param>
    /// <param name="encodeParams"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static string AppendUrlParams(this string uriString, bool encodeParams,
        params NameValueCollection[] requestParamsArray)
    {
        var result = string.Empty;

        if (encodeParams)
            result = AppendUrlParams(uriString, Encoding.UTF8, requestParamsArray);
        else
            result = AppendUrlParams(uriString, null, requestParamsArray);

        return result;
    }

    /// <summary>
    /// 在已经有的url后面添加参数集合
    /// </summary>
    /// <param name="uriString"></param>
    /// <param name="encoding"></param>
    /// <param name="requestParamsArray"></param>
    /// <returns></returns>
    public static string AppendUrlParams(this string uriString, Encoding? encoding,
        params NameValueCollection[] requestParamsArray)
    {
        uriString.CheckStringIsNullOrEmpty();

        var parameters = MergeParamsCollection(requestParamsArray);

        var bookmark = GetBookmarkStringInUrl(uriString);
        var result = RemoveBookmarkStringInUrl(uriString);
        var encodeUrl = encoding != null;

        var paramString = parameters.ToUrlParameters(encodeUrl, encoding);

        if (result.IndexOf("?") >= 0)
            result += "&";
        else
            result += "?";

        result += paramString;

        if (bookmark.IsNotEmpty())
            result += bookmark;

        return result;
    }

    #endregion

    /// <summary>
    /// 得到 url 中的书签部分。“#”后面的部分
    /// </summary>
    /// <param name="queryString">http://localhost/lianhome#littleTurtle</param>
    /// <returns>littleTurtle</returns>
    public static string GetBookmarkStringInUrl(this string queryString)
    {
        queryString.NullCheck();

        var bookmarkStart = -1;
        for (var i = queryString.Length - 1; i >= 0; i--)
        {
            if (queryString[i] == '#')
            {
                bookmarkStart = i;
                break;
            }
        }

        var result = string.Empty;

        if (bookmarkStart >= 0)
            result = queryString.Substring(bookmarkStart);

        return result;
    }

    /// <summary>
    /// 删除url中的书签部分。“#”后面的部分
    /// </summary>
    /// <param name="queryString">http://localhost/lianhome#littleTurtle</param>
    /// <returns>littleTurtle</returns>
    public static string RemoveBookmarkStringInUrl(this string queryString)
    {
        var bookmark = GetBookmarkStringInUrl(queryString);

        var result = queryString;

        if (bookmark.IsNotEmpty())
            result = queryString.Remove(queryString.Length - bookmark.Length);

        return result;
    }

    private static NameValueCollection MergeParamsCollection(NameValueCollection[] requestParams)
    {
        NameValueCollection result = new(StringComparer.OrdinalIgnoreCase);

        for (var i = 0; i < requestParams.Length; i++)
            MergeTwoParamsCollection(result, requestParams[i]);

        return result;
    }

    private static void MergeTwoParamsCollection(NameValueCollection target, NameValueCollection src)
    {
        foreach (string key in src.Keys)
        {
            if (target[key] == null)
                target.Add(key, src[key]);
        }
    }
}
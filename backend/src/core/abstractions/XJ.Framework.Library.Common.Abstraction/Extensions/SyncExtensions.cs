namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class SyncExtensions
{
    public static void DoReadAction(this ReaderWriterLockSlim rwLock, Action action)
    {
        if (rwLock != null && action != null)
        {
            rwLock.EnterReadLock();

            try
            {
                action();
            }
            finally
            {
                rwLock.ExitReadLock();
            }
        }
    }

    public static R? DoReadFunc<R>(this ReaderWriterLockSlim rwLock, Func<R> func)
    {
        R? result = default;

        if (rwLock != null && func != null)
        {
            rwLock.EnterReadLock();

            try
            {
                result = func();
            }
            finally
            {
                rwLock.ExitReadLock();
            }
        }

        return result;
    }

    public static void DoWriteAction(this ReaderWriterLockSlim rwLock, Action action)
    {
        if (rwLock != null && action != null)
        {
            rwLock.EnterWriteLock();

            try
            {
                action();
            }
            finally
            {
                rwLock.ExitWriteLock();
            }
        }
    }

    public static R? DoWriteFunc<R>(this ReaderWriterLockSlim rwLock, Func<R> func)
    {
        R? result = default;

        if (rwLock != null && func != null)
        {
            rwLock.EnterWriteLock();

            try
            {
                result = func();
            }
            finally
            {
                rwLock.ExitWriteLock();
            }
        }

        return result;
    }

    public static void DoAction(this SemaphoreSlim semaphore, Action action,
        CancellationToken cancellationToken = default)
    {
        semaphore.NullCheck();
        action.NullCheck();

        semaphore.Wait(cancellationToken);

        try
        {
            action();
        }
        finally
        {
            semaphore.Release();
        }
    }

    public static async Task DoActionAsync(this SemaphoreSlim semaphore, Func<Task> action,
        CancellationToken cancellationToken = default)
    {
        semaphore.NullCheck();
        action.NullCheck();

        await semaphore.WaitAsync(cancellationToken);

        try
        {
            await action();
        }
        finally
        {
            semaphore.Release();
        }
    }

    public static R DoFunc<R>(this SemaphoreSlim semaphore, Func<R> func, CancellationToken cancellationToken = default)
    {
        semaphore.NullCheck();
        func.NullCheck();

        semaphore.Wait(cancellationToken);

        try
        {
            return func();
        }
        finally
        {
            semaphore.Release();
        }
    }

    public static async Task<R> DoFuncAsync<R>(this SemaphoreSlim semaphore, Func<Task<R>> func,
        CancellationToken cancellationToken = default)
    {
        semaphore.NullCheck();
        func.NullCheck();

        await semaphore.WaitAsync(cancellationToken);

        try
        {
            return await func();
        }
        finally
        {
            semaphore.Release();
        }
    }
}
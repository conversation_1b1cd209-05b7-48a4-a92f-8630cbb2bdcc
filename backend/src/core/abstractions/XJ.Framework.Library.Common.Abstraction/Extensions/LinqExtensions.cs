using System.Linq.Expressions;
using System.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class LinqExtensions
{
    private static PropertyInfo GetPropertyInfo(Type objType, string name)
    {
        var properties = objType.GetProperties();
        var matchedProperty = properties.FirstOrDefault(p => p.Name == name);
        if (matchedProperty == null)
        {
            throw new ArgumentException("name");
        }

        return matchedProperty;
    }

    private static LambdaExpression GetOrderExpression(Type objType, PropertyInfo pi)
    {
        var paramExpr = Expression.Parameter(objType);
        var propAccess = Expression.PropertyOrField(paramExpr, pi.Name);
        var expr = Expression.Lambda(propAccess, paramExpr);
        return expr;
    }

    /// <summary>
    /// 多个OrderBy用逗号隔开,属性前面带-号表示反序排序，exp:"name,-createtime"
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    public static IEnumerable<T> OrderByBatch<T>(this IEnumerable<T> query, string name)
    {
        var index = 0;
        var a = name.Split(',');
        foreach (var item in a)
        {
            var m = index++ > 0 ? "ThenBy" : "OrderBy";
            if (item.StartsWith("-"))
            {
                m += "Descending";
                name = item.Substring(1);
            }
            else
            {
                name = item;
            }

            name = name.Trim();

            var propInfo = GetPropertyInfo(typeof(T), name);
            var expr = GetOrderExpression(typeof(T), propInfo);
            var method = typeof(Enumerable).GetMethods()
                .FirstOrDefault(mt => mt.Name == m && mt.GetParameters().Length == 2);
            var genericMethod = method?.MakeGenericMethod(typeof(T), propInfo.PropertyType);
            query = (IEnumerable<T>)genericMethod?.Invoke(null, new object[] { query, expr.Compile() })!;
        }

        return query;
    }

    /// <summary>
    /// 多个OrderBy用逗号隔开,属性前面带-号表示反序排序，exp:"name,-createtime"
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    public static IQueryable<T> OrderByBatch<T>(this IQueryable<T> query, string name)
    {
        var index = 0;
        var a = name.Split(',');
        foreach (var item in a)
        {
            var m = index++ > 0 ? "ThenBy" : "OrderBy";
            if (item.StartsWith("-"))
            {
                m += "Descending";
                name = item.Substring(1);
            }
            else
            {
                name = item;
            }

            name = name.Trim();

            var propInfo = GetPropertyInfo(typeof(T), name);
            var expr = GetOrderExpression(typeof(T), propInfo);
            var method = typeof(Queryable).GetMethods()
                .FirstOrDefault(mt => mt.Name == m && mt.GetParameters().Length == 2);
            var genericMethod = method?.MakeGenericMethod(typeof(T), propInfo.PropertyType);
            query = (IQueryable<T>)genericMethod?.Invoke(null, new object[] { query, expr })!;
        }

        return query;
    }

    /// <summary>
    /// 正序排序单个
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    public static IQueryable<T> OrderBy<T>(this IQueryable<T> query, string name)
    {
        var propInfo = GetPropertyInfo(typeof(T), name);
        var expr = GetOrderExpression(typeof(T), propInfo);

        var method = typeof(Queryable).GetMethods()
            .FirstOrDefault(m => m.Name == "OrderBy" && m.GetParameters().Length == 2);
        var genericMethod = method?.MakeGenericMethod(typeof(T), propInfo.PropertyType);
        return (IQueryable<T>)genericMethod?.Invoke(null, new object[] { query, expr })!;
    }

    /// <summary>
    /// 正序排序单个（非首个）
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    public static IQueryable<T> ThenBy<T>(this IQueryable<T> query, string name)
    {
        var propInfo = GetPropertyInfo(typeof(T), name);
        var expr = GetOrderExpression(typeof(T), propInfo);

        var method = typeof(Queryable).GetMethods()
            .FirstOrDefault(m => m.Name == "ThenBy" && m.GetParameters().Length == 2);
        var genericMethod = method?.MakeGenericMethod(typeof(T), propInfo.PropertyType);
        return (IQueryable<T>)genericMethod?.Invoke(null, new object[] { query, expr })!;
    }

    /// <summary>
    /// 反序排序单个
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    public static IQueryable<T> OrderByDescending<T>(this IQueryable<T> query, string name)
    {
        var propInfo = GetPropertyInfo(typeof(T), name);
        var expr = GetOrderExpression(typeof(T), propInfo);
        var metMethods = typeof(Queryable).GetMethods();
        var method = metMethods.FirstOrDefault(m => m.Name == "OrderByDescending" && m.GetParameters().Length == 2);
        var genericMethod = method?.MakeGenericMethod(typeof(T), propInfo.PropertyType);
        return (IQueryable<T>)genericMethod?.Invoke(null, new object[] { query, expr })!;
    }

    /// <summary>
    /// 反序排序单个（非首个）
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <param name="name"></param>
    /// <returns></returns>
    public static IQueryable<T> ThenByDescending<T>(this IQueryable<T> query, string name)
    {
        var propInfo = GetPropertyInfo(typeof(T), name);
        var expr = GetOrderExpression(typeof(T), propInfo);
        var metMethods = typeof(Queryable).GetMethods();
        var method = metMethods.FirstOrDefault(m => m.Name == "ThenByDescending" && m.GetParameters().Length == 2);
        var genericMethod = method?.MakeGenericMethod(typeof(T), propInfo.PropertyType);
        return (IQueryable<T>)genericMethod?.Invoke(null, new object[] { query, expr })!;
    }
}
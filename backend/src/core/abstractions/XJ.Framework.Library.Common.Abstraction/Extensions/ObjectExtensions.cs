namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class ObjectExtensions
{
    /// <summary>
    /// 如果 data 不为空，则执行一个 action
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public static T? IsNotNull<T>(this T? data, Action<T> action)
    {
        if (data != null && action != null)
            action(data);

        return data;
    }

    /// <summary>
    /// 如果data不为空，则执行一个 async 的 action
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public static async Task<T?> IsNotNullAsync<T>(this T? data, Func<T, Task> action)
    {
        if (data != null && action != null)
            await action(data);

        return data;
    }

    /// <summary>
    /// 如果data不为空，则执行一个func，并返回func的值
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="R"></typeparam>
    /// <param name="data"></param>
    /// <param name="notNullFunc"></param>
    /// <returns></returns>
    public static R IsNotNull<T, R>(this T? data, Func<T, R> notNullFunc)
    {
        R? result = default;

        if (data != null && notNullFunc != null)
            result = notNullFunc(data);

        return result!;
    }

    /// <summary>
    /// 如果data不为空，则执行一个async的func，并返回func的值
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="R"></typeparam>
    /// <param name="data"></param>
    /// <param name="notNullFunc"></param>
    /// <returns></returns>
    public static async Task<R> IsNotNullAsync<T, R>(this T? data, Func<T, Task<R>> notNullFunc)
    {
        R? result = default;

        if (data != null && notNullFunc != null)
            result = await notNullFunc(data);

        return result!;
    }
}
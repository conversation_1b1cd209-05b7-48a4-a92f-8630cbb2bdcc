using XJ.Framework.Library.Common.Abstraction.Data.DataObjects;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

/// <summary>
/// TimeScope的扩展方法
/// </summary>
public static class TimeScopeExtensions
{
    /// <summary>
    /// 将时间范围的的开始和结束时间按照若干分钟对齐，然后拆成若干时间点
    /// </summary>
    /// <param name="scope"></param>
    /// <param name="sectionInMin"></param>
    /// <returns></returns>
    public static List<DateTimeOffset> SplitByMinutes(this TimeScope scope, int sectionInMin)
    {
        scope.NullCheck();

        List<DateTimeOffset> result = new();

        var start = scope.StartTime.RoundToMinute(sectionInMin);
        var end = scope.EndTime.RoundToMinute(sectionInMin);

        result.Add(start);

        while (start < end)
        {
            var next = start.AddMinutes(sectionInMin);

            if (next > end)
                next = end;

            result.Add(next);

            start = next;
        }

        return result;
    }

    /// <summary>
    /// 将时间范围的的开始和结束时间按照若干小时对齐，然后拆成若干时间点
    /// </summary>
    /// <param name="scope"></param>
    /// <param name="sectionInHour"></param>
    /// <returns></returns>
    public static List<DateTimeOffset> SplitByHours(this TimeScope scope, int sectionInHour)
    {
        scope.NullCheck();

        List<DateTimeOffset> result = new();

        var start = scope.StartTime.RoundToHour(sectionInHour);
        var end = scope.EndTime.RoundToHour(sectionInHour);

        result.Add(start);

        while (start < end)
        {
            var next = start.AddHours(sectionInHour);

            if (next > end)
                next = end;

            result.Add(next);

            start = next;
        }

        return result;
    }
}
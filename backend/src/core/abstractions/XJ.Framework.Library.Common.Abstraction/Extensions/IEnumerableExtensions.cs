using System.Collections;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class IEnumerableExtensions
{
    /// <summary>
    /// 判断集合中每个元素是否都满足某条件，且集合不为空
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="match"></param>
    /// <returns></returns>
    public static bool AllAndNotEmpty<T>(this IEnumerable<T> data, Predicate<T> match)
    {
        var result = true;
        var notEmpty = false;

        if (data != null && match != null)
        {
            foreach (var item in data)
            {
                notEmpty = true;

                if (match(item) == false)
                {
                    result = false;
                    break;
                }
            }
        }

        return result && notEmpty;
    }

    /// <summary>
    /// 枚举处理IEnumerable的内容
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public static IEnumerable<T> ForEach<T>(this IEnumerable<T> data, Action<T> action)
    {
        data.NullCheck();

        if (action != null)
        {
            foreach (var item in data)
                action(item);
        }

        return data;
    }

    public static IEnumerable<T> ForEach<T>(this IEnumerable<T> data, Action<int, T> action)
    {
        data.NullCheck();

        if (action != null)
        {
            var idx = 0;
            foreach (var item in data)
            {
                action(idx, item);
                idx++;
            }
        }

        return data;
    }

    /// <summary>
    /// 枚举异步处理IEnumerable的内容
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public async static Task<IEnumerable<T>> ForEachAsync<T>(this IEnumerable<T> data, Func<T, Task> action)
    {
        if (action != null)
        {
            foreach (var item in data)
                await action(item);
        }

        return data;
    }

    /// <summary>
    /// 枚举异步处理IEnumerable的内容
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public async static Task<IEnumerable<T>> ForEachAsync<T>(this IEnumerable<T> data, Func<int, T, Task> action)
    {
        if (action != null)
        {
            var idx = 0;
            foreach (var item in data)
            {
                await action(idx, item);
                idx++;
            }
        }

        return data;
    }

    public async static IAsyncEnumerable<TResult> SelectAsync<TSource, TResult>(this IEnumerable<TSource> source,
        Func<TSource, Task<TResult>> selector)
    {
        foreach (var item in source)
        {
            yield return await selector(item);
        }
    }

    /// <summary>
    /// 分页处理集合，以pageSize为批量处理
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="collection"></param>
    /// <param name="pageSize"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public static IEnumerable<T> Page<T>(this IEnumerable<T> collection, int pageSize, Action<IList<T>> action)
    {
        collection.NullCheck(nameof(collection));
        action.NullCheck(nameof(action));

        var count = 0;

        List<T> pagedData = [];

        collection.ForEach(item =>
        {
            if (count++ < pageSize)
            {
                pagedData.Add(item);
            }
            else
            {
                action(pagedData);

                pagedData.Clear();
                pagedData.Add(item);
                count = 1;
            }
        });

        if (pagedData.Count > 0)
            action(pagedData);


        return collection;
    }

    /// <summary>
    /// Async分页处理集合，以pageSize为批量处理
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="collection"></param>
    /// <param name="pageSize"></param>
    /// <param name="action"></param>
    /// <returns></returns>
    public static async Task<IEnumerable<T>> PageAsync<T>(this IEnumerable<T> collection, int pageSize,
        Func<IList<T>, Task> action)
    {
        collection.NullCheck(nameof(collection));
        action.NullCheck(nameof(action));

        var count = 0;

        List<T> pagedData = new();

        collection.ForEach(async item =>
        {
            if (count++ < pageSize)
            {
                pagedData.Add(item);
            }
            else
            {
                await action(pagedData);

                pagedData.Clear();
                pagedData.Add(item);
                count = 1;
            }
        });

        if (pagedData.Count > 0)
            await action(pagedData);


        return collection;
    }

    /// <summary>
    /// 枚举处理IEnumerable的内容
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="action"></param>
    public static void ForEach<T>(this IEnumerable data, Action<T> action)
    {
        if (data != null && action != null)
        {
            foreach (T item in data)
            {
                action(item);
            }
        }
    }

    /// <summary>
    /// 枚举每一项存在的值，判断是否存在满足条件的项
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="match"></param>
    /// <returns></returns>
    public static bool Exists<T>(this IEnumerable<T> data, Predicate<T> match)
    {
        var result = false;

        if (data != null && match != null)
        {
            foreach (var item in data)
            {
                if (match(item))
                {
                    result = true;
                    break;
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 枚举每一项存在的值，判断是否都不存在满足条件的项
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="match"></param>
    /// <returns></returns>
    public static bool NotExists<T>(this IEnumerable<T> data, Predicate<T> match)
    {
        return !Exists(data, match);
    }

    /// <summary>
    /// 枚举每一项存在的值，判断是否存在满足条件的项
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="match"></param>
    /// <returns></returns>
    public static bool Exists<T>(this IEnumerable data, Predicate<T> match)
    {
        var result = false;

        if (data != null && match != null)
        {
            foreach (T item in data)
            {
                if (match(item))
                {
                    result = true;
                    break;
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 枚举每一项存在的值，判断是否都不存在满足条件的项
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="match"></param>
    /// <returns></returns>
    public static bool NotExists<T>(this IEnumerable data, Predicate<T> match)
    {
        return !Exists(data, match);
    }

    /// <summary>
    /// 得到符合条件的第一项，如果没有找到，则返回默认值
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="match"></param>
    /// <returns></returns>
    public static T? FirstOrDefault<T>(this IEnumerable data, Predicate<T> match)
    {
        T? result = default;

        if (data != null && match != null)
        {
            foreach (T item in data)
            {
                if (match(item))
                {
                    result = item;
                    break;
                }
            }
        }

        return result;
    }

    /// <summary>
    /// 去除集合类中重复项
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="data"></param>
    /// <param name="comparer"></param>
    /// <returns></returns>
    public static IEnumerable<T> Distinct<T>(this IEnumerable<T> data, EqualityComparerHandler<T> comparer)
    {
        GeneralEqualityComparer<T> comparerClass = new GeneralEqualityComparer<T>(comparer);

        return Enumerable.Distinct(data, comparerClass);
    }

    /// <summary>
    /// 从IEnumerable复制到一个List中
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="target"></param>
    /// <param name="source"></param>
    /// <returns></returns>
    public static IList<T> CopyFrom<T>(this IList<T> target, IEnumerable<T> source)
    {
        if (target != null && source != null)
            source.ForEach(target.Add);

#pragma warning disable CS8603 // Possible null reference return.
        return target;
#pragma warning restore CS8603 // Possible null reference return.
    }

    /// <summary>
    /// 将IEnumerable复制到一个List中
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="source"></param>
    /// <param name="target"></param>
    /// <returns></returns>
    public static IEnumerable<T> CopyTo<T>(this IEnumerable<T> source, IList<T> target)
    {
        if (target != null && source != null)
            source.ForEach(target.Add);

#pragma warning disable CS8603 // Possible null reference return.
        return source;
#pragma warning restore CS8603 // Possible null reference return.
    }

    /// <summary>
    /// 在List中增加不为空的元素
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="target"></param>
    /// <param name="item"></param>
    public static void AddNotNull<T>(this IList<T> target, T? item)
    {
        if (item != null)
            target.Add(item);
    }
}

using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class StringExtensions
{
    #region NullOrEmpty的判断和处理

    /// <summary>
    /// 字符串不是Null且Empty
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static bool IsNotEmpty(this string? data)
    {
        var result = false;

        if (data != null)
            result = (string.IsNullOrEmpty(data) == false);

        return result;
    }

    /// <summary>
    /// 如果字符串不为空，则执行Action
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回传入的data</returns>
    public static string? IsNotEmpty(this string? data, Action<string> action)
    {
        if (data.IsNotEmpty() && action != null)
#pragma warning disable CS8604 // Possible null reference argument.
            action(data);
#pragma warning restore CS8604 // Possible null reference argument.

        return data;
    }

    /// <summary>
    /// 如果字符串不为空，则执行Func
    /// </summary>
    /// <typeparam name="R"></typeparam>
    /// <param name="data"></param>
    /// <param name="func"></param>
    /// <returns></returns>
    public static R? IsNotEmpty<R>(this string? data, Func<string, R> func)
    {
        R? result = default;

        if (data.IsNotEmpty() && func != null)
#pragma warning disable CS8604 // Possible null reference argument.
            result = func(data);
#pragma warning restore CS8604 // Possible null reference argument.

        return result;
    }

    /// <summary>
    /// 字符串是否为Null或Empty
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static bool IsNullOrEmpty(this string? data)
    {
        return string.IsNullOrEmpty(data);
    }

    /// <summary>
    /// 如果字符串为空，则返回替代的字符串
    /// </summary>
    /// <param name="data"></param>
    /// <param name="replacedData"></param>
    /// <returns></returns>
    public static string? NullOrEmptyIs(this string? data, string replacedData)
    {
        var result = data;

        if (result.IsNullOrEmpty())
            result = replacedData;

        return result;
    }

    /// <summary>
    /// 如果字符串为空，则执行Action
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回传入的data</returns>
    public static string? IsNullOrEmpty(this string? data, Action action)
    {
        if (string.IsNullOrEmpty(data) && action != null)
            action();

        return data;
    }

    /// <summary>
    /// 如果字符串为空，则执行Func
    /// </summary>
    /// <param name="data"></param>
    /// <param name="func"></param>
    /// <returns></returns>
    public static string? IsNullOrEmpty(this string? data, Func<string> func)
    {
        var result = data;

        if (string.IsNullOrEmpty(data) && func != null)
            result = func();

        return result;
    }

    /// <summary>
    /// 字符串是否为Null、Empty和WhiteSpace
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static bool IsNullOrWhiteSpace(this string data)
    {
        return string.IsNullOrWhiteSpace(data);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    /// <returns>返回传入的data</returns>
    public static string? IsNullOrWhiteSpace(this string? data, Action action)
    {
        if (string.IsNullOrWhiteSpace(data) && action != null)
            action();

        return data;
    }

    /// <summary>
    /// 字符串不是Null、Empty和WhiteSpace
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static bool IsNotWhiteSpace(this string? data)
    {
        var result = false;

        if (data != null)
            result = (string.IsNullOrWhiteSpace(data) == false);

        return result;
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="data"></param>
    /// <param name="action"></param>
    public static void IsNotWhiteSpace(this string? data, Action<string?> action)
    {
        if (data.IsNotWhiteSpace() && action != null)
            action(data);
    }

    #endregion NullOrEmpty的判断和处理

    #region Compare

    /// <summary>
    /// 大小写无关的比较
    /// </summary>
    /// <param name="strA"></param>
    /// <param name="strB"></param>
    /// <returns></returns>
    public static int IgnoreCaseCompare(this string strA, string strB)
    {
        return string.Compare(strA, strB, true);
    }

    /// <summary>
    /// 大小写无关的等于判断
    /// </summary>
    /// <param name="strA"></param>
    /// <param name="strB"></param>
    /// <returns></returns>
    public static bool IgnoreCaseEquals(this string strA, string strB)
    {
        return string.Compare(strA, strB, true) == 0;
    }

    #endregion Compare

    #region ToBase64Bytes

    /// <summary>
    /// 字节数组转换为base64的字符串。如果数组为null，则返回空串
    /// </summary>
    /// <param name="data"></param>
    /// <returns></returns>
    public static string ToBase64String(this byte[] data)
    {
        var result = string.Empty;

        if (data != null)
            result = Convert.ToBase64String(data);

        return result;
    }

    public static string ToHashString(this byte[] data)
    {
        // 使用SHA256哈希算法
        using var sha256 = SHA256.Create();
        // 计算哈希值
        var hashBytes = sha256.ComputeHash(data);
        // 将哈希值转换为十六进制字符串
        var hashString = BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
        return hashString;
    }

    public static string ToHashString(this Stream stream)
    {
        // 使用SHA256哈希算法
        using var sha256 = SHA256.Create();
        // 计算哈希值
        var hashBytes = sha256.ComputeHash(stream);
        // 将哈希值转换为十六进制字符串
        var hashString = BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
        return hashString;
    }

    /// <summary>
    /// 将byte数组转换为base16的字符串
    /// </summary>
    /// <param name="data">待转换的byte数组</param>
    /// <returns>转换好的16进制字符串</returns>
    public static string ToBase16String(this byte[] data)
    {
        StringBuilder strB = new();

        if (data != null)
        {
            for (var i = 0; i < data.Length; i++)
                strB.AppendFormat("{0:x2}", data[i]);
        }

        return strB.ToString();
    }

    /// <summary>
    /// 将保存好的16进制字符串转换为byte数组
    /// </summary>
    /// <param name="strData"></param>
    /// <returns></returns>
    public static byte[] ToBase16Bytes(this string strData)
    {
        var data = Array.Empty<byte>();

        if (strData != null)
        {
            data = new Byte[strData.Length / 2];

            for (var i = 0; i < strData.Length / 2; i++)
                data[i] = Convert.ToByte(strData.Substring(i * 2, 2), 16);
        }
        else
            data = new byte[0];

        return data;
    }

    #endregion ToBase64Bytes

    #region Append

    /// <summary>
    /// 
    /// </summary>
    /// <param name="strB"></param>
    /// <param name="data"></param>
    public static void AppendWithSplitChars(this StringBuilder strB, string data)
    {
        AppendWithSplitChars(strB, data, " ");
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="strB"></param>
    /// <param name="data"></param>
    /// <param name="splitChars"></param>
    public static void AppendWithSplitChars(this StringBuilder strB, string data, string splitChars)
    {
        if (data.IsNotEmpty())
        {
            if (strB.Length > 0 && splitChars.IsNotEmpty())
                strB.Append(splitChars);

            strB.Append(data);
        }
    }

    /// <summary>
    /// 在字符串后面添加路径分隔符
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    public static string AppendDirSeparator(this string source)
    {
        var result = source;

        if (source.IsNotEmpty())
        {
            result = source.TrimEnd();

            if (result[source.Length - 1] != Path.DirectorySeparatorChar)
            {
                result += Path.DirectorySeparatorChar;
            }
        }

        return result;
    }

    #endregion Append

    public static string CapitalizeFirstLetter(this string input)
    {
        if (string.IsNullOrEmpty(input)) return input;

        // 判断首字母是否为小写
        if (char.IsLower(input[0]))
        {
            // 将首字母转为大写
            return char.ToUpper(input[0]) + input.Substring(1);
        }

        return input;
    }
    
    /// <summary>
    /// 筛选出包含某些字符的字符换集合
    /// </summary>
    /// <param name="strings"></param>
    /// <param name="chars"></param>
    /// <returns></returns>
    public static List<string> FilterByChars(this IEnumerable<string> strings, string chars)
    {
        return FilterByChars(strings, chars.ToCharArray());
    }

    /// <summary>
    /// 筛选出包含某些字符的字符换集合
    /// </summary>
    /// <param name="strings"></param>
    /// <param name="chars"></param>
    /// <returns></returns>
    public static List<string> FilterByChars(this IEnumerable<string> strings, char[] chars)
    {
        strings.NullCheck();
        chars.NullCheck();

        HashSet<string> selected = new();

        foreach (var ch in chars)
        {
            foreach (var str in strings)
            {
                if (selected.Contains(str))
                    continue;

                if (str.Contains(ch, StringComparison.OrdinalIgnoreCase))
                    selected.Add(str);
            }
        }

        return selected.ToList();
    }

    public static string ToBase64String(this string data)
    {
        return Encoding.UTF8.GetBytes(data).ToBase64String();
    }

    public static async Task<byte[]> ToBytesAsync(this string data, Encoding encoding)
    {
        return await Task.FromResult(encoding.GetBytes(data));
    }

    public static Stream ToStream(this string content)
    {
        MemoryStream stream = new();

        StreamWriter writer = new(stream, Encoding.UTF8);

        writer.Write(content);
        writer.Flush();

        stream.Seek(0, SeekOrigin.Begin);

        return stream;
    }

    public static bool ContainsAnyKeywords(this string content, IEnumerable<string> keywords)
    {
        content.CheckStringIsNullOrEmpty();
        keywords.NullCheck();

        return keywords.Any(keyword => content.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    public static bool ContainsAllKeywords(this string content, IEnumerable<string> keywords)
    {
        content.CheckStringIsNullOrEmpty();
        keywords.NullCheck();

        return keywords.All(keyword => content.Contains(keyword, StringComparison.OrdinalIgnoreCase));
    }

    public static async Task<byte[]> Base64ToBytesAsync(this string input)
    {
        return await Task.FromResult(Convert.FromBase64String(input));
    }

    public static string ToMd5(this string input)
    {
        input.NullCheck();

        var inputBytes = Encoding.UTF8.GetBytes(input);
        var hashBytes = MD5.HashData(inputBytes);

        return Convert.ToHexString(hashBytes);
    }

    #region MatchWithAsterisk

    /// <summary>
    /// 某个字符串是否匹配通配符原则
    /// </summary>
    /// <param name="data"></param>
    /// <param name="pattern"></param>
    /// <returns></returns>
    public static bool MatchWithAsterisk(this string data, string pattern)
    {
        if (data.IsNullOrEmpty() || pattern.IsNullOrEmpty())
            return false;

        string[] ps = pattern.Split('*');

        if (ps.Length == 1) // 没有*的模型
            return MatchWithInterrogation(data, ps[0]);

        var si = data.IndexOf(ps[0], 0); // 从 string头查找第一个串

        if (si != 0)
            return false; // 第一个串没找到或者不在string的头部

        si += ps[0].Length; // 找到了串后,按就近原则,移到未查询过的最左边

        var plast = ps.Length - 1; // 最后一串应单独处理,为了提高效率,将它从循环中取出
        var pi = 0; // 跳过之前处理过的第一串

        while (++pi < plast)
        {
            if (ps[pi] == "")
                continue; //连续的*号,可以忽略

            si = data.IndexOf(ps[pi], si); // 继续下一串的查找

            if (-1 == si)
                return false; // 没有找到

            si += ps[pi].Length; // 就近原则
        }

        if (ps[plast] == "") // 模型尾部为*,说明所有有效字符串部分已全部匹配,string后面可以是任意字符
            return true;

        // 从尾部查询最后一串是否存在
        var last_index = data.LastIndexOf(ps[plast]);

        // 如果串存在,一定要在string的尾部, 并且不能越过已查询过部分
        return (last_index == data.Length - ps[plast].Length) && (last_index >= si);
    }

    private static bool MatchWithInterrogation(string data, string pattern)
    {
        var result = false;

        if (data.Length == pattern.Length)
            result = data.IndexOf(pattern) > -1;

        return result;
    }

    #endregion MatchWithAsterisk

    #region 模板中参数提取

    //private const string ParamPattern = @"\$\{[0-9 a-z A-Z]*?\}\$";
    private const string ParamPattern = @"\$\{(.+?)\}\$";

    /// <summary>
    /// 提取中模板中的参数，参数为${param}$格式
    /// </summary>
    /// <param name="template"></param>
    /// <returns></returns>
    public static HashSet<string> GetParameters(this string? template)
    {
        HashSet<string> result = [];

        if (template.IsNotEmpty())
        {
            Regex reg = new(ParamPattern);

            var matches = reg.Matches(template!);
            foreach (Match match in matches)
            {
                if (match.Success)
                {
                    var param = match.Groups[0].Value.Substring(2, match.Length - 4);

                    if (result.Contains(param) == false)
                        result.Add(param);
                }
            }
        }

        return result;
    }

    public static string ReplaceParameters(this string? template, IDictionary<string, string> parameters)
    {
        parameters.NullCheck();

        var result = string.Empty;

        if (template.IsNotEmpty())
        {
            Regex reg = new(ParamPattern);

            result = reg.Replace(template!, match =>
            {
                var paramName = match.Value.Substring(2, match.Length - 4);

                return parameters.GetValue(paramName, string.Empty);
            });
        }

        return result;
    }

    #endregion 模板中参数提取
}

using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class EnumExtensions
{
    private static readonly Dictionary<Type, EnumItemDescriptionList> innerDictionary = new();

    /// <summary>
    /// 转换为字符串转换为枚举类型
    /// </summary>
    /// <typeparam name="TEnum"></typeparam>
    /// <param name="value"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static TEnum ParseToEnum<TEnum>(this string value, TEnum defaultValue)
    {
        defaultValue.NullCheck();

        if (Enum.TryParse(typeof(TEnum), value, out var result) == false)
            result = defaultValue;

        return (TEnum)result!;
    }

    /// <summary>
    /// 获得已排序的枚举型的描述信息表
    /// </summary>
    /// <param name="enumType">枚举型</param>
    /// <returns>已排序的枚举型的描述信息表</returns>
    /// <remarks>得到已排序的枚举型的描述信息表，该表是根据SortID属性排序的。
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\EnumItemDescriptionAttributeTest.cs" region = "GetEnumItemDescriptionListTest" lang="cs" title="获得已排序的枚举项的描述信息表" />
    /// </remarks>
    public static EnumItemDescriptionList ToDescriptionList(this Type enumType)
    {
        enumType.NullCheck(nameof(enumType));
        enumType.IsEnum.FalseThrow<ArgumentException>("\"{0}\"不是枚举类型", enumType.FullName!);

        lock (EnumExtensions.innerDictionary)
        {
            EnumItemDescriptionList? result;

            if (EnumExtensions.innerDictionary.TryGetValue(enumType, out result) == false)
            {
                result = GetDescriptionListFromEnumType(enumType);
                EnumExtensions.innerDictionary.Add(enumType, result);
            }

            return result;
        }
    }

    /// <summary>
    /// 获得枚举项附加属性的描述信息属性
    /// </summary>
    /// <param name="enumItem">枚举项</param>
    /// <returns>描述信息属性，若该附加属性没有定义，则返回null</returns>
    /// <remarks>获得枚举项的附加属性，若该附加属性没有定义，则返回null
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\EnumItemDescriptionAttributeTest.cs" region = "GetAttributeTest" lang="cs" title="得到枚举项的描述信息属性" />
    /// </remarks>
    [System.Diagnostics.DebuggerNonUserCode]
    public static TAttribute? GetAttribute<TAttribute>(this System.Enum enumItem) where TAttribute : Attribute
    {
        var fi = enumItem.GetType().GetField(enumItem.ToString());

        TAttribute? attr = default;

        if (fi != null)
            attr = (TAttribute?)
                Attribute.GetCustomAttribute(fi, typeof(TAttribute));

        return attr;
    }

    /// <summary>
    /// 获得枚举项的描述信息值，若没有定义该附加属性，则返回空串
    /// </summary>
    /// <param name="enumItem">枚举项</param>
    /// <returns>枚举项的描述信息值，若没有定义该枚举项附加属性，则返回空串</returns>
    /// <remarks>获得枚举项的描述信息值，若没有定义该枚举项附加属性，则返回空串
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Core\EnumItemDescriptionAttributeTest.cs" region = "GetDescriptionTest" lang="cs" title="得到枚举项的描述信息属性" />
    /// </remarks>
    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetDescription(this System.Enum enumItem)
    {
        return enumItem.GetType()
            .ToDescriptionList()[enumItem].Description;
    }

    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetName(this System.Enum enumItem)
    {
        return enumItem.GetType()
            .ToDescriptionList()[enumItem].Name;
    }

    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetCurrentTopicAndDescription(this System.Enum enumItem)
    {
        return $"当前主题为\"{enumItem.GetName()}\"，功能为\"{enumItem.GetDescription()}\"";
    }

    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetShortName(this System.Enum enumItem)
    {
        return enumItem.GetType()
            .ToDescriptionList()[enumItem].ShortName;
    }

    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetGroupName(this System.Enum enumItem)
    {
        return enumItem.GetType()
            .ToDescriptionList()[enumItem].GroupName;
    }

    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetCategory(this System.Enum enumItem)
    {
        return enumItem.GetType()
            .ToDescriptionList()[enumItem].Category;
    }

    [System.Diagnostics.DebuggerNonUserCode]
    public static string GetEnumName(this System.Enum enumItem)
    {
        return enumItem.GetType()
            .ToDescriptionList()[enumItem].EnumName;
    }

    private static EnumItemDescriptionList GetDescriptionListFromEnumType(Type enumType)
    {
        List<EnumItemDescriptionAttribute> eidList = new();

        FieldInfo[] fields = enumType.GetFields();

        for (var i = 0; i < fields.Length; i++)
        {
            var fi = fields[i];

            if (fi.IsLiteral && fi.IsStatic)
                eidList.Add(fi.CreateFromFieldInfo(enumType));
        }

        eidList.Sort();

        return new EnumItemDescriptionList(eidList);
    }

    private static EnumItemDescriptionAttribute CreateFromFieldInfo(this FieldInfo fi, Type enumType)
    {
        var eid = (EnumItemDescriptionAttribute?)
            Attribute.GetCustomAttribute(fi, typeof(EnumItemDescriptionAttribute));

        if (eid == null)
            eid = new();

        var enumValue = fi.GetValue(enumType);

        if (enumValue != null && eid.EnumValue == int.MinValue)
            eid.EnumValue = (int)enumValue;

        eid.EnumName = fi.Name;

        if (eid.Name.IsNullOrEmpty())
            eid.Name = fi.Name;

        if (eid.SortId == int.MinValue)
            eid.SortId = eid.EnumValue;

        return eid;
    }
}
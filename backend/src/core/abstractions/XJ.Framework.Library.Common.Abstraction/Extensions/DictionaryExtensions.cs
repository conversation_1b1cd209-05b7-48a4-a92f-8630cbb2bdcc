using System.Collections;
using System.Collections.Specialized;
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Converters;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class DictionaryExtensions
{
    private static readonly SemaphoreSlim DictSemaphoreSlim = new(1, 1);
    private static readonly object SyncObject = new();

    /// <summary>
    /// 读取泛型字典中的某一项，如果不存在此项，返回缺省值
    /// </summary>
    /// <typeparam name="TKey">字典Key的类型</typeparam>
    /// <typeparam name="TValue">字典项的类型</typeparam>
    /// <typeparam name="TReturnValue">返回值的类型</typeparam>
    /// <param name="dict">泛型字典对象</param>
    /// <param name="key">需要访问的key值</param>
    /// <param name="defaultValue">如果不存在key时，返回的缺省值</param>
    /// <returns></returns>
    public static TReturnValue GetValue<TKey, TValue, TReturnValue>(this IDictionary<TKey, TValue>? dict, TKey key,
        TReturnValue defaultValue)
    {
        var result = defaultValue;

        if (dict != null)
        {
            TValue? oResult = default;

            if (dict.TryGetValue(key, out oResult))
                result = (TReturnValue)DataConverter.ChangeType(oResult, typeof(TReturnValue))!;
        }

        return result;
    }

    /// <summary>
    /// 根据Key获取Value，如过不存在，则通过回调获取。并且防止并发，为字典加锁。
    /// 需要注意的是，同一个字典的GetOrAddValueWithLock，不可同步和异步混用。
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dict"></param>
    /// <param name="key"></param>
    /// <param name="valueGetter"></param>
    /// <returns></returns>
    public static TValue GetOrAddValueWithLock<TKey, TValue>(this IDictionary<TKey, TValue> dict, TKey key,
        Func<TKey, TValue> valueGetter)
    {
        dict.NullCheck(nameof(dict));
        valueGetter.NullCheck(nameof(valueGetter));

        if (dict.TryGetValue(key, out var value) == false)
        {
            lock (SyncObject)
            {
                if (dict.TryGetValue(key, out value) == false)
                {
                    value = valueGetter(key);

                    dict.Add(key, value);
                }
            }
        }

        return value!;
    }

    /// <summary>
    /// 根据Key获取Value，如过不存在，则通过回调获取。并且防止并发，为字典加锁
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dict"></param>
    /// <param name="key"></param>
    /// <param name="valueGetter"></param>
    /// <returns></returns>
    public static async Task<TValue> GetOrAddValueWithLockAsync<TKey, TValue>(this IDictionary<TKey, TValue> dict,
        TKey key, Func<TKey, Task<TValue>> valueGetter)
    {
        dict.NullCheck(nameof(dict));
        valueGetter.NullCheck(nameof(valueGetter));

        if (dict.TryGetValue(key, out var value) == false)
        {
            await DictSemaphoreSlim.DoActionAsync(async () =>
            {
                if (dict.TryGetValue(key, out value) == false)
                {
                    value = await valueGetter(key);

                    dict.Add(key, value);
                }
            });
        }

        return value!;
    }

    /// <summary>
    /// 读取字典中的某一项，如果不存在此项，返回缺省值
    /// </summary>
    /// <typeparam name="TReturnValue">字典的返回类型</typeparam>
    /// <param name="dict">字典对象</param>
    /// <param name="key">需要访问的key值</param>
    /// <param name="defaultValue">如果不存在key时，返回的缺省值</param>
    /// <returns></returns>
    public static TReturnValue? GetValue<TReturnValue>(this IDictionary? dict, object key, TReturnValue defaultValue)
    {
        var result = defaultValue;

        if (dict != null)
        {
            if (dict.Contains(key))
                result = (TReturnValue?)DataConverter.ChangeType(dict[key], typeof(TReturnValue));
        }

        return result;
    }

    /// <summary>
    /// 读取字典中的某一项，如果为空，则返回缺省值
    /// </summary>
    /// <typeparam name="TReturnValue"></typeparam>
    /// <param name="dict"></param>
    /// <param name="key"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static TReturnValue? GetValue<TReturnValue>(this StringDictionary? dict, string key,
        TReturnValue defaultValue)
    {
        var result = defaultValue;

        if (dict != null)
        {
            if (dict[key] != null)
                result = (TReturnValue?)DataConverter.ChangeType(dict[key], typeof(TReturnValue));
        }

        return result;
    }

    /// <summary>
    /// 当添加的字典项不是该类型的缺省值时，则添加此项
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dict"></param>
    /// <param name="key"></param>
    /// <param name="data"></param>
    /// <returns>是否添加了该项</returns>
    public static bool AddNonDefaultValue<TKey, TValue>(this IDictionary<TKey, TValue> dict, TKey key, TValue data)
    {
        dict.NullCheck(nameof(dict));

        var added = false;

        if (data != null)
            if (data is string)
            {
                if (data.ToString() != string.Empty)
                {
                    dict.Add(key, data);
                    added = true;
                }
            }
            else
            {
                if (data.Equals(default(TValue)) == false)
                {
                    dict.Add(key, data);
                    added = true;
                }
            }

        return added;
    }

    /// <summary>
    /// 当添加的字典项不是指定的缺省值时，则添加此项
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dict"></param>
    /// <param name="key"></param>
    /// <param name="data"></param>
    /// <param name="defaultValue"></param>
    /// <returns>是否添加了该项</returns>
    public static bool AddNonDefaultValue<TKey, TValue>(this IDictionary<TKey, TValue> dict, TKey key, TValue data,
        TValue defaultValue)
    {
        dict.NullCheck(nameof(dict));

        var added = false;

        if (data != null)
        {
            if (data.Equals(defaultValue) == false)
            {
                dict.Add(key, data);
                added = true;
            }
        }
        else
        {
            if (defaultValue != null)
            {
                dict.Add(key, data);
                added = true;
            }
        }

        return added;
    }

    /// <summary>
    /// 如果字典中没有指定的key，则添加。否则忽略
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dict"></param>
    /// <param name="key"></param>
    /// <param name="data"></param>
    /// <returns></returns>
    public static IDictionary<TKey, TValue> AddNotExistsItem<TKey, TValue>(this IDictionary<TKey, TValue> dict,
        TKey key, TValue data)
    {
        dict.NullCheck("dict");

        if (dict.ContainsKey(key) == false)
            dict.Add(key, data);

        return dict;
    }

    /// <summary>
    /// 从一个字典合并到另一���字典，原来如果存在，则不增加
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="target"></param>
    /// <param name="source"></param>
    /// <returns></returns>
    public static IDictionary<TKey, TValue>? MergeFrom<TKey, TValue>(this IDictionary<TKey, TValue>? target,
        IDictionary<TKey, TValue> source)
    {
        if (target != null && source != null)
        {
            foreach (KeyValuePair<TKey, TValue> kp in source)
            {
                if (target.ContainsKey(kp.Key) == false)
                    target.Add(kp.Key, kp.Value);
            }
        }

        return target;
    }

    #region Dictionary转换为对象的属性

    /// <summary>
    /// 创建一个对象，将一个字典中的值赋予一个对象的属性中
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="dictionary"></param>
    /// <returns></returns>
    public static T ToObject<T>(this IDictionary<string, object?> dictionary) where T : new()
    {
        return dictionary.FillProperties(new T());
    }

    /// <summary>
    /// 将一个字典中的值赋予一个对象的属性中
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="dictionary"></param>
    /// <param name="graph"></param>
    /// <returns></returns>
    public static T FillProperties<T>(this IDictionary<string, object?> dictionary, T graph)
    {
        return dictionary.FillProperties(typeof(T).ToPropertyDictionary().Values, graph);
    }

    /// <summary>
    /// 根据属性集合，将一个字典中的值赋予一个对象的属性中
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="dictionary"></param>
    /// <param name="properties"></param>
    /// <param name="graph"></param>
    /// <returns></returns>
    public static T FillProperties<T>(this IDictionary<string, object?> dictionary,
        IEnumerable<PropertyInfo> properties, T graph)
    {
        dictionary.NullCheck(nameof(dictionary));
        properties.NullCheck(nameof(properties));
        graph.NullCheck(nameof(graph));

        foreach (var property in properties)
        {
            if (property.CanWrite)
            {
                if (dictionary.TryGetValue(property.Name, out var valueInDict))
                {
                    object? value = null;

                    if (valueInDict != null)
                    {
                        // 处理集合类型的转换
                        if (valueInDict is IEnumerable sourceCollection && 
                            property.PropertyType.IsGenericType && 
                            property.PropertyType.GetGenericTypeDefinition() == typeof(List<>))
                        {
                            // 获取目标集合的元素类型
                            var targetElementType = property.PropertyType.GetGenericArguments()[0];
                            // 创建目标类型的List
                            var targetList = (IList)Activator.CreateInstance(property.PropertyType)!;
                            
                            // 转换每个元素
                            foreach (var item in sourceCollection)
                            {
                                if (item != null)
                                {
                                    var convertedItem = DataConverter.ChangeType(item.GetType(), item, targetElementType);
                                    if (convertedItem != null)
                                    {
                                        targetList.Add(convertedItem);
                                    }
                                }
                            }
                            value = targetList;
                        }
                        else
                        {
                            value = DataConverter.ChangeType(valueInDict.GetType(), valueInDict, property.PropertyType);
                        }
                    }

                    property.SetValue(graph, value, null);
                }
            }
        }

        return graph;
    }

    /// <summary>
    /// 复制一个字典
    /// </summary>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <param name="dictionary"></param>
    /// <param name="source"></param>
    /// <returns></returns>
    public static IDictionary<TKey, TValue> CopyFrom<TKey, TValue>(this IDictionary<TKey, TValue> dictionary,
        IEnumerable<KeyValuePair<TKey, TValue>> source)
    {
        dictionary.NullCheck();
        source.NullCheck();

        foreach (KeyValuePair<TKey, TValue> kp in source)
            dictionary[kp.Key] = kp.Value;

        return dictionary;
    }

    #endregion Dictionary转换为对象的属性
}
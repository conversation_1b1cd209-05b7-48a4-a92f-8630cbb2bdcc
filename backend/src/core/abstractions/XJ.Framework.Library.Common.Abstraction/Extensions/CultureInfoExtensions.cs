using System.Globalization;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class CultureInfoExtensions
{
    public static string GetCultureInfoCode(this CultureInfo cultureInfo, bool replaceDash = false)
    {
        // return cultureInfo.TwoLetterISOLanguageName;
        var cultureName = cultureInfo.Name;
        if (replaceDash)
            cultureName = cultureName.Replace("-", "_");
        return cultureName;
    }

    public static CultureInfo GetCultureInfo(this string cultureInfoCode)
    {
        return new CultureInfo(cultureInfoCode);
    }

    public static string GetTranslatedSuffix(this CultureInfo cultureInfo)
    {
        return "译文";
    }

    public static string GetTranslateBlocksSuffix(this CultureInfo cultureInfo)
    {
        return "译文对照表";
    }
}
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class MemberInfoExtensions
{
    private static readonly Dictionary<Type, Dictionary<string, PropertyInfo>>
        PropertiesWithNonPublicDictionary = new();

    private static readonly Dictionary<Type, Dictionary<string, PropertyInfo>> FlattenHierarchyPropertiesDictionary =
        new();

    private static readonly Dictionary<Type, Dictionary<string, PropertyInfo>> PropertyDictionary = new();
    private static readonly Dictionary<Type, object?> TypeDefaultValueDictionary = new();

    public static void ThrowInvalidMemberInfoTypeException(this MemberInfo mi)
    {
        throw new InvalidOperationException(string.Format("非法的成员类型{0},{1}",
            mi.Name,
            mi.MemberType));
    }

    /// <summary>
    /// 得到某个类型的默认值
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static object? GetDefaultValue(this System.Type type)
    {
        type.NullCheck(nameof(type));

        return TypeDefaultValueDictionary.GetOrAddValueWithLock(type, typeInCache =>
        {
            object? result = null;

            if (typeInCache.IsClass == false)
                result = TypeCreator.CreateInstance(typeInCache);

            return result;
        });
    }

    /// <summary>
    /// 得到包含非公共实例属性的的字典(Instance | Public | NonPublic)
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static Dictionary<string, PropertyInfo> ToPropertiesWithNonPublicDictionary(this System.Type type)
    {
        return PropertiesWithNonPublicDictionary.GetOrAddValueWithLock(type,
            key => key.ToPropertyDictionary(key => key.GetPropertiesWithNonPublic()));
    }

    /// <summary>
    /// 得到包含非公共实例属性的的字典(Instance | Public以及基类的属性)
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static Dictionary<string, PropertyInfo> ToFlattenHierarchyPropertiesDictionary(this System.Type type)
    {
        return FlattenHierarchyPropertiesDictionary.GetOrAddValueWithLock(type,
            key => key.ToPropertyDictionary(key => key.GetFlattenProperties()));
    }

    /// <summary>
    /// 得到默认的属性字典
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static Dictionary<string, PropertyInfo> ToPropertyDictionary(this System.Type type)
    {
        return PropertyDictionary.GetOrAddValueWithLock(type,
            key => key.ToPropertyDictionary(key => key.GetProperties()));
    }

    private static Dictionary<string, PropertyInfo> ToPropertyDictionary(this System.Type type,
        Func<Type, PropertyInfo[]> propertiesGetter)
    {
        type.NullCheck(nameof(type));
        propertiesGetter.NullCheck(nameof(propertiesGetter));

        Dictionary<string, PropertyInfo> newItem = new Dictionary<string, PropertyInfo>();

        PropertyInfo[] pis = propertiesGetter(type);

        foreach (var pi in pis)
        {
            if (newItem.ContainsKey(pi.Name) == false)
                newItem.Add(pi.Name, pi);
        }

        return newItem;
    }

    private static PropertyInfo[] GetPropertiesWithNonPublic(this Type type)
    {
        return type.GetProperties(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic);
    }

    private static PropertyInfo[] GetFlattenProperties(this Type type)
    {
        return type.GetProperties(BindingFlags.Instance | BindingFlags.Public | BindingFlags.FlattenHierarchy);
    }

    private static PropertyInfo[] GetProperties(this Type type)
    {
        return type.GetProperties();
    }
}
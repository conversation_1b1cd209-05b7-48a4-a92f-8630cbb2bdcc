namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class StreamExtensions
{
    public static bool IsNull(this Stream? stream)
    {
        return stream == null || stream == Stream.Null;
    }

    /// <summary>
    /// 异步堆区所有字符串
    /// </summary>
    /// <param name="stream"></param>
    /// <returns></returns>
    public static async Task<string> ReadToEndAsync(this Stream stream)
    {
        var result = string.Empty;

        using (StreamReader reader = new(stream))
        {
            result = await reader.ReadToEndAsync();
        }

        return result;
    }

    public static byte[] ToBytes(this Stream stream)
    {
        stream.NullCheck();

        using MemoryStream ms = new(2048);

        stream.CopyTo(ms);

        return ms.ToArray();
    }

    public static async Task<byte[]> ToBytesAsync(this Stream stream, bool seek = true)
    {
        if (seek) stream.Seek(0, SeekOrigin.Begin);

        var bytes = new byte[stream.Length];

        var ret = await stream.ReadAsync(bytes, 0, bytes.Length);

        // 设置当前流的位置为流的开始 
        if (seek) stream.Seek(0, SeekOrigin.Begin);
        return bytes;
    }

    public static async Task<MemoryStream> ToMemoryStreamAsync(this FileStream fileStream)
    {
        fileStream.NullCheck();

        var data = new byte[fileStream.Length];

        await fileStream.ReadAsync(data, 0, data.Length);

        //实例化一个内存流--->把从文件流中读取的内容[字节数组]放到内存流中去
        return await Task.FromResult(new MemoryStream(data));
    }

    public static async Task SaveToFileAsync(this Stream stream, string destinationFileName, bool overwrite)
    {
        ArgumentNullException.ThrowIfNull(stream);
        ArgumentNullException.ThrowIfNull(destinationFileName);

        FileStreamOptions fileStreamOptions = new()
        {
            Access = FileAccess.Write,
            Mode = overwrite ? FileMode.Create : FileMode.CreateNew,
            Share = FileShare.None,
            BufferSize = 0x1000
        };

        //var directoryPath = destinationFileName.Substring(0, destinationFileName.LastIndexOf('/'));
        var directoryPath = Path.GetDirectoryName(destinationFileName);

        if (!Directory.Exists(directoryPath))
            Directory.CreateDirectory(directoryPath!);

        stream.Seek(0, SeekOrigin.Begin);

        await using var fs = new FileStream(destinationFileName, fileStreamOptions);

        await stream.CopyToAsync(fs);
    }

    public static async Task<string> ToBase64StringAsync(this Stream stream)
    {
        var bytes = new byte[stream.Length];

        var ret = await stream.ReadAsync(bytes, 0, bytes.Length);

        return Convert.ToBase64String(bytes);
    }
}
using XJ.Framework.Library.Common.Abstraction.Exceptions;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

/// <summary>
/// 不可重试的校验异常
/// </summary>
public class ValidationException : NonTransientException
{
    /// <summary>
    /// ValidationException 的缺省构造函数
    /// </summary>
    /// <remarks>ValidationException的缺省构造函数.
    /// </remarks>
    public ValidationException()
    {
    }

    /// <summary>
    /// ValidationException 的带错误消息参数的构造函数
    /// </summary>
    /// <param name="strMessage">错误消息串</param>
    /// <remarks>ValidationException 的带错误消息参数的构造函数,该错误消息将在消息抛出异常时显示出来。
    /// </remarks>
    public ValidationException(string strMessage)
        : base(strMessage)
    {
    }

    /// <summary>
    /// ValidationException 的构造函数。
    /// </summary>
    /// <param name="strMessage">错误消息串</param>
    /// <param name="ex">导致该异常的异常</param>
    /// <remarks>该构造函数把导致该异常的异常记录了下来。
    /// </remarks>
    public ValidationException(string strMessage, Exception? ex)
        : base(strMessage, ex)
    {
    }
}
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.Validation.Validators;

namespace XJ.Framework.Library.Common.Abstraction.Validation;

internal class MetadataValidatorBuilder
{
    private static readonly Dictionary<string, List<Validator>> RuleSetValidators = new();

    public static readonly MetadataValidatorBuilder Instance = new MetadataValidatorBuilder();

    private MetadataValidatorBuilder()
    {
    }

    public Validator CreateValidator(Type type, string[] ruleset, List<string>? unValidates)
    {
        List<Validator> totalValidators = new List<Validator>();
        string[] localRuleset = ruleset;

        if (ruleset == null || ruleset.Length == 0)
            localRuleset = new string[] { string.Empty };

        foreach (var rulesetItem in localRuleset)
        {
            var cacheKey = $"{type.FullName}-{rulesetItem}";

            List<Validator> list = RuleSetValidators.GetOrAddValueWithLock(cacheKey, key =>
            {
                List<Validator> innerList = new();

                GetValidatorsFromType(type, rulesetItem, innerList);
                GetValidatorsFromProperties(type, rulesetItem, innerList);
                GetValidatorsFromFields(type, rulesetItem, innerList);

                return innerList;
            });

            list.ForEach(v => totalValidators.Add(v));
        }

        List<Validator> clonedValidators = new List<Validator>();

        //排除不需要验证的Validator（根据属性名称）
        foreach (var v in totalValidators)
        {
            if (unValidates == null || unValidates.Exists(s => s == v.Source) == false)
                clonedValidators.Add(v);
        }

        return new AndCompositeValidator(clonedValidators);
    }

    #region GetValidatorsFromProperties

    private void GetValidatorsFromProperties(Type type, string rulesetItem, List<Validator> list)
    {
        PropertyInfo[] pInfos = type.GetProperties(BindingFlags.Instance | BindingFlags.Public);

        foreach (var pi in pInfos)
        {
            FillValidatorsFromProperty(pi, rulesetItem, list);
        }
    }

    private void FillValidatorsFromProperty(PropertyInfo info, string rulesetItem, List<Validator> list)
    {
        var builder = GetCompositeValidatorBuilder(info, rulesetItem);

        foreach (ValidatorAttribute attr in info.GetCustomAttributes(typeof(ValidatorAttribute), true))
        {
            if (attr.ContainsRuleset(rulesetItem) && info.CanRead)
            {
                var valueValidator = new ValueAccessValidator(
                    new PropertyValueAccess(info),
                    attr.CreateValidator(info.PropertyType, info.ReflectedType!));

                builder.AddValueValidator(valueValidator);
            }
        }

        if (builder.GetCompositeValidatorsCount() != 0)
        {
            var result = builder.GetValidator();
            result.Source = info.Name;
            list.Add(result);
        }
    }

    #endregion

    #region GetValidatorsFromFields

    private void GetValidatorsFromFields(Type type, string rulesetItem, List<Validator> list)
    {
        FieldInfo[] fieldInfo = type.GetFields(BindingFlags.Instance | BindingFlags.Public);

        foreach (var info in fieldInfo)
        {
            FillValidatorsFromField(info, rulesetItem, list);
        }
    }

    private void FillValidatorsFromField(FieldInfo info, string rulesetItem, List<Validator> list)
    {
        if (rulesetItem == null)
            throw new ArgumentNullException("rulesetItem");

        var builder = GetCompositeValidatorBuilder(info, rulesetItem);

        foreach (ValidatorAttribute attr in info.GetCustomAttributes(typeof(ValidatorAttribute), true))
        {
            if (attr.ContainsRuleset(rulesetItem))
            {
                var valueValidator = new ValueAccessValidator(
                    new FieldValueAccess(info),
                    attr.CreateValidator(info.FieldType, info.ReflectedType!));

                builder.AddValueValidator(valueValidator);
            }
        }

        if (builder.GetCompositeValidatorsCount() != 0)
        {
            var result = builder.GetValidator();
            result.Source = info.Name;
            list.Add(result);
        }
    }

    #endregion

    #region GetValidatorsFromType

    private void GetValidatorsFromType(Type type, string rulesetItem, List<Validator> list)
    {
        var builder = GetCompositeValidatorBuilder(type, rulesetItem);

        foreach (ValidatorAttribute attr in type.GetCustomAttributes(typeof(ValidatorAttribute), true))
        {
            if (attr.ContainsRuleset(rulesetItem))
            {
                builder.AddValueValidator(attr.CreateValidator(type, type.ReflectedType!));
            }
        }

        if (builder.GetCompositeValidatorsCount() != 0)
        {
            var result = builder.GetValidator();
            result.Source = type.FullName!;
            list.Add(result);
        }
    }

    #endregion

    private CompositeValidatorBuilder GetCompositeValidatorBuilder(MemberInfo info, string rulesetItem)
    {
        CompositeValidatorBuilder? builder = null;

        foreach (ValidatorCompositionAttribute attr in info.GetCustomAttributes(typeof(ValidatorCompositionAttribute),
                     true))
        {
            if (attr.ContainsRuleset(rulesetItem))
            {
                builder = new CompositeValidatorBuilder(attr.CompositionType, attr.MessageTemplate);
                break;
            }
        }

        builder ??= new CompositeValidatorBuilder(CompositionType.And);

        return builder;
    }
}
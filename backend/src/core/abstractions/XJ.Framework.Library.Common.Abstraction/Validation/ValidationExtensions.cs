using System.Text;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Validation;

public static class ValidationExtensions
{
    /// <summary>
    /// 将校验结果转换为字符串，中间以splitChars分割
    /// </summary>
    /// <param name="results"></param>
    /// <param name="splitChars"></param>
    /// <returns></returns>
    public static string ToDescription(this ValidationResults results, string splitChars)
    {
        if (splitChars == null)
            splitChars = string.Empty;

        StringBuilder strB = new();

        if (results != null)
        {
            foreach (ValidationResult result in results)
            {
                if (strB.Length > 0)
                    strB.Append(splitChars);

                var message = result.Message;

                /*
                if (result.Category.IsNotEmpty())
                    message = Translator.Translate(result.Category, result.Message);
                */

                strB.Append(message);
            }
        }

        return strB.ToString();
    }

    /// <summary>
    /// 将校验结果转换为字符串
    /// </summary>
    /// <param name="results"></param>
    /// <returns></returns>
    public static string ToDescription(this ValidationResults results)
    {
        return results.ToDescription("\n");
    }

    /// <summary>
    /// 根据Validator的属性得到数据的校验结果
    /// </summary>
    /// <param name="data"></param>
    /// <param name="ruleset"></param>
    /// <returns></returns>
    public static ValidationResults GetValidateResults(this object data, params string[] ruleset)
    {
        data.NullCheck();

        var validator = ValidationFactory.CreateValidator(data.GetType(), ruleset);

        return validator.Validate(data);
    }

    /// <summary>
    /// 根据Validator的属性得到数据的校验结果，如果有不满足校验的地方，则抛出DeluxeValidationException，并且包含错误信息
    /// </summary>
    /// <param name="data"></param>
    /// <param name="ruleset"></param>
    public static void Validate(this object data, params string[] ruleset)
    {
        var result = data.GetValidateResults(ruleset);

        (result.ResultCount > 0).TrueThrow<ValidationException>(result.ToDescription());
    }
}
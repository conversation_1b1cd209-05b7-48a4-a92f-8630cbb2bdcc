using System.Collections;

namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 集合对象为空的校验
/// </summary>
internal class ObjectCollectionEmptyValidator : Validator
{
    private string targetRuleset = string.Empty;

    /// <summary>
    /// 无参数构造函数
    /// </summary>
    public ObjectCollectionEmptyValidator()
    {
    }

    /// <summary>
    /// ObjectCollectionValidator的构造函数
    /// </summary>
    /// <param name="targetRuleset">所校验类型所属的校验规则集</param>
    public ObjectCollectionEmptyValidator(string targetRuleset)
    {
        this.targetRuleset = targetRuleset;
    }

    public override void DoValidate(object? objectToValidate, object currentObject, string? key,
        ValidationResults validateResults)
    {
        var invalid = false;

        if (objectToValidate != null)
        {
            var collection = objectToValidate as ICollection;

            if (collection != null && collection.Count == 0)
                invalid = true;
        }
        else
            invalid = true;

        if (invalid)
            RecordValidationResult(validateResults, this.MessageTemplate, currentObject, key);
    }
}
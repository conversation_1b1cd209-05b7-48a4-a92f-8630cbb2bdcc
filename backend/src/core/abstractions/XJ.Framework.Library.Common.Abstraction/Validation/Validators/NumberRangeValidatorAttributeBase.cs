namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 数字值范围判断校验器属性类
/// </summary>
[AttributeUsage(AttributeTargets.Property
                | AttributeTargets.Field,
    AllowMultiple = true,
    Inherited = false)]
public abstract class NumberRangeValidatorAttributeBase<T> : ValidatorAttribute where T : IComparable<T>
{
    private readonly T lowerBound;
    private readonly T upperBound;

    /// <summary>
    /// 整形值下限
    /// </summary>
    public virtual T LowerBound {
        get {
            return lowerBound;
        }
    }

    /// <summary>
    /// 整形值上限
    /// </summary>
    public virtual T UpperBound {
        get { return upperBound; }
    }

    /// <summary>
    /// IntegerRangeValidatorAttribute构造函数
    /// </summary>
    /// <param name="lowerBound">整形值下限</param>
    /// <param name="upperBound">整形值上限</param>
    /// <remarks>
    /// <code  source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Validation\HelperClass\Student.cs" region="IntegerRangeValidatorAttributeUsage" lang="cs" title="如何添加整形范围校验器属性"  ></code>
    /// </remarks>
    public NumberRangeValidatorAttributeBase(T lowerBound, T upperBound)
    {
        this.lowerBound = lowerBound;
        this.upperBound = upperBound;
    }
}
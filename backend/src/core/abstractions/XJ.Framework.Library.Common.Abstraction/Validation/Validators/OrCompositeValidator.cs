namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

internal class OrCompositeValidator : Validator
{
    private IEnumerable<Validator> validators;

    public OrCompositeValidator(params Validator[] validators)
        : this(string.Empty, validators)
    {
    }

    public OrCompositeValidator(string messageTemplate, params Validator[] validators)
        : base(messageTemplate)
    {
        this.validators = validators;
    }

    public override void DoValidate(
        object? objectToValidate,
        object currentObject,
        string? key,
        ValidationResults validateResults)
    {
        List<ValidationResult> childrenValidationResults = new List<ValidationResult>();

        foreach (var validator in this.validators)
        {
            var childValidationResults = new ValidationResults();

            validator.DoValidate(objectToValidate, currentObject, key, childValidationResults);

            if (childValidationResults.IsValid())
                return;

            childrenValidationResults.AddRange(childValidationResults);
        }

        RecordValidationResult(validateResults, this.MessageTemplate, currentObject, key, childrenValidationResults);
    }
}
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 日期类型的为空判断的校验器
/// </summary>
public class DateTimeEmptyValidator : Validator
{
    private delegate bool TypeFilterFunc(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults);

    private List<TypeFilterFunc> _TypeFilter = new List<TypeFilterFunc>();

    /// <summary>
    /// 构造函数
    /// </summary>
    public DateTimeEmptyValidator()
    {
        this.InitFilters();
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="messageTemplate"></param>
    /// <param name="tag"></param>
    public DateTimeEmptyValidator(string messageTemplate, string tag)
        : base(messageTemplate, tag)
    {
        this.InitFilters();
    }

    private void InitFilters()
    {
        this._TypeFilter.Add(this.ValidateNullProperty);
        this._TypeFilter.Add(this.ValidateDateTimeProperty);
        this._TypeFilter.Add(this.ValidateNullableDateTimeProperty);
        this._TypeFilter.Add(this.ValidateDateTimeOffsetProperty);
        this._TypeFilter.Add(this.ValidateNullableDateTimeOffsetProperty);
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="objectToValidate">被校验的属性值</param>
    /// <param name="currentObject">属性所属于的对象值</param>
    /// <param name="key"></param>
    /// <param name="validationResults"></param>
    public override void DoValidate(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults)
    {
        foreach (var filter in this._TypeFilter)
        {
            if (filter(objectToValidate, currentObject, key, validationResults))
                break;
        }
    }

    private bool ValidateNullProperty(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults)
    {
        var processed = false;

        if (objectToValidate == null)
        {
            this.RecordValidationResult(validationResults, this.MessageTemplate, currentObject, key);

            processed = true;
        }

        return processed;
    }

    private bool ValidateDateTimeProperty(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults)
    {
        var processed = false;

        if (objectToValidate is DateTime)
        {
            ((DateTime)objectToValidate).IsMinValue(() =>
                this.RecordValidationResult(validationResults, this.MessageTemplate, currentObject, key));

            processed = true;
        }

        return processed;
    }

    private bool ValidateNullableDateTimeProperty(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults)
    {
        var processed = false;

        (objectToValidate as Nullable<DateTime>).IsNotNull(ndt =>
        {
            ndt.IsNullOrDefault(() =>
                this.RecordValidationResult(validationResults, this.MessageTemplate, currentObject, key)
            );
            processed = true;
        });

        return processed;
    }

    private bool ValidateDateTimeOffsetProperty(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults)
    {
        var processed = false;

        if (objectToValidate is DateTimeOffset)
        {
            ((DateTimeOffset)objectToValidate).IsMinValue(() =>
                this.RecordValidationResult(validationResults, this.MessageTemplate, currentObject, key));

            processed = true;
        }

        return processed;
    }

    private bool ValidateNullableDateTimeOffsetProperty(object? objectToValidate, object currentObject, string? key,
        ValidationResults validationResults)
    {
        var processed = false;

        (objectToValidate as Nullable<DateTime>).IsNotNull(ndt =>
        {
            ndt.IsNullOrDefault(() =>
                this.RecordValidationResult(validationResults, this.MessageTemplate, currentObject, key)
            );
            processed = true;
        });

        return processed;
    }
}
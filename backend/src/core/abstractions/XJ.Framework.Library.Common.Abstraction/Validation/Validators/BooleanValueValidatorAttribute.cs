namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 布尔值的校验器
/// </summary>
[AttributeUsage(AttributeTargets.Property
                | AttributeTargets.Field,
    AllowMultiple = true,
    Inherited = false)]
public class BooleanValueValidatorAttribute : ValidatorAttribute
{
    /// <summary>
    /// 构造方法。默认TargetValue为true
    /// </summary>
    public BooleanValueValidatorAttribute()
    {
        this.TargetValue = true;
    }

    /// <summary>
    /// 构造方法
    /// </summary>
    /// <param name="targetValue">需要满足的目标值</param>
    public BooleanValueValidatorAttribute(bool targetValue)
    {
        this.TargetValue = targetValue;
    }

    /// <summary>
    /// 需要校验的目标值，不等于目标值，则校验不通过
    /// </summary>
    public bool TargetValue {
        get;
        set;
    }

    /// <summary>
    /// 创建校验器
    /// </summary>
    /// <param name="targetType"></param>
    /// <returns></returns>
    protected override Validator DoCreateValidator(Type targetType)
    {
        return new BooleanValueValidator(this.MessageTemplate, this.TargetValue, this.Tag);
    }
}
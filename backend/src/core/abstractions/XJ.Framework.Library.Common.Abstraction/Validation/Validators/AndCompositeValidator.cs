using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

public class AndCompositeValidator : Validator
{
    private readonly IEnumerable<Validator> validators;

    public AndCompositeValidator(params Validator[] validators)
        : base(string.Empty, string.Empty)
    {
        validators.NullCheck(nameof(validators));

        this.validators = validators;
    }

    public AndCompositeValidator(IEnumerable<Validator> validators)
        : base(string.Empty, string.Empty)
    {
        this.validators = validators;
    }

    public override void DoValidate(object? objectToValidate, object currentObject,
        string? key, ValidationResults validateResults)
    {
        foreach (var validator in this.validators)
        {
            validator.DoValidate(objectToValidate, currentObject, key, validateResults);
        }
    }
}
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Validation.Validators;

/// <summary>
/// 枚举类型是否是缺省值的校验器（如果是，则相当于为空，报出错误）
/// </summary>
public class EnumDefaultValueValidator : Validator
{
    /// <summary>
    /// 无参数构造函数
    /// </summary>
    public EnumDefaultValueValidator()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="messageTemplate"></param>
    /// <param name="tag"></param>
    public EnumDefaultValueValidator(string messageTemplate, string tag) :
        base(messageTemplate, tag)
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="objectToValidate"></param>
    /// <param name="currentObject"></param>
    /// <param name="key"></param>
    /// <param name="validateResults"></param>
    public override void DoValidate(object? objectToValidate, object currentObject, string? key,
        ValidationResults validateResults)
    {
        var isValid = true;

        if (objectToValidate != null && objectToValidate.GetType().IsEnum)
        {
            var defaultData = objectToValidate.GetType().GetDefaultValue();

            isValid = objectToValidate.Equals(defaultData) == false;
        }

        if (isValid == false)
            RecordValidationResult(validateResults, this.MessageTemplate, currentObject, key);
    }
}
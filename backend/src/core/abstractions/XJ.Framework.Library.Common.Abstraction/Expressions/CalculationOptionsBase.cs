namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 计算时的请求参数
/// </summary>
public abstract class CalculationOptionsBase
{
    public CalculationOptionsBase(object? callerContext = null, bool optimize = true)
    {
        this.CallerContext = callerContext;
        this.Optimize = optimize;
    }

    public object? CallerContext {
        get;
    }

    /// <summary>
    /// 计算时是否进行逻辑表达式优化（与运算时，一个为 false 则整个表达式为 false；或运算时，一个为 true 则整个表达式为 true）
    /// </summary>
    public bool Optimize {
        get;
    }

    public ExpressionDictionaryCollection ExpressionDictionaries {
        get;
        set;
    } = [];
}
using System.Collections;
using XJ.Framework.Library.Common.Abstraction.Converters;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.Properties;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

internal sealed partial class ExpTreeExecutor
{
    private static readonly object[] EmptyObjectArray = new object[0];

    private delegate object? BuiltInFunctionDelegate(ExpTreeNode funcNode, IReadOnlyList<ExpTreeNode> arrParams,
        CalculateContext calcContext);

    private delegate object? ParalyzedBuiltInFunctionDelegate(ParamObjectCollection arrParams,
        CalculateContext calcContext);

    /// <summary>
    /// 这里面的函数，不一定每个参数都解析完成，只要碰到符合条件的就停止
    /// </summary>
    private static readonly Dictionary<string, BuiltInFunctionDelegate> BuiltInFunctionMap =
        new(StringComparer.OrdinalIgnoreCase)
        {
            { "in", DoInFunction },
            { "iif", DoIIFFunction },
            {
                "contains",
                (funcNode, arrParams, calcContext) => DoContainsFunction(funcNode, arrParams, calcContext, false)
            },
            {
                "containsci",
                (funcNode, arrParams, calcContext) => DoContainsFunction(funcNode, arrParams, calcContext, true)
            }
        };

    /// <summary>
    /// 这里面的函数，每个参数都会被解析到
    /// </summary>
    private static readonly Dictionary<string, ParalyzedBuiltInFunctionDelegate> ParalyzedBuiltInFunctionMap =
        new(StringComparer.OrdinalIgnoreCase)
        {
            { "now", (arrParams, calcContext) => DateTime.Now },
            { "today", (arrParams, calcContext) => DateTime.Today },
            { "dateinterval.day", (arrParams, calcContext) => "d" },
            { "dateinterval.hour", (arrParams, calcContext) => "h" },
            { "dateinterval.minute", (arrParams, calcContext) => "n" },
            { "dateinterval.second", (arrParams, calcContext) => "s" },
            { "dateinterval.millisecond", (arrParams, calcContext) => "ms" },
            { "datediff", DoDateDiff },
            { "mindate", (arrParams, calcContext) => DateTime.MinValue },
            { "maxdate", (arrParams, calcContext) => DateTime.MaxValue },
            { "addDays", AddDays },
            { "addMonths", AddMonths },
            { "addYears", AddYears },

            { "sum", DoSum },
            { "max", DoMax },
            { "min", DoMin },
            { "array", DoArray },
            { "array.indexof", DoArrayIndexOf },
            { "array.sub", DoArraySub },
            { "array.subArray", DoArraySub },
            { "subArray", DoArraySub }
        };

    private static object? DoBuiltInFunctions(ExpTreeNode node, List<ExpTreeNode> arrParams,
        CalculateContext calcContext)
    {
        try
        {
            var oValue = DoNotParalyzedBuiltInFunctions(node, node.Params, calcContext);

            if (oValue == null)
            {
                var funcParams = GetParams(node.Params, calcContext);

                oValue = DoParalyzedBuiltInFunctions(node.FunctionName, funcParams, calcContext);

                if (oValue == null)
                    oValue = calcContext.GetUserFunctionValue(node.FunctionName, funcParams);

                if (oValue == null)
                    oValue = CalculateExpressionDictionary(node.FunctionName, funcParams, calcContext);
            }

            return oValue;
        }
        catch (ParsingException)
        {
            throw;
        }
        catch (System.Exception ex)
        {
            throw new SystemSupportException(string.Format(ExpressionParserRes.FunctionError, node.FunctionName,
                ex.Message));
        }
    }

    private static object? DoNotParalyzedBuiltInFunctions(ExpTreeNode funcNode, List<ExpTreeNode> arrParams,
        CalculateContext calcContext)
    {
        object? result = null;

        if (BuiltInFunctionMap.TryGetValue(funcNode.FunctionName, out var func))
            result = func(funcNode, arrParams, calcContext);

        return result;
    }

    private static object? DoParalyzedBuiltInFunctions(string funcName, ParamObjectCollection arrParams,
        CalculateContext calcContext)
    {
        object? result = null;

        if (ParalyzedBuiltInFunctionMap.TryGetValue(funcName, out var func))
            result = func(arrParams, calcContext);

        return result;
    }

    #region Not Paramlized BuiltIn Functions

    private static object DoInFunction(ExpTreeNode funcNode, IReadOnlyList<ExpTreeNode> arrParams,
        CalculateContext calcContext)
    {
        var result = false;

        if (arrParams.Count > 0)
        {
            var sourceData = VExp(arrParams[0], calcContext);

            if (sourceData != null)
            {
                for (var i = 1; i < arrParams.Count; i++)
                {
                    var itemValue = VExp(arrParams[i], calcContext);

                    if (itemValue != null)
                    {
                        if ((bool)CompareEqualOP(sourceData, itemValue, 0))
                        {
                            result = true;
                            break;
                        }
                    }
                }
            }
        }

        return result;
    }

    private static object DoContainsFunction(ExpTreeNode funcNode, IReadOnlyList<ExpTreeNode> arrParams,
        CalculateContext calcContext, bool ignoreCase)
    {
        var result = false;

        if (arrParams.Count > 0)
        {
            var sourceData = VExp(arrParams[0], calcContext);

            if (sourceData != null)
            {
                for (var i = 1; i < arrParams.Count; i++)
                {
                    var itemValue = VExp(arrParams[i], calcContext);

                    if (itemValue != null)
                    {
                        if ((bool)ContainsOP(sourceData, itemValue, ignoreCase))
                        {
                            result = true;
                            break;
                        }
                    }
                }
            }
        }

        return result;
    }

    private static object? DoIIFFunction(ExpTreeNode funcNode, IReadOnlyList<ExpTreeNode> arrParams,
        CalculateContext calcContext)
    {
        if (arrParams.Count != 3)
            throw ParsingException.NewParsingException(ParseError.peInvalidParam,
                funcNode.Position, funcNode.FunctionName, "3");
        object? result = false;

        if (arrParams.Count > 0)
        {
            var sourceData = VExp(arrParams[0], calcContext);

            if (sourceData != null)
            {
                var sourceCondition = (bool)DataConverter.ChangeType(sourceData, typeof(bool))!;

                if (sourceCondition)
                    result = VExp(arrParams[1], calcContext);
                else
                    result = VExp(arrParams[2], calcContext);
            }
        }

        return result;
    }

    #endregion Not Paramlized BuiltIn Functions

    #region Paramlized BuiltIn Functions

    private static object AddYears(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        arrParams.CheckParamsLength(2);
        arrParams[0].CheckParameterType<DateTime>();
        arrParams[1].CheckParameterType<decimal>();

        return ((DateTime)(arrParams[0].Value!)).AddYears((int)NToD(arrParams[1].Value))!;
    }

    private static object AddMonths(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        arrParams.CheckParamsLength(2);
        arrParams[0].CheckParameterType<DateTime>();
        arrParams[1].CheckParameterType<decimal>();

        return ((DateTime)(arrParams[0].Value!)).AddMonths((int)NToD(arrParams[1].Value));
    }

    private static object AddDays(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        arrParams.CheckParamsLength(2);
        arrParams[0].CheckParameterType<DateTime>();
        arrParams[1].CheckParameterType<decimal>();

        return ((DateTime)(arrParams[0].Value!)).AddDays((int)NToD(arrParams[1].Value));
    }

    private static object DoDateDiff(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        arrParams.CheckParamsLength(3);
        arrParams[0].CheckParameterType<string>();
        arrParams[1].CheckParameterType<DateTime>();
        arrParams[2].CheckParameterType<DateTime>();

        var startTime = (DateTime)arrParams[1].Value!;
        var endTime = (DateTime)arrParams[2].Value!;

        var ts = endTime - startTime;

        double result = 0;

        var intervalType = arrParams[0].Value!.ToString()!.ToLower();

        switch (intervalType)
        {
            case "d":
                result = ts.TotalDays;
                break;
            case "h":
                result = ts.TotalHours;
                break;
            case "n":
                result = ts.TotalMinutes;
                break;
            case "s":
                result = ts.TotalSeconds;
                break;
            case "ms":
                result = ts.TotalMilliseconds;
                break;
            default:
                throw new SystemSupportException(string.Format(ExpressionParserRes.InvalidDateDiffType, intervalType));
        }

        return Math.Ceiling(Convert.ToDecimal(result));
    }

    #region DoMax

    private static object DoMax(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        var result = decimal.MinValue;
        var dealed = false;

        result = DoArrayMax(arrParams, calcContext, ref dealed);

        if (dealed == false)
            result = DoDictionaryMax(arrParams, calcContext, ref dealed);

        if (dealed == false)
            result = DoParamsArrayMax(arrParams, calcContext);

        return result;
    }

    private static decimal DoArrayMax(ParamObjectCollection arrParams, CalculateContext calcContext, ref bool dealed)
    {
        var result = decimal.MinValue;

        if (arrParams.Count == 1 && arrParams[0].Value is string == false && arrParams[0].Value is IList)
        {
            foreach (var value in (IList)(arrParams[0].Value!))
                result = Math.Max(result, NToD(value));

            dealed = true;
        }

        return result;
    }

    private static decimal DoDictionaryMax(ParamObjectCollection arrParams, CalculateContext calcContext,
        ref bool dealed)
    {
        var result = decimal.MinValue;

        if (arrParams.Count == 1 && arrParams[0].Value is string == false && arrParams[0].Value is IDictionary)
        {
            foreach (DictionaryEntry entry in (IDictionary)(arrParams[0].Value!))
                result = Math.Max(result, NToD(entry.Value));

            dealed = true;
        }

        return result;
    }

    private static decimal DoParamsArrayMax(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        var result = decimal.MinValue;

        foreach (var param in arrParams)
            result = Math.Max(result, NToD(param.Value));

        return result;
    }

    #endregion DoMax

    #region DoMin

    private static object DoMin(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        var result = decimal.MaxValue;
        var dealed = false;

        result = DoArrayMin(arrParams, calcContext, ref dealed);

        if (dealed == false)
            result = DoDictionaryMin(arrParams, calcContext, ref dealed);

        if (dealed == false)
            result = DoParamsArrayMin(arrParams, calcContext);

        return result;
    }

    private static decimal DoArrayMin(ParamObjectCollection arrParams, CalculateContext calcContext, ref bool dealed)
    {
        var result = decimal.MaxValue;

        if (arrParams.Count == 1 && arrParams[0].Value is string == false && arrParams[0].Value is IList)
        {
            foreach (var value in (IList)(arrParams[0].Value!))
                result = Math.Min(result, NToD(value));

            dealed = true;
        }

        return result;
    }

    private static decimal DoDictionaryMin(ParamObjectCollection arrParams, CalculateContext calcContext,
        ref bool dealed)
    {
        var result = decimal.MaxValue;

        if (arrParams.Count == 1 && arrParams[0].Value is string == false && arrParams[0].Value is IDictionary)
        {
            foreach (DictionaryEntry entry in (IDictionary)(arrParams[0].Value!))
                result = Math.Min(result, NToD(entry.Value));

            dealed = true;
        }

        return result;
    }

    private static decimal DoParamsArrayMin(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        var result = decimal.MaxValue;

        foreach (var param in arrParams)
            result = Math.Min(result, NToD(param.Value));

        return result;
    }

    #endregion DoMin

    #region DoSum

    private static object DoSum(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        var result = 0M;
        var dealed = false;

        result = DoArraySum(arrParams, calcContext, ref dealed);

        if (dealed == false)
            result = DoDictionarySum(arrParams, calcContext, ref dealed);

        if (dealed == false)
            result = DoParamsArraySum(arrParams, calcContext);

        return result;
    }

    private static decimal DoArraySum(ParamObjectCollection arrParams, CalculateContext calcContext, ref bool dealed)
    {
        var result = 0M;

        if (arrParams.Count == 1 && arrParams[0].Value is string == false && arrParams[0].Value is IList)
        {
            foreach (var value in (IList)(arrParams[0].Value!))
                result += NToD(value);

            dealed = true;
        }

        return result;
    }

    private static decimal DoDictionarySum(ParamObjectCollection arrParams, CalculateContext calcContext,
        ref bool dealed)
    {
        var result = 0M;

        if (arrParams.Count == 1 && arrParams[0].Value is string == false && arrParams[0].Value is IDictionary)
        {
            foreach (DictionaryEntry entry in (IDictionary)(arrParams[0].Value!))
                result += NToD(entry.Value);

            dealed = true;
        }

        return result;
    }

    private static decimal DoParamsArraySum(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        var result = 0M;

        foreach (var param in arrParams)
            result += NToD(param.Value);

        return result;
    }

    #endregion DoSum

    #region DoArray

    private static object DoArray(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        object?[] result = new object[arrParams.Count];

        for (var i = 0; i < arrParams.Count; i++)
            result[i] = arrParams[i].Value;

        return result;
    }

    #endregion DoArray

    #region DoArrayIndexOf

    private static object DoArrayIndexOf(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        arrParams.CheckParamsLength(2);
        arrParams[0].CheckParameterType<IList>();

        var result = -1;

        if (arrParams[0].Value is string == false && arrParams[0].Value is IList)
        {
            (arrParams[0].Value as IList)
                .IsNotNull(list =>
                {
                    for (var i = 0; i < list!.Count; i++)
                    {
                        if ((bool)CompareEqualOP(arrParams[1].Value, list[i], 0))
                        {
                            result = i;
                            break;
                        }
                    }
                });
        }

        return result;
    }

    #endregion DoArraySub

    #region DoArraySub

    private static object DoArraySub(ParamObjectCollection arrParams, CalculateContext calcContext)
    {
        arrParams.CheckParamsLength(2);
        arrParams[0].CheckParameterType<IList>();
        arrParams[1].CheckParameterType<decimal>();

        var startIndex = (int)NToD(arrParams[1].Value);

        object?[] result = EmptyObjectArray;

        (arrParams[0].Value as IList)
            .IsNotNull(list =>
            {
                if (startIndex >= 0 && startIndex < list!.Count)
                {
                    var length = list.Count - startIndex;

                    if (arrParams.Count > 2)
                        length = Math.Min(length, Math.Max(0, (int)NToD(arrParams[2].Value)));

                    result = new object[length];

                    for (var i = 0; i < length; i++)
                        result[i] = list![startIndex + i];
                }
            });

        return result;
    }

    #endregion DoArrayLeft

    #endregion Paramlized BuiltIn Functions
}
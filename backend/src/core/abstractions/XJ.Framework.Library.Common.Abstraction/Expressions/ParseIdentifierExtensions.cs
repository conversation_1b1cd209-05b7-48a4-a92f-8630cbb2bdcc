namespace XJ.Framework.Library.Common.Abstraction.Expressions;

public static class ParseIdentifierExtensions
{
    /// <summary>
    /// 遍历所有的标识符
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="action"></param>
    public static void ScanIdentifiers(this ParseIdentifier? identifier, Action<ParseIdentifier> action)
    {
        if (action != null && identifier != null)
        {
            identifier.ScanIdentifiers(identifier =>
            {
                action(identifier!);
                return true;
            });
        }
    }

    /// <summary>
    /// 遍历所有的标识符
    /// </summary>
    /// <param name="identifier"></param>
    /// <param name="func"></param>
    public static bool ScanIdentifiers(this ParseIdentifier? identifier, Func<ParseIdentifier?, bool> func)
    {
        var execContinue = true;

        if (func != null && identifier != null)
        {
            execContinue = func(identifier);

            if (execContinue)
            {
                if (identifier.SubIdentifier != null)
                    execContinue = identifier.SubIdentifier.ScanIdentifiers(func);

                if (execContinue)
                {
                    if (identifier.NextIdentifier != null)
                        execContinue = identifier.NextIdentifier.ScanIdentifiers(func);
                }
            }
        }

        return execContinue;
    }

    /// <summary>
    /// 得到按照位置排序的标识符
    /// </summary>
    /// <param name="identifier"></param>
    /// <returns></returns>
    public static List<ParseIdentifier> GetSortedByPositionIdentifiers(this ParseIdentifier? identifier)
    {
        List<ParseIdentifier> result = [];

        identifier?.ScanIdentifiers(id => result.Add(id!));

        result.Sort((x, y) => x.Position - y.Position);

        return result;
    }
}
namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 表达式分析的结果
/// </summary>
/// <remarks>
/// 表达式分析后产生的结果
/// </remarks>
public sealed class ParseResult
{
    private readonly ExpTreeNode? tree = null;
    private readonly ParseIdentifier? identifiers = null;

    internal ParseResult(ExpTreeNode? tree, ParseIdentifier? identifiers)
    {
        this.tree = tree;
        this.identifiers = identifiers;
    }

    private ParseResult()
    {
    }

    /// <summary>
    /// 取解析出的二叉树
    /// </summary>
    public ExpTreeNode? Tree {
        get { return this.tree; }
    }

    /// <summary>
    /// 标识符
    /// </summary>
    public ParseIdentifier? Identifiers {
        get { return this.identifiers; }
    }
}
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 操作类型，作为语法分析二叉树节点中的运算符标识
/// </summary>
//public enum ParseOperation
public enum Operation_IDs
{
    /// <summary>
    /// 无操作符
    /// </summary>
    [EnumItemDescription("无操作符", ShortName = "")]
    OI_NONE = 0,

    /// <summary>
    /// 非
    /// </summary>
    [EnumItemDescription("非操作符", ShortName = "!")]
    OI_NOT = 120,

    /// <summary>
    /// 加
    /// </summary>
    [EnumItemDescription("加操作符", ShortName = "+")]
    OI_ADD,

    /// <summary>
    /// 减
    /// </summary>
    [EnumItemDescription("减操作符", ShortName = "-")]
    OI_MINUS,

    /// <summary>
    /// 乘
    /// </summary>
    [EnumItemDescription("乘操作符", ShortName = "*")]
    OI_MUL,

    /// <summary>
    /// 除
    /// </summary>
    [EnumItemDescription("除操作符", ShortName = "/")]
    OI_DIV,

    /// <summary>
    /// 负号
    /// </summary>
    [EnumItemDescription("负操作符", ShortName = "-")]
    OI_NEG,

    /// <summary>
    /// 等于
    /// </summary>
    [EnumItemDescription("等于操作符", ShortName = "==")]
    OI_EQUAL,

    /// <summary>
    /// 不等于
    /// </summary>
    [EnumItemDescription("不等于操作符", ShortName = "<>")]
    OI_NOT_EQUAL,

    /// <summary>
    /// 大于
    /// </summary>
    [EnumItemDescription("大于操作符", ShortName = ">")]
    OI_GREAT,

    /// <summary>
    /// 大于等于
    /// </summary>
    [EnumItemDescription("大于等于操作符", ShortName = ">=")]
    OI_GREATEQUAL,

    /// <summary>
    /// 小于
    /// </summary>
    [EnumItemDescription("小于操作符", ShortName = "<")]
    OI_LESS,

    /// <summary>
    /// 小于等于
    /// </summary>
    [EnumItemDescription("小于等于操作符", ShortName = "<=")]
    OI_LESSEQUAL,

    /// <summary>
    ///逻辑与 
    /// </summary>
    [EnumItemDescription("逻辑与操作符", ShortName = "&&")]
    OI_LOGICAL_AND,

    /// <summary>
    /// 逻辑或
    /// </summary>
    [EnumItemDescription("逻辑或操作符", ShortName = "||")]
    OI_LOGICAL_OR,

    /// <summary>
    /// 左括号
    /// </summary>
    [EnumItemDescription("左括号操作符", ShortName = "(")]
    OI_LBRACKET,

    /// <summary>
    /// 右括号
    /// </summary>
    [EnumItemDescription("右括号操作符", ShortName = ")")]
    OI_RBRACKET,

    /// <summary>
    /// 逗号
    /// </summary>
    [EnumItemDescription("逗号操作符", ShortName = ",")]
    OI_COMMA,

    /// <summary>
    /// 自定义函数
    /// </summary>
    [EnumItemDescription("自定义函数", ShortName = "自定义函数")]
    OI_USERDEFINE,

    /// <summary>
    /// 字符串
    /// </summary>
    [EnumItemDescription("字符串", ShortName = "字符串")]
    OI_STRING,

    /// <summary>
    /// 数字
    /// </summary>
    [EnumItemDescription("数字", ShortName = "数字")]
    OI_NUMBER,

    /// <summary>
    /// 布尔型
    /// </summary>
    [EnumItemDescription("布尔型", ShortName = "布尔型")]
    OI_BOOLEAN,

    /// <summary>
    /// 日期型
    /// </summary>
    [EnumItemDescription("日期型", ShortName = "日期型")]
    OI_DATETIME,
}
namespace XJ.Framework.Library.Common.Abstraction.Expressions;

public class UserFunctionCalculationOptions : CalculationOptionsBase
{
    public UserFunctionCalculationOptions(CalculateUserFunction? userFunction = null, object? callerContext = null,
        bool optimize = true)
        : base(callerContext, optimize)
    {
        this.UserFunction = userFunction;
    }

    public static UserFunctionCalculationOptions Default(CalculateUserFunction? userFunction = null)
    {
        return new UserFunctionCalculationOptions(userFunction);
    }

    public CalculateUserFunction? UserFunction {
        get;
        set;
    }
}
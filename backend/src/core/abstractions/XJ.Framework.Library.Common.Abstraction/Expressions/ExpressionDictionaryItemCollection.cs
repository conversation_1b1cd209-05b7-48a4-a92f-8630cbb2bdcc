using System.Runtime.Serialization;
using XJ.Framework.Library.Common.Abstraction.Data.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 
/// </summary>
[Serializable]
public class
    ExpressionDictionaryItemCollection : SerializableEditableKeyedDataObjectCollectionBase<string,
    ExpressionDictionaryItem>
{
    /// <summary>
    /// 
    /// </summary>
    public ExpressionDictionaryItemCollection() :
        base(StringComparer.OrdinalIgnoreCase)
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="elements"></param>
    public void InitFromConfiguration(ExpressionDictionaryItemOptionItemCollection elements)
    {
        elements.NullCheck(nameof(elements));

        foreach (var element in elements)
            this.Add(new ExpressionDictionaryItem(element));
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="info"></param>
    /// <param name="context"></param>
    protected ExpressionDictionaryItemCollection(SerializationInfo info, StreamingContext context) :
        base(info, context)
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="item"></param>
    /// <returns></returns>
    protected override string GetKeyForItem(ExpressionDictionaryItem item)
    {
        return item.Name.ToLower();
    }
}
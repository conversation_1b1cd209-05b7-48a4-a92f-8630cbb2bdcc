using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.Properties;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 为表达式识别错误封装的异常
/// </summary>
/// <remarks>
/// 封装的解析错误，在表达式解析过程中会报出异常，提示信息包括错误原因、出错位置
/// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="parse_error" lang="cs" />
/// </remarks>
[System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2237:MarkISerializableTypesWithSerializable"),
 System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2240:ImplementISerializableCorrectly")]
public sealed class ParsingException : System.Exception
{
    private readonly ParseError reason = ParseError.peNone;
    private readonly int position = -1;

    /// <summary>
    /// 构造函数，根据错误类型、出错位置构造异常
    /// </summary>
    /// <param name="pe">错误类型</param>
    /// <param name="nPosition">出错位置</param>
    /// <param name="strMsg">错误信息</param>
    /// <param name="innerException">内部异常</param>
    public ParsingException(ParseError pe, int nPosition, string strMsg, Exception? innerException = null)
        : base(strMsg, innerException)
    {
        this.reason = pe;
        this.position = nPosition;
    }

    /// <summary>
    /// 错误原因
    /// </summary>
    public ParseError Reason {
        get {
            return this.reason;
        }
    }

    /// <summary>
    /// 出错位置
    /// </summary>
    public int Position {
        get {
            return this.position;
        }
    }

    /// <summary>
    /// 产生一个新的表达式识别异常
    /// </summary>
    /// <param name="pe">错误原因</param>
    /// <param name="nPosition">出错位置</param>
    /// <param name="strParams">在错误信息中的参数</param>
    /// <returns>表达式识别异常对象</returns>
    /// <remarks>
    /// 对表达式模块中产生的错误进行封装
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="parse_error" lang="cs" title="异常的封装" />
    /// </remarks>
    static public ParsingException NewParsingException(ParseError pe, int nPosition, params string[] strParams)
    {
        var strText = ExpressionParserRes.ResourceManager.GetString(pe.ToString());

        strText.IsNotEmpty().FalseThrow($"无法根据{pe.ToString()}在资源中找到错误信息");

        strText = string.Format(strText!, strParams);

        if (nPosition >= 0)
            strText = string.Format(ExpressionParserRes.position, nPosition + 1) + ", " + strText;

        return new ParsingException(pe, nPosition, strText);
    }

    /// <summary>
    /// 产生一个新的表达式识别异常
    /// </summary>
    /// <param name="pe">错误原因</param>
    /// <param name="nPosition">出错位置</param>
    /// <param name="innerEx"></param>
    /// <param name="strParams">在错误信息中的参数</param>
    /// <returns>表达式识别异常对象</returns>
    /// <remarks>
    /// 对表达式模块中产生的错误进行封装
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="parse_error" lang="cs" title="异常的封装" />
    /// </remarks>
    static public ParsingException NewParsingException(ParseError pe, int nPosition, Exception innerEx,
        params string[] strParams)
    {
        var strText = ExpressionParserRes.ResourceManager.GetString(pe.ToString());

        strText.IsNotEmpty().FalseThrow($"无法根据{pe.ToString()}在资源中找到错误信息");

        strText = string.Format(strText!, strParams);

        if (nPosition >= 0)
            strText = string.Format(ExpressionParserRes.position, nPosition + 1) + ", " + strText;

        if (innerEx != null)
            strText += "\n" + innerEx.Message;

        return new ParsingException(pe, nPosition, strText);
    }
}
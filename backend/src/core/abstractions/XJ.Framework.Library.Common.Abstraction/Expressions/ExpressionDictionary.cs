using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

public class ExpressionDictionary
{
    private ExpressionDictionaryItemCollection? _Items = null;

    /// <summary>
    /// 
    /// </summary>
    public ExpressionDictionary()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="element"></param>
    public ExpressionDictionary(ExpressionDictionaryOptionItem element)
    {
        element.NullCheck(nameof(element));

        this.Name = element.Name;
        this.Description = element.Description;
        this.Calculator = (IExpressionDictionaryCalculator)element.CreateInstance();

        this.Items.InitFromConfiguration(element.Items);
    }

    /// <summary>
    /// 
    /// </summary>
    public string Name {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public IExpressionDictionaryCalculator? Calculator {
        get;
        set;
    } = null;

    /// <summary>
    /// 
    /// </summary>
    public string Description {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 
    /// </summary>
    public ExpressionDictionaryItemCollection Items {
        get {
            this._Items ??= [];

            return this._Items;
        }
    }
}
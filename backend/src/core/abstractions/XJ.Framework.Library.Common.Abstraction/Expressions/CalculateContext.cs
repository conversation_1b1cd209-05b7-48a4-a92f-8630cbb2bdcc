namespace XJ.Framework.Library.Common.Abstraction.Expressions;

internal class CalculateContext
{
    private bool optimize = true;

    public CalculateContext()
    {
    }

    public object? BuiltInFunctionsWrapper {
        get;
        set;
    }

    public bool Optimize {
        get { return this.optimize; }
        set { this.optimize = value; }
    }

    public object? CallerContext {
        get;
        set;
    } = null!;

    public CalculateUserFunction? CalculateUserFunction {
        get;
        set;
    }

    /// <summary>
    /// 应该从配置ExpressionDictionaryOptions初始化
    /// </summary>
    public ExpressionDictionaryCollection ExpressionDictionaries {
        get;
        set;
    } = [];

    public object? GetUserFunctionValue(string strFuncName, ParamObjectCollection arrParams)
    {
        object? oValue = null;

        if (this.CalculateUserFunction != null)
        {
            oValue = this.CalculateUserFunction(strFuncName, arrParams, this.CallerContext);
        }
        else if (this.BuiltInFunctionsWrapper != null)
        {
            oValue = BuiltInFunctionHelper.ExecuteFunction(strFuncName, this.BuiltInFunctionsWrapper, arrParams,
                this.CallerContext);
        }

        return oValue;
    }
}
using System.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

internal sealed partial class ExpTreeExecutor
{
    private delegate object? BuiltInAction(object? p1, object? p2, int nPos);

    private delegate object? TypeFilterAction(object? p1, object? p2, int nPos, BuiltInAction builtIn, ref bool dealed);

    private static readonly TypeFilterAction[] TypeFilterActions =
    {
        BuiltInTypeFilter,
        DictionaryAndBuiltInFilter,
        BuiltInAndDictionaryFilter,
        DictionaryFilter
    };

    #region BuiltIn

    private static object? AddBuiltIn(object? p1, object? p2, int nPos)
    {
        object? result;

        if (p1 is System.String || p2 is System.String)
            result = (p1 ?? string.Empty).ToString() + (p2 ?? string.Empty).ToString();
        else
        {
            result = NToD(p1) + NToD(p2);
        }

        return result;
    }

    private static object? MinusBuiltIn(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);

        return NToD(p1) - NToD(p2);
    }

    private static object? MulBuiltIn(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);

        return NToD(p1) * NToD(p2);
    }

    private static object? DivBuiltIn(object? p1, object? p2, int nPos)
    {
        CheckOperandNull(p1, p2, nPos);

        if (NToD(p2!) == 0.0M)
            throw ParsingException.NewParsingException(ParseError.peFloatOverflow, nPos);

        return NToD(p1) / NToD(p2);
    }

    #endregion BuiltIn

    private static object? ExecuteFilter(object? p1, object? p2, BuiltInAction builtIn, int nPos,
        CalculateContext calcContext)
    {
        CheckOperandNull(p1, p2, nPos);

        var dealed = false;
        object? result = null;

        if (calcContext.CallerContext != null)
        {
            (p1 as IExpressionValueGetter).IsNotNull(g => p1 = g.GetValue(calcContext.CallerContext));
            (p2 as IExpressionValueGetter).IsNotNull(g => p2 = g.GetValue(calcContext.CallerContext));
        }

        foreach (var action in TypeFilterActions)
        {
            result = action(p1, p2, nPos, builtIn, ref dealed);

            if (dealed)
                break;
        }

        if (dealed == false)
            throw ParsingException.NewParsingException(ParseError.InvalidParameterType, nPos);

        return result;
    }

    private static object? BuiltInTypeFilter(object? p1, object? p2, int nPos, BuiltInAction builtIn, ref bool dealed)
    {
        object? result = null;

        if (IsBuiltInType(p1) && IsBuiltInType(p2))
        {
            result = builtIn(p1, p2, nPos);

            dealed = true;
        }

        return result;
    }

    private static object? DictionaryAndBuiltInFilter(object? p1, object? p2, int nPos, BuiltInAction builtIn,
        ref bool dealed)
    {
        object? result = null;

        if (p1 is IDictionary && IsBuiltInType(p2))
        {
            result = EnumerateDictionary(p1,
                kp => builtIn(kp.Value, p2, nPos));

            dealed = true;
        }

        return result;
    }

    private static object? BuiltInAndDictionaryFilter(object? p1, object? p2, int nPos, BuiltInAction builtIn,
        ref bool dealed)
    {
        object? result = null;

        if (IsBuiltInType(p1) && p2 is IDictionary)
        {
            result = EnumerateDictionary(p2,
                kp => builtIn(p1, kp.Value, nPos));

            dealed = true;
        }

        return result;
    }

    private static Dictionary<object, object?>? DictionaryFilter(object? p1, object? p2, int nPos,
        BuiltInAction builtIn, ref bool dealed)
    {
        Dictionary<object, object?>? result = null;

        if (p1 is IDictionary && p2 is IDictionary)
        {
            var p2Dict = (IDictionary)p2;

            result = EnumerateDictionary(p1, kp =>
            {
                var value = kp.Value;
                var p2Value = p2Dict[kp.Key];

                if (p2Value != null)
                    value = builtIn(kp.Value, p2Value, nPos);

                return value;
            });

            var p1Dict = (IDictionary)p1;

            foreach (DictionaryEntry entry in p2Dict)
            {
                if (p1Dict.Contains(entry.Key) == false)
                    result.Add(entry.Key, entry.Value);
            }

            dealed = true;
        }

        return result;
    }

    private static bool IsBuiltInType(object? data)
    {
        return data == null || data is string || data.GetType().IsValueType;
    }

    private static Dictionary<object, object?> EnumerateDictionary(
        object src,
        Func<DictionaryEntry, object?> func)
    {
        var srcDict = (IDictionary)src;

        Dictionary<object, object?> result = [];

        foreach (DictionaryEntry kp in srcDict)
        {
            object? value = null;

            if (func != null)
                value = func(kp);

            result.Add(kp.Key, value);
        }

        return result;
    }
}
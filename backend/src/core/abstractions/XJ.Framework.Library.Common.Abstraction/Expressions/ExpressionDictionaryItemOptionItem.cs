using XJ.Framework.Library.Common.Abstraction.Options;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 表达式字典项说明的配置项
/// </summary>
public class ExpressionDictionaryItemOptionItem : NamedOptionItem
{
    /// <summary>
    /// 数据类型
    /// </summary>
    public ExpressionDataType DataType {
        get;
        set;
    }

    /// <summary>
    /// 默认值
    /// </summary>
    public string DefaultValue {
        get;
        set;
    } = string.Empty;
}

/// <summary>
/// 表达式字典项说明的配置项集合
/// </summary>
public class
    ExpressionDictionaryItemOptionItemCollection : NamedOptionItemCollection<ExpressionDictionaryItemOptionItem>
{
}
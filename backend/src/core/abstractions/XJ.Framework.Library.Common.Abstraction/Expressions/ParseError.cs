namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 错误类别
/// </summary>
/// <remarks>
/// 表达式解析的一些错误封装
/// </remarks>
public enum ParseError
{
    /// <summary>
    /// 无异常
    /// </summary>
    peNone = 0,

    /// <summary>
    /// 非法字符
    /// </summary>
    peInvalidChar,

    /// <summary>
    /// 非法的字符串
    /// </summary>
    peInvalidString,

    /// <summary>
    /// 非法操作符
    /// </summary>
    peInvalidOperator,

    /// <summary>
    /// 类型不匹配
    /// </summary>
    peTypeMismatch,

    /// <summary>
    /// 非法的参数
    /// </summary>
    peInvalidParam,

    /// <summary>
    /// 非法的用户自定义函数返回值
    /// </summary>
    peInvalidUFValue,

    /// <summary>
    /// 语法错误
    /// </summary>
    peSyntaxError,

    /// <summary>
    /// 浮点运算溢出
    /// </summary>
    peFloatOverflow,

    /// <summary>
    /// 需要某个字符
    /// </summary>
    peCharExpected,

    /// <summary>
    /// 函数错误
    /// </summary>
    peFuncError,

    /// <summary>
    /// 需要操作数
    /// </summary>
    peNeedOperand,

    /// <summary>
    /// 格式错误
    /// </summary>
    peFormatError,

    /// <summary>
    /// 参数类型错误
    /// </summary>
    InvalidParameterType,


    ///// <summary>
    ///// 参数个数错误
    ///// </summary>
    //InvalidParameterNum, 
}
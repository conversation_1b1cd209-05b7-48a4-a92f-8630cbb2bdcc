using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Attributes;

[AttributeUsage(AttributeTargets.Class, Inherited = true, AllowMultiple = true)]
public abstract class TargetAttributeBase<T> : Attribute
{
    public TargetAttributeBase()
    {
    }

    public TargetAttributeBase(T target)
    {
        target.NullCheck(nameof(target));

        this.Target = target;
    }

    public virtual T Target {
        get;
        set;
    } = default!;
}
namespace XJ.Framework.Library.Common.Abstraction.Exceptions;

public class HttpClientNonTransientException : NonTransientException, IHttpClientException
{
    public HttpClientNonTransientException()
    {
    }

    public HttpClientNonTransientException(string strMessage)
        : base(strMessage)
    {
    }

    public HttpClientNonTransientException(string strMessage, Exception? ex)
        : base(strMessage, ex)
    {
    }

    public int StatusCode {
        get;
        set;
    }

    public Uri? RequestUri {
        get;
        set;
    }

    public HttpMethod Method {
        get;
        set;
    } = HttpMethod.Get;
}
namespace XJ.Framework.Library.Common.Abstraction.Exceptions;

public class RateLimitingException : NonTransientException
{
    public RateLimitingException()
    {
    }

    public RateLimitingException(string message) : base(message)
    {
    }

    public RateLimitingException(string message, int ruleLimit, int tokenUsed) : base(message)
    {
        this.RuleLimit = ruleLimit;
        this.TokenUsed = tokenUsed;
    }

    public int RuleLimit {
        get;
    }

    public int TokenUsed {
        get;
    }
}
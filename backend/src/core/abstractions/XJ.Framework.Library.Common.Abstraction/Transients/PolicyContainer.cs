using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Transients;

public class PolicyContainer
{
    private readonly PolicyExceptionHandlerCollection handledExceptions = new();
    private readonly PolicyExceptionHandlerCollection unhandledExceptions = new();
    private Func<int, TimeSpan>? waitCalculator = null;
    private int retryLimit = 1;
    private TimeSpan waitDuration = TimeSpan.Zero;

    internal PolicyContainer()
    {
        this.waitCalculator = this.DefaultWaitCalculator;
    }

    public PolicyContainer Handle<TException>(Func<System.Exception, bool>? exceptionPredicate = null)
        where TException : Exception
    {
        return this.Handle(typeof(TException), exceptionPredicate);
    }

    public PolicyContainer Handle(System.Type exceptionType, Func<System.Exception, bool>? exceptionPredicate = null)
    {
        if (this.handledExceptions.ContainsKey(exceptionType) == false)
            this.handledExceptions.Add(new PolicyExceptionHandler(exceptionType, exceptionPredicate));

        return this;
    }

    public PolicyContainer Unhandle<TException>(Func<System.Exception, bool>? exceptionPredicate = null)
        where TException : Exception
    {
        return this.Unhandle(typeof(TException), exceptionPredicate);
    }

    public PolicyContainer Unhandle(System.Type exceptionType, Func<System.Exception, bool>? exceptionPredicate = null)
    {
        if (this.unhandledExceptions.ContainsKey(exceptionType) == false)
            this.unhandledExceptions.Add(new PolicyExceptionHandler(exceptionType, exceptionPredicate));

        return this;
    }

    public PolicyContainer RetryLimit(int retryLimit)
    {
        this.retryLimit = retryLimit;

        return this;
    }

    public PolicyContainer Wait(TimeSpan waitDuration)
    {
        this.waitDuration = waitDuration;

        return this;
    }

    public PolicyContainer WaitAndRetry(int retryLimit, Func<int, TimeSpan> waitCalculator)
    {
        waitCalculator.NullCheck();

        this.retryLimit = retryLimit;
        this.waitCalculator = waitCalculator;

        return this;
    }

    private TimeSpan DefaultWaitCalculator(int retryAttempt)
    {
        return this.waitDuration;
    }

    public void Execute(Action<int> action)
    {
        this.InnerExecute(action);
    }

    public R Execute<R>(Func<int, R> func)
    {
        return this.InnerExecute(func);
    }

    public async Task ExecuteAsync<R>(Func<int, Task> func)
    {
        await this.InnerExecuteAsync<Task>(func);
    }

    public async Task<R> ExecuteAsync<R>(Func<int, Task<R>> func)
    {
        return await this.InnerExecuteAsync(func);
    }

    private void InnerExecute(Action<int> action)
    {
        action.NullCheck();

        var retires = 0;
        System.Exception? lastException = null;
        var wait = this.waitDuration;

        while (retires <= this.retryLimit)
        {
            try
            {
                lastException = null;
                wait = this.waitCalculator!(retires + 1);
                action(retires);
                break;
            }
            catch (Exception ex)
            {
                this.CheckHandlers(ex);

                Thread.Sleep(wait);
                lastException = ex;
            }
            finally
            {
                retires++;
            }
        }

        if (lastException != null)
            throw lastException;
    }

    private R InnerExecute<R>(Func<int, R> func)
    {
        func.NullCheck();

        var retires = 0;
        System.Exception? lastException = null;
        var wait = this.waitDuration;

        R? result = default;

        while (retires <= this.retryLimit)
        {
            try
            {
                lastException = null;
                wait = this.waitCalculator!(retires + 1);
                result = func(retires);
                break;
            }
            catch (Exception ex)
            {
                this.CheckHandlers(ex);

                Thread.Sleep(wait);
                lastException = ex;
            }
            finally
            {
                retires++;
            }
        }

        if (lastException != null)
            throw lastException;

        return result!;
    }

    private async Task InnerExecuteAsync<R>(Func<int, Task> func)
    {
        func.NullCheck();

        var retires = 0;
        System.Exception? lastException = null;
        var wait = this.waitDuration;

        while (retires <= this.retryLimit)
        {
            try
            {
                lastException = null;
                wait = this.waitCalculator!(retires + 1);
                await func(retires);
                break;
            }
            catch (Exception ex)
            {
                this.CheckHandlers(ex);

                await Task.Delay(wait);

                lastException = ex;
            }
            finally
            {
                retires++;
            }
        }

        if (lastException != null)
            throw lastException;
    }

    private async Task<R> InnerExecuteAsync<R>(Func<int, Task<R>> func)
    {
        func.NullCheck();

        var retires = 0;
        System.Exception? lastException = null;
        var wait = this.waitDuration;

        R? result = default;

        while (retires <= this.retryLimit)
        {
            try
            {
                lastException = null;
                wait = this.waitCalculator!(retires + 1);
                result = await func(retires);
                break;
            }
            catch (Exception ex)
            {
                this.CheckHandlers(ex);

                await Task.Delay(wait);

                lastException = ex;
            }
            finally
            {
                retires++;
            }
        }

        if (lastException != null)
            throw lastException;

        return result!;
    }

    private void CheckHandlers(System.Exception ex)
    {
        // 如果在不重试的异常中，则抛出
        if (InHandlers(this.unhandledExceptions, ex))
            throw ex;

        // 如果不在重试的异常中，则抛出
        if (InHandlers(this.handledExceptions, ex) == false)
            throw ex;
    }

    private static bool InHandlers(IEnumerable<PolicyExceptionHandler> handlers, Exception ex)
    {
        var result = false;

        foreach (var handler in handlers)
        {
            if (handler.ExceptionPredicate(ex))
            {
                result = true;
                break;
            }
        }

        return result;
    }
}
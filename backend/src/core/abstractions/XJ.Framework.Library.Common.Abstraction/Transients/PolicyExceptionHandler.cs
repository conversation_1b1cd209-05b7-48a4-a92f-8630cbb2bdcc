using XJ.Framework.Library.Common.Abstraction.Data.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Transients;

internal class PolicyExceptionHandler
{
    public PolicyExceptionHandler(Type exceptionType, Func<System.Exception, bool>? exceptionPredicate = null)
    {
        exceptionType.NullCheck();
        (typeof(System.Exception).IsAssignableFrom(exceptionType))
            .FalseThrow($"{exceptionType.FullName}不是一个Exception类型");

        this.ExceptionType = exceptionType;

        if (exceptionPredicate != null)
            this.ExceptionPredicate = exceptionPredicate;
        else
            this.ExceptionPredicate = this.DefaultFunc;
    }

    private bool DefaultFunc(System.Exception actualException)
    {
        return this.ExceptionType.IsAssignableFrom(actualException.GetType());
    }

    public System.Type ExceptionType {
        get;
        private set;
    } = null!;

    public Func<System.Exception, bool> ExceptionPredicate {
        get;
        private set;
    } = null!;
}

internal class PolicyExceptionHandlerCollection : EditableKeyedDataObjectCollectionBase<Type, PolicyExceptionHandler>
{
    protected override Type GetKeyForItem(PolicyExceptionHandler item)
    {
        return item.ExceptionType;
    }
}
using System.IO.Compression;
using System.Text;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Compression;

public class ZipDecompression : IDisposable
{
    private readonly ZipArchive _archive;
    private readonly IEnumerator<ZipArchiveEntry> _zipEntries;
#pragma warning disable CS8625
    private Stream _currentStream = null;
#pragma warning restore CS8625

    public Stream CurrentStream => _currentStream;

    public ZipDecompression(Stream zipStream)
    {
        zipStream.NullCheck();

        _archive = new ZipArchive(zipStream, ZipArchiveMode.Read, true, Encoding.UTF8);

        _zipEntries = _archive.Entries.GetEnumerator();
    }

    public bool MoveNext()
    {
        if (!_zipEntries.MoveNext())
        {
            return false;
        }

        using var entryStream = _zipEntries.Current.Open();

        _currentStream = new MemoryStream();

        entryStream.CopyTo(_currentStream);

        _currentStream.Seek(0, SeekOrigin.Begin);


        return true;
    }

    public string CurrentUri => _zipEntries.Current.FullName;


    public void Dispose()
    {
        _archive.Dispose();
        _zipEntries.Dispose();
    }
}
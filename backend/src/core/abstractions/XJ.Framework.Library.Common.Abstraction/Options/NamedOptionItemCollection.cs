using XJ.Framework.Library.Common.Abstraction.Data.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Options;

public abstract class NamedOptionItemCollection<T> : EditableKeyedDataObjectCollectionBase<string, T>
    where T : NamedOptionItem, new()
{
    /// <summary>
    /// 检查并且获取对应的项，如果不存在，则抛出异常
    /// </summary>
    /// <param name="name"></param>
    /// <returns></returns>
    public T CheckAndGet(string name)
    {
        return this.CheckAndGet(name, true);
    }

    /// <summary>
    /// 检查并且获取对应的项，如果不存在，则抛出异常
    /// </summary>
    /// <param name="name"></param>
    /// <param name="autoThrow">是否自动抛出异常</param>
    /// <returns></returns>
    public T CheckAndGet(string name, bool autoThrow)
    {
        if (this.ContainsKey(name) == false && autoThrow)
            throw new SystemSupportException(string.Format("不能找到名称为{0}的元素", name));

        return this[name]!;
    }

    protected override string GetKeyForItem(T item)
    {
        return item.Name;
    }
}
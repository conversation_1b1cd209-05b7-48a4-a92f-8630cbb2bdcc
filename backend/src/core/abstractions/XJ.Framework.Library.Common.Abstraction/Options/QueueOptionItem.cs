namespace XJ.Framework.Library.Common.Abstraction.Options;

/// <summary>
/// 队列的配置项
/// </summary>
public class QueueOptionItem : NamedOptionItem
{
    public string QueueName {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 默认有多少个执行器
    /// </summary>
    public int ProcessorCount {
        get;
        set;
    } = 5;

    /// <summary>
    /// 每次 Dequeue 的数量
    /// </summary>
    public int BatchSize {
        get;
        set;
    } = 1;

    /// <summary>
    /// 队列的类别，用于分组启动消费者
    /// </summary>
    public string Category {
        get;
        set;
    } = string.Empty;
}
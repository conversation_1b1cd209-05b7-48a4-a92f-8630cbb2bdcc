using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Data.Collections;

/// <summary>
/// 数据对象集合类的虚基类
/// </summary>
/// <typeparam name="T"></typeparam>
[Serializable]
public abstract class DataObjectCollectionBase<T> : ReadOnlyDataObjectCollectionBase<T>
{
    /// <summary>
    /// 构造方法
    /// </summary>
    protected DataObjectCollectionBase()
    {
    }

    /// <summary>
    /// 构造方法。集合增加时的分配冗余
    /// </summary>
    /// <param name="capacity"></param>
    protected DataObjectCollectionBase(int capacity)
        : base(capacity)
    {
    }

    /// <summary>
    /// 从别的集合中复制(添加到现有的集合中)
    /// </summary>
    /// <param name="data"></param>
    public void CopyFrom(IEnumerable<T> data)
    {
        this.CopyFrom(data, null, null);
    }

    /// <summary>
    /// 从别的集合中复制(添加到现有的集合中)，复制的过程可以筛选数据
    /// </summary>
    /// <param name="data"></param>
    /// <param name="predicate">筛选条件</param>
    public void CopyFrom(IEnumerable<T> data, Predicate<T> predicate)
    {
        this.CopyFrom(data, predicate, null);
    }

    /// <summary>
    /// 从别的集合中复制(添加到现有的集合中)，复制的过程可以转换数据
    /// </summary>
    /// <param name="data"></param>
    /// <param name="converter">数据转换器</param>
    public void CopyFrom(IEnumerable<T> data, Converter<T, T> converter)
    {
        this.CopyFrom(data, null, converter);
    }

    /// <summary>
    /// 从别的集合中复制(添加到现有的集合中)。复制的过程可以筛选和转换数据
    /// </summary>
    /// <param name="data">源集合</param>
    /// <param name="predicate">筛选条件</param>
    /// <param name="converter">转换器</param>
    public virtual void CopyFrom(IEnumerable<T> data, Predicate<T>? predicate, Converter<T, T>? converter)
    {
        (data != null).FalseThrow<ArgumentNullException>(nameof(data));

        IEnumerator<T> enumerator = data!.GetEnumerator();

        while (enumerator.MoveNext())
        {
            var item = (T)enumerator.Current;

            if (predicate == null || predicate(item))
            {
                var newItem = item;

                if (converter != null)
                    newItem = converter(item);

                this.InnerAdd(newItem);
            }
        }
    }

    /// <summary>
    /// 删除满足条件的记录
    /// </summary>
    /// <param name="match"></param>
    public bool Remove(Predicate<T?> match)
    {
        match.NullCheck(nameof(match));

        var i = 0;
        var result = false;

        while (i < this.Count)
        {
            var data = (T?)List[i];

            if (match!(data))
            {
                this.RemoveAt(i);
                result = true;
            }
            else
                i++;
        }

        return result;
    }
}
using System.Collections;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Data.DataObjects;

/// <summary>
/// 
/// </summary>
/// <typeparam name="T<PERSON><PERSON>"></typeparam>
/// <typeparam name="TList"></typeparam>
public abstract class GroupNode<TKey, TList>
    where TList : IList, new()
    where TKey : notnull
{
    private TKey? groupKey;
    private TList? data;

    /// <summary>
    /// 
    /// </summary>
    public TKey GroupKey {
        get {
            return this.groupKey!;
        }
        internal set {
            value.NullCheck();

            this.groupKey = value;
        }
    }

    /// <summary>
    /// 
    /// </summary>
    public TList Data {
        get {
            if (this.data == null)
                this.data = [];

            return this.data;
        }
        internal set {
            this.data = value;
        }
    }
}
namespace XJ.Framework.Library.Common.Abstraction.Data.DataObjects;

public class TimeScope
{
    public TimeScope()
    {
        this.StartTime = DateTimeOffset.MinValue;
        this.EndTime = DateTimeOffset.MaxValue;
    }

    public TimeScope(DateTimeOffset startTime, DateTimeOffset endTime)
    {
        this.StartTime = startTime;
        this.EndTime = endTime;
    }

    public DateTimeOffset StartTime { get; set; }

    public DateTimeOffset EndTime { get; set; }

    public bool IsEmpty {
        get {
            return this.StartTime == DateTime.MinValue && this.EndTime == DateTime.MaxValue;
        }
    }

    public bool IsInScope(DateTimeOffset time)
    {
        return time >= this.StartTime && time < this.EndTime;
    }

    public bool IsInScope(TimeScope scope)
    {
        return scope.StartTime >= this.StartTime && scope.EndTime <= this.EndTime;
    }

    public TimeScope Intersect(TimeScope scope)
    {
        TimeScope result = new()
        {
            StartTime = this.StartTime > scope.StartTime ? this.StartTime : scope.StartTime,
            EndTime = this.EndTime < scope.EndTime ? this.EndTime : scope.EndTime
        };

        return result;
    }

    public TimeScope Union(TimeScope scope)
    {
        TimeScope result = new();

        result.StartTime = this.StartTime < scope.StartTime ? this.StartTime : scope.StartTime;
        result.EndTime = this.EndTime > scope.EndTime ? this.EndTime : scope.EndTime;

        return result;
    }

    public TimeScope Extend(TimeSpan timeSpan)
    {
        TimeScope result = new();

        result.StartTime = this.StartTime - timeSpan;
        result.EndTime = this.EndTime + timeSpan;

        return result;
    }

    public TimeScope Shrink(TimeSpan timeSpan)
    {
        TimeScope result = new();

        result.StartTime = this.StartTime + timeSpan;
        result.EndTime = this.EndTime - timeSpan;

        return result;
    }

    public TimeScope Clone()
    {
        return new TimeScope(this.StartTime, this.EndTime);
    }

    public override string ToString()
    {
        return string.Format("StartTime: {0}, EndTime: {1}", this.StartTime, this.EndTime);
    }
}
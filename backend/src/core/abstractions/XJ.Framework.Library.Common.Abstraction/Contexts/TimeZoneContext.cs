namespace XJ.Framework.Library.Common.Abstraction.Contexts;

/// <summary>
/// 时区相关的上下文
/// </summary>
[ActionContextDescription(Key = nameof(TimeZoneContext))]
public class TimeZoneContext : ActionContextBase<TimeZoneContext>
{
    /// <summary>
    /// 构造方法
    /// </summary>
    public TimeZoneContext()
    {
        this.CurrentTimeZone = GetDefaultTimeZone();
    }

    /// <summary>
    /// 
    /// </summary>
    public TimeZoneInfo CurrentTimeZone {
        get;
        set;
    }

    /// <summary>
    /// 将TimeSpan从utc转成local
    /// </summary>
    /// <param name="utcTimeSpan"></param>
    /// <returns></returns>
    public TimeSpan ConvertTimeFromUtc(TimeSpan utcTimeSpan)
    {
        var tz = this.CurrentTimeZone;

        if (tz == null)
            tz = TimeZoneInfo.Local;

        return utcTimeSpan + tz.BaseUtcOffset;
    }

    /// <summary>
    /// 将TimeSpan从local转成utc
    /// </summary>
    /// <param name="localTimeSpan"></param>
    /// <returns></returns>
    public TimeSpan ConvertTimeToUtc(TimeSpan localTimeSpan)
    {
        var tz = this.CurrentTimeZone;

        if (tz == null)
            tz = TimeZoneInfo.Local;

        return localTimeSpan - tz.BaseUtcOffset;
    }

    /// <summary>
    /// 从Utc时间转变为对应时区的本地时间
    /// </summary>
    /// <param name="utcTime"></param>
    /// <returns></returns>
    public DateTime ConvertTimeFromUtc(DateTime utcTime)
    {
        var tz = this.CurrentTimeZone;

        if (tz == null)
            tz = TimeZoneInfo.Local;

        var unspecifiedTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Unspecified);

        DateTime result;

        if (unspecifiedTime == DateTime.MinValue)
        {
            result = DateTime.MinValue;
        }
        else
        {
            var convertedTime = DateTime.SpecifyKind(utcTime, DateTimeKind.Utc);
            result = TimeZoneInfo.ConvertTimeFromUtc(convertedTime, tz);
            result = DateTime.SpecifyKind(result, DateTimeKind.Local);
        }

        return result;
    }

    /// <summary>
    /// 将某个时区的时间转变为Utc时间
    /// </summary>
    /// <param name="localTime"></param>
    /// <returns></returns>
    public DateTime ConvertTimeToUtc(DateTime localTime)
    {
        var tz = this.CurrentTimeZone;

        if (tz == null)
            tz = TimeZoneInfo.Local;

        var unspecifiedTime = DateTime.SpecifyKind(localTime, DateTimeKind.Unspecified);

        DateTime result;

        if (unspecifiedTime == DateTime.MinValue)
        {
            result = DateTime.MinValue;
        }
        else
        {
            result = TimeZoneInfo.ConvertTimeToUtc(unspecifiedTime, tz);
            result = DateTime.SpecifyKind(result, DateTimeKind.Utc);
        }

        return result;
    }

    /// <summary>
    /// 将本机Local的时间转换为当前上下文中的时间
    /// </summary>
    /// <param name="localTime"></param>
    /// <returns></returns>
    public DateTime ConvertLocalTimeToCurrent(DateTime localTime)
    {
        var tz = this.CurrentTimeZone;

        if (tz == null)
            tz = TimeZoneInfo.Local;

        var convertedTime = DateTime.SpecifyKind(localTime, DateTimeKind.Unspecified);

        return TimeZoneInfo.ConvertTime(convertedTime, TimeZoneInfo.Local, tz);
    }

    private static TimeZoneInfo GetDefaultTimeZone()
    {
        var result = TimeZoneInfo.Local;

        //if (TimeZoneContextSettings.GetConfig().Enabled)
        //{
        //    TimeZoneContextSettings settings = TimeZoneContextSettings.GetConfig();

        //    result = TimeZoneInfo.CreateCustomTimeZone(settings.TimeZoneID,
        //        settings.TimeOffset, settings.TimeZoneName, settings.TimeZoneName);
        //}

        return result;
    }
}
namespace XJ.Framework.Library.Common.Abstraction.Contexts;

public interface IContextContainer
{
    /// <summary>
    /// 初始化
    /// </summary>
    /// <returns></returns>
    IContextContainer Init();

    /// <summary>
    /// 得到某一种Context队列
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <returns></returns>
    T GetInstance<T>() where T : ContextCacheQueueBase;

    /// <summary>
    /// 清除所有状态数据
    /// </summary>
    void Clear();
}
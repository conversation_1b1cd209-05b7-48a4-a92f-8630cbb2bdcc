namespace XJ.Framework.Library.Common.Abstraction.Contexts;

/// <summary>
/// 
/// </summary>
public abstract class ActionContextBase<T> where T : ActionContextBase<T>, new()
{
    /// <summary>
    /// 执行<paramref name="action"/>指定的操作。
    /// </summary>
    /// <param name="action">操作方法 或 <see langword="null"/>表示无操作。</param>
    [System.Diagnostics.DebuggerNonUserCode]
    public void DoAction(Action? action)
    {
        try
        {
            action?.Invoke();
        }
        finally
        {
        }
    }

    /// <summary>
    /// 执行<paramref name="action"/>指定的操作。
    /// </summary>
    /// <param name="action">操作方法 或 <see langword="null"/>表示无操作。</param>
    [System.Diagnostics.DebuggerNonUserCode]
    public async Task DoActionAsync(Func<Task>? action)
    {
        if (action != null)
            await action();
    }
}
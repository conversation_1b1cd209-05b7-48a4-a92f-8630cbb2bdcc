using Microsoft.Extensions.Logging;
using Serilog.Context;

namespace XJ.Framework.Library.Logging.Abstraction.DI;

public static class LoggerExtensions
{
    public static void LoggingInformation(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogInformation(message, args);
        }
    }

    public static void LoggingWarning(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogWarning(message, args);
        }
    }

    public static void LoggingError(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogError(message, args);
        }
    }

    public static void LoggingDebug(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogDebug(message, args);
        }
    }

    public static void LoggingCritical(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogCritical(message, args);
        }
    }

    public static void LoggingTrace(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogTrace(message, args);
        }
    }

    public static void LoggingException(this ILogger logger, string category, Exception exception, string message,
        params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogError(exception, message, args);
        }
    }

    public static void LoggingException(this ILogger logger, string category, Exception exception)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogError(exception, exception.Message);
        }
    }

    public static void LoggingException(this ILogger logger, string category, string message)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogError(message);
        }
    }

    public static void LoggingException(this ILogger logger, string category, string message, params object[] args)
    {
        using (LogContext.PushProperty("Category", category))
        {
            logger.LogError(message, args);
        }
    }
}
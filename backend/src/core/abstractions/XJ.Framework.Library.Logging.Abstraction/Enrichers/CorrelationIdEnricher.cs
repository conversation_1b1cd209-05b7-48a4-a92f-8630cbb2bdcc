using Serilog.Core;
using Serilog.Events;
using XJ.Framework.Library.Common.Abstraction.Contexts;

namespace XJ.Framework.Library.Logging.Abstraction.Enrichers;

public class CorrelationIdEnricher : ILogEventEnricher
{
    private const string CorrelationIdPropertyName = "CorrelationId";

    private readonly IContextContainer? _contextContainer;

    public CorrelationIdEnricher(IContextContainer? contextContainer)
    {
        _contextContainer = contextContainer;
    }

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        if (_contextContainer == null)
        {
            return;
        }

        try
        {
            var correlationId = _contextContainer.GetCorrelationId();

            var correlationIdProperty = new LogEventProperty(CorrelationIdPropertyName, new ScalarValue(correlationId));

            logEvent.AddOrUpdateProperty(correlationIdProperty);
        }
        catch //(InvalidOperationException ex)
        {
            // 此处如果没获取到CorrelationId 将异常信息记入CorrelationId

            var correlationIdProperty = new LogEventProperty(CorrelationIdPropertyName, new ScalarValue("Null"));

            logEvent.AddOrUpdateProperty(correlationIdProperty);
        }
    }
}
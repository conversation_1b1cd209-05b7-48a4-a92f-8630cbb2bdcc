namespace XJ.Framework.Library.DistributedLock;

/// <summary>
/// 分布式锁接口
/// </summary>
public interface IDistributedLock
{
    /// <summary>
    /// 尝试获取分布式锁
    /// </summary>
    /// <param name="key">锁的键</param>
    /// <param name="expiration">锁的过期时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>锁的句柄，如果获取失败返回null</returns>
    Task<IDistributedLockHandle?> TryAcquireAsync(string key, TimeSpan expiration, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// 获取分布式锁（阻塞等待）
    /// </summary>
    /// <param name="key">锁的键</param>
    /// <param name="expiration">锁的过期时间</param>
    /// <param name="timeout">等待超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>锁的句柄</returns>
    Task<IDistributedLockHandle> AcquireAsync(string key, TimeSpan expiration, TimeSpan timeout, CancellationToken cancellationToken = default);
}

/// <summary>
/// 分布式锁句柄
/// </summary>
public interface IDistributedLockHandle : IDisposable
{
    /// <summary>
    /// 释放锁
    /// </summary>
    Task ReleaseAsync();
}



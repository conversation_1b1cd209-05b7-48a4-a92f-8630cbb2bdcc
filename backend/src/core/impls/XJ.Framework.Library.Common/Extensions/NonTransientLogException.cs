using XJ.Framework.Library.Common.Abstraction.Exceptions;

namespace XJ.Framework.Library.Common.Extensions;

public class NonTransientLogException : NonTransientException, IEscapeLogException
{
    /// <summary>
    /// 
    /// </summary>
    public NonTransientLogException()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="strMessage"></param>
    public NonTransientLogException(string strMessage)
        : base(strMessage)
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="strMessage"></param>
    /// <param name="ex"></param>
    public NonTransientLogException(string strMessage, Exception? ex)
        : base(strMessage, ex)
    {
    }
}
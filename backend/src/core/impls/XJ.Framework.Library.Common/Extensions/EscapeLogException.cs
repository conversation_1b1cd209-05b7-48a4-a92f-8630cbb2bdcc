namespace XJ.Framework.Library.Common.Extensions;

public class EscapeLogException : ApplicationException, IEscapeLogException
{
    /// <summary>
    /// 
    /// </summary>
    public EscapeLogException()
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="strMessage"></param>
    public EscapeLogException(string strMessage)
        : base(strMessage)
    {
    }

    /// <summary>
    /// 
    /// </summary>
    /// <param name="strMessage"></param>
    /// <param name="ex"></param>
    public EscapeLogException(string strMessage, Exception? ex)
        : base(strMessage, ex)
    {
    }
}
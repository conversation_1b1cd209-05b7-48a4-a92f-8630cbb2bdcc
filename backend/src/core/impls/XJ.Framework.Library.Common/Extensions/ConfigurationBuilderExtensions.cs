using Microsoft.Extensions.Configuration;
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Extensions;

public static class ConfigurationBuilderExtensions
{
    /// <summary>
    /// 执行默认的配置加载，先使用环境变量 settingsUrl 加载远程 json，再使用 appsettings.local.json加载本地 json
    /// </summary>
    /// <param name="builder"></param>
    /// <returns></returns>
    public static IConfigurationBuilder AddDefaultJson(this IConfigurationBuilder builder)
    {
        return builder
                // .AddRemoteJsonFromEnvVar()
                .AddSecrets()
            //.AddLocalJsonFile()
            ;
    }

    public static IConfigurationBuilder AddDefaultJson(this IConfigurationBuilder builder, string envName)
    {
        return builder
            // .AddRemoteJsonFromEnvName(envName)
            .AddSecrets()
            .AddLocalJsonFile();
    }

    /// <summary>
    /// 使用默认的“appsettings.local.json”，增加本地Json文件。建议该文件放在.gitignore中
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    public static IConfigurationBuilder AddLocalJsonFile(this IConfigurationBuilder builder)
    {
        return builder.AddLocalJsonFile("appsettings.local.json");
    }

    /// <summary>
    /// 增加本地Json文件，如果文件不存在，则忽略
    /// </summary>
    /// <param name="builder"></param>
    /// <param name="file"></param>
    /// <returns></returns>
    public static IConfigurationBuilder AddLocalJsonFile(this IConfigurationBuilder builder, string file)
    {
        file.CheckStringIsNullOrEmpty();

        if (File.Exists(file))
            builder.AddJsonFile(file);

        return builder;
    }

    // /// <summary>
    // /// 以环境变量settingsUrl中的值读取远程配置信息。
    // /// </summary>
    // /// <param name="builder"></param>
    // /// <returns></returns>
    // public static IConfigurationBuilder AddRemoteJsonFromEnvVar(this IConfigurationBuilder builder)
    // {
    //     return builder.AddRemoteJsonFromEnvVar("settingsUrl");
    // }
    //
    // /// <summary>
    // /// 从指定的环境变量中获取远程配置的url，并且读取其配置信息
    // /// </summary>
    // /// <param name="builder"></param>
    // /// <param name="variableName"></param>
    // /// <returns></returns>
    // public static IConfigurationBuilder AddRemoteJsonFromEnvVar(this IConfigurationBuilder builder, string variableName)
    // {
    //     if (variableName.IsNotEmpty())
    //     {
    //         string? variableValue = Environment.GetEnvironmentVariable(variableName);
    //
    //         if (variableValue.IsNotEmpty())
    //         {
    //             builder.AddRemoteJson(variableValue!);
    //         }
    //         else
    //         {
    //             var configurationRoot = builder.Build();
    //
    //             string? url =
    //                 configurationRoot[$"SettingsUrl:{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")}"];
    //
    //             if (url.IsNotEmpty())
    //                 builder.AddRemoteJson(SettingsUrlParser.ParseSettingUrl(url!));
    //         }
    //     }
    //
    //     return builder;
    // }

    public static IConfigurationBuilder AddSecrets(this IConfigurationBuilder builder)
    {
        return builder.AddUserSecrets(Assembly.GetExecutingAssembly(), true);
    }
    // public static IConfigurationBuilder AddRemoteJsonFromEnvName(this IConfigurationBuilder builder, string envName)
    // {
    //     if (envName.IsNotEmpty())
    //     {
    //         var configurationRoot = builder.Build();
    //
    //         string? url = configurationRoot[$"SettingsUrl:{envName}"];
    //
    //         if (url.IsNotEmpty())
    //             builder.AddRemoteJson(SettingsUrlParser.ParseSettingUrl(url!));
    //     }
    //
    //     return builder;
    // }
    //
    // /// <summary>
    // /// 从远程url中读取配置信息
    // /// </summary>
    // /// <param name="builder"></param>
    // /// <param name="url"></param>
    // /// <returns></returns>
    // public static IConfigurationBuilder AddRemoteJson(this IConfigurationBuilder builder, string url)
    // {
    //     builder.NullCheck();
    //     url.CheckStringIsNullOrEmpty();
    //
    //     using HttpClient client = new();
    //
    //     using var stream = client.GetStreamAsync(url).Result;
    //
    //     var copyStream = new MemoryStream();
    //
    //     stream.CopyTo(copyStream);
    //
    //     copyStream.Seek(0, SeekOrigin.Begin);
    //
    //     builder.AddJsonStream(copyStream);
    //
    //     return builder;
    // }
}
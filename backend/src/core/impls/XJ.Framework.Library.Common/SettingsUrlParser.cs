using System.Text;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common;

public static class SettingsUrlParser
{
    public static string ParseSettingUrl(string settingUrl)
    {
        if (settingUrl.IsNullOrEmpty())
        {
            return settingUrl;
        }

        //Base64处理逻辑
        try
        {
            return Encoding.UTF8.GetString(Convert.FromBase64String(settingUrl));
        }
        catch (FormatException)
        {
            //Base64转换错误 无需处理 直接返回settingUrl
            return settingUrl;
        }
    }
}
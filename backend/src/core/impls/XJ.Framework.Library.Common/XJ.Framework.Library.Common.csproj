<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>
    <ItemGroup>
        <ProjectReference Include="..\..\abstractions\XJ.Framework.Library.Common.Abstraction\XJ.Framework.Library.Common.Abstraction.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets"/>
    </ItemGroup>
</Project>

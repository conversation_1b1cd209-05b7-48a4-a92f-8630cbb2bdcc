using System.Text.Json.Serialization;

namespace XJ.Framework.Library.AsyncProcess;

public class CommandBody
{
    [<PERSON>sonPropertyName("WorkDirectory")] public string? WorkDirectory { get; set; } = null!;
    [JsonPropertyName("Command")] public string Command { get; set; } = null!;
    [JsonPropertyName("Arguments")] public string Arguments { get; set; } = null!;
    // [JsonPropertyName("Timeout")] public int Timeout { get; set; } = 60000;

    // [JsonPropertyName("EnvironmentVariables")]
    // public Dictionary<string, string>? EnvironmentVariables { get; set; } = null;
}
using SkiaSharp;
using System.Reflection;

namespace XJ.Framework.Library.Image;

public class CaptchaHelper : IDisposable
{
    private Random objRandom = new Random();
    private bool disposed = false;
    private static SKTypeface? _cachedTypeface;
    private static readonly object _typefaceLock = new object();

    #region setting

    /// <summary>
    /// 验证码字符串
    /// </summary>
    public string SetVerifyCodeText { get; set; }

    /// <summary>
    /// 字体大小
    /// </summary>
    public readonly int SetFontSize = 36;

    /// <summary>
    ///  //字体颜色
    /// </summary>
    public SKColor SetFontColor { get; set; } = SKColors.Blue;

    /// <summary>
    /// 字体类型
    /// </summary>
    public string SetFontFamily = "Verdana";

    /// <summary>
    /// 背景色
    /// </summary>
    public SKColor SetBackgroundColor { get; set; } = SKColors.AliceBlue;

    /// <summary>
    /// 是否加入背景线
    /// </summary>
    public bool SetIsBackgroundLine { get; set; }

    /// <summary>
    /// 前景噪点数量
    /// </summary>
    public int SetForeNoisePointCount { get; set; } = 2;

    /// <summary>
    /// 随机码的旋转角度
    /// </summary>
    public int SetRandomAngle { get; set; } = 40;

    /// <summary>
    /// 是否随机字体颜色
    /// </summary>
    public bool SetIsRandomColor { get; set; } = true;

    /// <summary>
    /// 图片宽度
    /// </summary>
    public int SetWith { get; set; } = 200;

    /// <summary>
    /// 图片高度
    /// </summary>
    public int SetHeight { get; set; } = 40;

    #endregion

    #region Constructor Method

    public CaptchaHelper(string verifyCodeText)
    {
        SetVerifyCodeText = verifyCodeText;
        SetRandomAngle = 40;

        SetWith = SetVerifyCodeText.Length * SetFontSize;
        SetHeight = Convert.ToInt32((60.0 / 100) * SetFontSize + SetFontSize);

        InitColors();
    }

    #endregion

    #region Private Method

    /// <summary>
    /// 获取缓存的字体类型，避免重复加载
    /// </summary>
    private static SKTypeface GetCachedTypeface()
    {
        if (_cachedTypeface == null)
        {
            lock (_typefaceLock)
            {
                if (_cachedTypeface == null)
                {
                    using var stream = Assembly.GetExecutingAssembly()
                        .GetManifestResourceStream("XJ.Framework.Library.Image.Fonts.SimHei.ttf");
                    _cachedTypeface = SKFontManager.Default.CreateTypeface(stream);
                }
            }
        }
        return _cachedTypeface;
    }

    /// <summary>
    /// 获取随机颜色
    /// </summary>
    /// <returns></returns>
    private SKColor GetRandomColor()
    {
        var RandomNum_First = new Random((int)DateTime.Now.Ticks);
        // 对于C#的随机数，没什么好说的
        System.Threading.Thread.Sleep(RandomNum_First.Next(50));
        var RandomNum_Sencond = new Random((int)DateTime.Now.Ticks);
        // 为了在白色背景上显示，尽量生成深色
        var int_Red = RandomNum_First.Next(256);
        var int_Green = RandomNum_Sencond.Next(256);
        var int_Blue = (int_Red + int_Green > 400) ? 0 : 400 - int_Red - int_Green;
        int_Blue = (int_Blue > 255) ? 255 : int_Blue;
        return SKColor.FromHsv(int_Red, int_Green, int_Blue);
    }

    #endregion


    #region newCode

    /// <summary>
    /// 干扰线的颜色集合
    /// </summary>
    private List<SKColor> Colors { get; set; } = null!;

    public void InitColors()
    {
        Colors =
        [
            SKColors.AliceBlue,
            SKColors.PaleGreen,
            SKColors.PaleGoldenrod,
            SKColors.Orchid,
            SKColors.OrangeRed,
            SKColors.Orange,
            SKColors.OliveDrab,
            SKColors.Olive,
            SKColors.OldLace,
            SKColors.Navy,
            SKColors.NavajoWhite,
            SKColors.Moccasin,
            SKColors.MistyRose,
            SKColors.MintCream,
            SKColors.MidnightBlue,
            SKColors.MediumVioletRed,
            SKColors.MediumTurquoise,
            SKColors.MediumSpringGreen,
            SKColors.LightSlateGray,
            SKColors.LightSteelBlue,
            SKColors.LightYellow,
            SKColors.Lime,
            SKColors.LimeGreen,
            SKColors.Linen,
            SKColors.PaleTurquoise,
            SKColors.Magenta,
            SKColors.MediumAquamarine,
            SKColors.MediumBlue,
            SKColors.MediumOrchid,
            SKColors.MediumPurple,
            SKColors.MediumSeaGreen,
            SKColors.MediumSlateBlue,
            SKColors.Maroon,
            SKColors.PaleVioletRed,
            SKColors.PapayaWhip,
            SKColors.PeachPuff,
            SKColors.Snow,
            SKColors.SpringGreen,
            SKColors.SteelBlue,
            SKColors.Tan,
            SKColors.Teal,
            SKColors.Thistle,
            SKColors.SlateGray,
            SKColors.Tomato,
            SKColors.Violet,
            SKColors.Wheat,
            SKColors.White,
            SKColors.WhiteSmoke,
            SKColors.Yellow,
            SKColors.YellowGreen,
            SKColors.Turquoise,
            SKColors.LightSkyBlue,
            SKColors.SlateBlue,
            SKColors.Silver,
            SKColors.Peru,
            SKColors.Pink,
            SKColors.Plum,
            SKColors.PowderBlue,
            SKColors.Purple,
            SKColors.Red,
            SKColors.SkyBlue,
            SKColors.RosyBrown,
            SKColors.SaddleBrown,
            SKColors.Salmon,
            SKColors.SandyBrown,
            SKColors.SeaGreen,
            SKColors.SeaShell,
            SKColors.Sienna,
            SKColors.RoyalBlue,
            SKColors.LightSeaGreen,
            SKColors.LightSalmon,
            SKColors.LightPink,
            SKColors.Crimson,
            SKColors.Cyan,
            SKColors.DarkBlue,
            SKColors.DarkCyan,
            SKColors.DarkGoldenrod,
            SKColors.DarkGray,
            SKColors.Cornsilk,
            SKColors.DarkGreen,
            SKColors.DarkMagenta,
            SKColors.DarkOliveGreen,
            SKColors.DarkOrange,
            SKColors.DarkOrchid,
            SKColors.DarkRed,
            SKColors.DarkSalmon,
            SKColors.DarkKhaki,
            SKColors.DarkSeaGreen,
            SKColors.CornflowerBlue,
            SKColors.Chocolate,
            SKColors.AntiqueWhite,
            SKColors.Aqua,
            SKColors.Aquamarine,
            SKColors.Azure,
            SKColors.Beige,
            SKColors.Bisque,
            SKColors.Coral,
            SKColors.Black,
            SKColors.Blue,
            SKColors.BlueViolet,
            SKColors.Brown,
            SKColors.BurlyWood,
            SKColors.CadetBlue,
            SKColors.Chartreuse,
            SKColors.BlanchedAlmond,
            SKColors.Transparent,
            SKColors.DarkSlateBlue,
            SKColors.DarkTurquoise,
            SKColors.IndianRed,
            SKColors.Indigo,
            SKColors.Ivory,
            SKColors.Khaki,
            SKColors.Lavender,
            SKColors.LavenderBlush,
            SKColors.HotPink,
            SKColors.LawnGreen,
            SKColors.LightBlue,
            SKColors.LightCoral,
            SKColors.LightCyan,
            SKColors.LightGoldenrodYellow,
            SKColors.LightGray,
            SKColors.LightGreen,
            SKColors.LemonChiffon,
            SKColors.DarkSlateGray,
            SKColors.Honeydew,
            SKColors.Green,
            SKColors.DarkViolet,
            SKColors.DeepPink,
            SKColors.DeepSkyBlue,
            SKColors.DimGray,
            SKColors.DodgerBlue,
            SKColors.Firebrick,
            SKColors.GreenYellow,
            SKColors.FloralWhite,
            SKColors.Fuchsia,
            SKColors.Gainsboro,
            SKColors.GhostWhite,
            SKColors.Gold,
            SKColors.Goldenrod,
            SKColors.Gray,
            SKColors.ForestGreen
        ];
    }

    /// <summary>
    /// 创建画笔
    /// </summary>
    /// <param name="color"></param>
    /// <param name="fontSize"></param>
    /// <returns></returns>
    private SKPaint CreatePaint(SKColor color, float fontSize)
    {
        return new SKPaint
        {
            IsAntialias = true,
            Color = color
        };
    }

    /// <summary>
    /// 获取验证码
    /// </summary>
    /// <param name="captchaText">验证码文字</param>
    /// <param name="width">图片宽度</param>
    /// <param name="height">图片高度</param>
    /// <param name="lineNum">干扰线数量</param>
    /// <param name="lineStrookeWidth">干扰线宽度</param>
    /// <returns></returns>
    public byte[] GetVerifyCodeImage(int lineNum = 6, int lineStrookeWidth = 2)
    {
        
        using (var image2d = new SKBitmap(SetWith, SetHeight, SKColorType.Bgra8888, SKAlphaType.Premul))
        {
            using (var canvas = new SKCanvas(image2d))
            {
                if (SetIsRandomColor)
                {
                    SetFontColor = GetRandomColor();
                }

                canvas.Clear(SetBackgroundColor);

                AddForeNoisePoint(image2d);
                AddBackgroundNoisePoint(image2d, canvas);

                // 使用缓存的字体类型
                var typeface = GetCachedTypeface();
                using (var font = new SKFont(typeface, SetFontSize))
                using (var paint = new SKPaint
                {
                    IsAntialias = true,
                    Color = SetFontColor
                })
                {
                    var chars = SetVerifyCodeText.ToCharArray();
                    for (var i = 0; i < chars.Length; i++)
                    {
                        float angle = objRandom.Next(-30, 30);
                        
                        // 保存当前Canvas状态
                        canvas.Save();
                        
                        float px = ((i) * SetFontSize);
                        float py = (SetHeight) / 2;

                        // 应用变换
                        canvas.Translate(px + 12, py + 12);
                        canvas.RotateDegrees(angle);

                        // 绘制文字（在原点绘制，因为已经通过Translate移动了位置）
                        canvas.DrawText(chars[i].ToString(), 0, 0, font, paint);

                        // 恢复Canvas状态
                        canvas.Restore();
                    }
                }

                using (var disturbStyle = new SKPaint())
                {
                    var random = new Random();
                    for (var i = 0; i < lineNum; i++)
                    {
                        disturbStyle.Color = Colors[random.Next(Colors.Count)];
                        disturbStyle.StrokeWidth = lineStrookeWidth;
                        canvas.DrawLine(random.Next(0, SetWith), random.Next(0, SetHeight), random.Next(0, SetWith),
                            random.Next(0, SetHeight), disturbStyle);
                    }
                }

                using (var img = SKImage.FromBitmap(image2d))
                {
                    using (var p = img.Encode(SKEncodedImageFormat.Png, 100))
                    {
                        var result = p.ToArray();
                        return result;
                    }
                }
            }
        }
    }

    /// <summary>
    /// 获取验证码
    /// </summary>
    /// <param name="captchaText">验证码文字</param>
    /// <param name="width">图片宽度</param>
    /// <param name="height">图片高度</param>
    /// <param name="lineNum">干扰线数量</param>
    /// <param name="lineStrookeWidth">干扰线宽度</param>
    /// <returns></returns>
    public string GetVerifyCodeImageBase64(int lineNum = 15, int lineStrookeWidth = 5)
    {
        return Convert.ToBase64String(GetVerifyCodeImage(lineNum, lineStrookeWidth));
    }

    private void AddForeNoisePoint(SKBitmap objBitmap)
    {
        for (var i = 0; i < objBitmap.Width * SetForeNoisePointCount; i++)
        {
            objBitmap.SetPixel(objRandom.Next(objBitmap.Width), objRandom.Next(objBitmap.Height), SetFontColor);
        }
    }

    private void AddBackgroundNoisePoint(SKBitmap objBitmap, SKCanvas objGraphics)
    {
        using (var objPaint = CreatePaint(SKColors.Azure, 0))
        {
            for (var i = 0; i < objBitmap.Width * 2; i++)
            {
                objGraphics.DrawRect(objRandom.Next(objBitmap.Width), objRandom.Next(objBitmap.Height), 1, 1, objPaint);
            }
        }

        if (SetIsBackgroundLine)
        {
            for (var i = 0; i < 12; i++)
            {
                var x1 = objRandom.Next(objBitmap.Width);
                var x2 = objRandom.Next(objBitmap.Width);
                var y1 = objRandom.Next(objBitmap.Height);
                var y2 = objRandom.Next(objBitmap.Height);

                using (var paint = new SKPaint { Color = SKColors.Silver })
                {
                    objGraphics.DrawLine(x1, y1, x2, y2, paint);
                }
            }
        }
    }

    #endregion

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                // 释放托管资源
                objRandom = null;
            }
            // 释放非托管资源
        }
        disposed = true;
    }

    /// <summary>
    /// 释放静态资源（应用程序关闭时调用）
    /// </summary>
    public static void DisposeStaticResources()
    {
        lock (_typefaceLock)
        {
            if (_cachedTypeface != null)
            {
                _cachedTypeface.Dispose();
                _cachedTypeface = null;
            }
        }
    }

    ~CaptchaHelper()
    {
        Dispose(false);
    }
}

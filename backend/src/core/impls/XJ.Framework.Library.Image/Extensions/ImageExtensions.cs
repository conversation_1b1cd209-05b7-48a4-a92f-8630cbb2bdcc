using SkiaSharp;

namespace XJ.Framework.LIbrary.Image.Extensions;

public static class ImageExtensions
{
    public static SKImage CropImage(this SKImage image,
        decimal leftOffsetPercent,
        decimal rightOffsetPercent,
        decimal topOffsetPercent,
        decimal bottomOffsetPercent)
    {
        leftOffsetPercent = leftOffsetPercent < 0 ? 0 : leftOffsetPercent;
        rightOffsetPercent = rightOffsetPercent < 0 ? 0 : rightOffsetPercent;
        topOffsetPercent = topOffsetPercent < 0 ? 0 : topOffsetPercent;
        bottomOffsetPercent = bottomOffsetPercent < 0 ? 0 : bottomOffsetPercent;

        var originalWidth = image.Width;
        var originalHeight = image.Height;


        var leftOffset = (float)(leftOffsetPercent * originalWidth / 100);
        var rightOffset = (float)(rightOffsetPercent * originalWidth / 100);
        var topOffset = (float)(topOffsetPercent * originalHeight / 100);
        var bottomOffset = (float)(bottomOffsetPercent * originalHeight / 100);

        var width = (int)(image.Width - leftOffset - rightOffset);
        var height = (int)(image.Height - topOffset - bottomOffset);

        var rect = new SKRect(leftOffset, topOffset, width - rightOffset, height - bottomOffset);


        // var canvas = new SKCanvas(bitmap);


        var subset = image.Subset(SKRectI.Create((int)leftOffset, (int)topOffset, width, height));

        return subset;
    }

    public static SKImage ToImage(this byte[] bytes)
    {
        return SKImage.FromEncodedData(bytes);
    }

    public static byte[]? ToBytes(this SKImage image, SKEncodedImageFormat? format)
    {
        return format == null ? null : image.Encode(format.Value, 100).ToArray();
    }

    // public static byte[] ToBytes(this SKBitmap bitmap, SKEncodedImageFormat format)
    // {
    //     return bitmap.Encode(format, 100).ToArray();
    // }
    //
    public static SKEncodedImageFormat? GetImageFormat(string fileName)
    {
        var ext = Path.GetExtension(fileName);
        if (ext.Equals(".png", StringComparison.OrdinalIgnoreCase))
        {
            return SKEncodedImageFormat.Png;
        }
        else if (ext.Equals(".jpg", StringComparison.OrdinalIgnoreCase))
        {
            return SKEncodedImageFormat.Jpeg;
        }
        else if (ext.Equals(".jpeg", StringComparison.OrdinalIgnoreCase))
        {
            return SKEncodedImageFormat.Jpeg;
        }
        else if (ext.Equals(".bmp", StringComparison.OrdinalIgnoreCase))
        {
            return SKEncodedImageFormat.Bmp;
        }
        else if (ext.Equals(".heif", StringComparison.OrdinalIgnoreCase))
        {
            return SKEncodedImageFormat.Heif;
        }

        // else if (ext.Equals(".tiff", StringComparison.OrdinalIgnoreCase))
        // {
        //     return SKEncodedImageFormat.;
        // }
        //".JPEG", ".JPG", ".PNG", ".BMP", ".TIFF", ".HEIF"
        // return SKEncodedImageFormat.Jpeg;
        return null;
    }
}
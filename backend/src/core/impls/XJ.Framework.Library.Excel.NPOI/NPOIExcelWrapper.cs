using NPOI.SS.UserModel;
using NPOI.SS.Util;
using NPOI.XSSF.UserModel;
using System.Globalization;
using XJ.Framework.Library.Excel.Abstraction;

namespace XJ.Framework.Library.Excel.NPOI;

public class NpoiExcelWrapper : ExcelWrapper<IWorkbook, ISheet, IRow, ICell, ICellStyle>
{
    // public NpoiExcelWrapper(string filePath) : base(filePath)
    // {
    // }

    public override IWorkbook GetWorkBook(MemoryStream stream)
    {
        return new XSSFWorkbook(stream);
    }

    public override int GetSheetCount(IWorkbook workbook)
    {
        return workbook.NumberOfSheets;
    }

    public override IEnumerable<ISheet> GetSheets(IWorkbook workbook, bool filterVisible = true)
    {
        var sheets = new List<ISheet>();
        var sheetCount = GetSheetCount(workbook);
        for (var i = 0; i < sheetCount; i++)
        {
            var sheet = workbook.GetSheetAt(i);
            if (!filterVisible || !IsSheetHidden(workbook, i))
                sheets.Add(sheet);
        }

        return sheets;
    }

    public override IRow GetRow(ICell cell)
    {
        return cell.Row;
    }

    public override ISheet GetSheet(IRow row)
    {
        return row.Sheet;
    }

    public override int GetCellIndex(ICell cell)
    {
        return cell.ColumnIndex;
    }

    public override int GetRowIndex(IRow row)
    {
        return row.RowNum;
    }

    public override IEnumerable<ICell> GetCells(IRow row)
    {
        return row.Cells;
    }

    public override int GetLastRowNum(ISheet sheet)
    {
        return sheet.LastRowNum;
    }

    public override int GetLastCellNum(IRow row)
    {
        return row.LastCellNum;
    }

    public override ISheet GetSheet(ICell cell)
    {
        return cell.Sheet;
    }


    public override bool IsSheetHidden(IWorkbook workbook, int sheetIndex)
    {
        return workbook.IsSheetHidden(sheetIndex);
    }

    public override Tuple<int, int> GetCellAddress(string cellAddress)
    {
        var reference = new CellReference(cellAddress);
        return new Tuple<int, int>(reference.Row, reference.Col);
    }


    public override ICell CreateCell(IRow row, int cellIndex)
    {
        return row.CreateCell(cellIndex);
    }


    public override ICell? GetCell(IRow row, int cellIndex)
    {
        return row.GetCell(cellIndex);
    }

    public override string GetCellStringValue(ISheet sheet, string cellAddress)
    {
        var address = GetCellAddress(cellAddress);

        var row = GetOrCreateRow(sheet, address.Item1);

        var cell = GetOrCreateCell(row, address.Item2);

        return GetCellStringValue(cell);
    }

    public override string GetCellStringValue(IRow row, int cellIndex)
    {
        var cell = GetCell(row, cellIndex);

        return GetCellStringValue(cell);
    }

    public override string GetCellStringValue(ICell? cell)
    {
        if (cell == null)
            return string.Empty;

        string cellStringValue;
        switch (cell.CellType)
        {
            case CellType.Boolean:
                cellStringValue = cell.BooleanCellValue.ToString();
                break;
            case CellType.String:
                cellStringValue = cell.StringCellValue;
                break;
            case CellType.Numeric:
                cellStringValue = DateUtil.IsCellDateFormatted(cell)
                    ? cell.DateCellValue.ToString("yyyy-MM-dd HH:mm:ss")
                    : cell.NumericCellValue.ToString(CultureInfo.CurrentCulture);
                break;
            default:
                cellStringValue = string.Empty;
                break;
        }

        return cellStringValue;
    }

    public override ICell SetWrapText(ICell cell, bool wrap)
    {
        cell.CellStyle.WrapText = true;
        return cell;
    }

    public override ICell SetMergeRange(ISheet sheet, int firstRow, int lastRow, int firstCol, int lastCol)
    {
        if (lastRow > firstRow)
        {
            sheet.AddMergedRegion(new CellRangeAddress(firstRow, lastRow, firstCol, lastCol));
        }

        var row = GetOrCreateRow(sheet, firstRow);

        return GetOrCreateCell(row, firstCol);
    }

    public override ICell SetMergeRange(ICell cell, int rowCount, int columnCount)
    {
        var cellRangeAddress = new CellRangeAddress(cell.RowIndex, cell.RowIndex + rowCount - 1, cell.ColumnIndex,
            cell.ColumnIndex + columnCount - 1);

        return SetMergeRange(GetSheet(cell), cellRangeAddress.FirstRow, cellRangeAddress.LastRow,
            cellRangeAddress.FirstColumn, cellRangeAddress.LastColumn);
    }

    public override ICell SetCellStyle(ICell cell, ICellStyle cellStyle)
    {
        cell.CellStyle = cellStyle;
        return cell;
    }

    public override ISheet SetColumnWidthUnit(ISheet sheet, int columnIndex, int columnWidth)
    {
        sheet.SetColumnWidth(columnIndex, columnWidth * 256);
        return sheet;
    }

    public override ISheet SetDefaultTableStyle(ISheet sheet, int beginRow, int beginColumn, int headerCount,
        int dataCount,
        int tableWidth)
    {
        var headerCellStyle = GetHeaderCellStyle(sheet);
        var dataCellStyle = GetDataCellStyle(sheet);

        for (var i = 0; i < headerCount; i++)
        {
            var row = GetOrCreateRow(sheet, beginRow + i);
            for (var j = 0; j < tableWidth; j++)
            {
                var cell = GetOrCreateCell(row, beginColumn + j);
                SetCellStyle(cell, headerCellStyle);
            }
        }

        for (var i = 0; i < dataCount; i++)
        {
            var row = GetOrCreateRow(sheet, beginRow + headerCount + i);
            for (var j = 0; j < tableWidth; j++)
            {
                var cell = GetOrCreateCell(row, beginColumn + j);
                SetCellStyle(cell, dataCellStyle);
            }
        }

        return sheet;
    }

    public override ICellStyle GetHeaderCellStyle(ISheet sheet)
    {
        var font = sheet.Workbook.CreateFont();

        var cellStyle = sheet.Workbook.CreateCellStyle();
        cellStyle.BorderBottom = BorderStyle.Thin;
        cellStyle.BorderTop = BorderStyle.Thin;
        cellStyle.BorderLeft = BorderStyle.Thin;
        cellStyle.BorderRight = BorderStyle.Thin;
        cellStyle.FillPattern = FillPattern.SolidForeground;
        cellStyle.FillForegroundColor = IndexedColors.RoyalBlue.Index;
        cellStyle.FillBackgroundColor = IndexedColors.White.Index;
        cellStyle.Alignment = HorizontalAlignment.Center;
        cellStyle.VerticalAlignment = VerticalAlignment.Center;

        font.Color = IndexedColors.White.Index;
        font.IsBold = true;
        cellStyle.SetFont(font);

        return cellStyle;
    }

    public override ICellStyle GetDataCellStyle(ISheet sheet)
    {
        var font = sheet.Workbook.CreateFont();
        font.Color = IndexedColors.Black.Index;

        var cellStyle = sheet.Workbook.CreateCellStyle();
        cellStyle.BorderBottom = BorderStyle.Thin;
        cellStyle.BorderTop = BorderStyle.Thin;
        cellStyle.BorderLeft = BorderStyle.Thin;
        cellStyle.BorderRight = BorderStyle.Thin;
        cellStyle.Alignment = HorizontalAlignment.Left;
        cellStyle.VerticalAlignment = VerticalAlignment.Center;

        cellStyle.SetFont(font);

        return cellStyle;
    }

    public override IRow? GetRow(ISheet sheet, int rowIndex)
    {
        return sheet.GetRow(rowIndex);
    }

    public override IRow CreateRow(ISheet sheet, int rowIndex)
    {
        return sheet.CreateRow(rowIndex);
    }
}
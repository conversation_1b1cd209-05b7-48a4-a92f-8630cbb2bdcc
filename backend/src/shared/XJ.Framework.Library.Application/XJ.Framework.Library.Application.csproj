<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\Common.props"/>
    <ItemGroup>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Cache.Abstraction\XJ.Framework.Library.Cache.Abstraction.csproj" />
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.DistributedLock\XJ.Framework.Library.DistributedLock.csproj" />
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Cache\XJ.Framework.Library.Cache.csproj" />
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.DistributedLock.Cache\XJ.Framework.Library.DistributedLock.Cache.csproj" />
<!--        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Image\XJ.Framework.Library.Image.csproj" />-->
        <ProjectReference Include="..\..\domains\rbac\XJ.Framework.Rbac.ApiClient\XJ.Framework.Rbac.ApiClient.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Infrastructure.ApiClient\XJ.Framework.Library.Infrastructure.ApiClient.csproj" />
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="AutoMapper"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions"/>
    </ItemGroup>

</Project>

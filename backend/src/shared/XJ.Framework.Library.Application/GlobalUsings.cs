// global using 指令

// System 命名空间

// 第三方库
global using AutoMapper;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;

// XJ.Framework 基础库
global using XJ.Framework.Library.Application.Contract.Extensions;
global using XJ.Framework.Library.Application.Contract.Interfaces;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Domain.Shared.Dtos;
global using XJ.Framework.Library.Domain.UOW;
global using XJ.Framework.Library.EntityFrameworkCore;
global using XJ.Framework.Library.EntityFrameworkCore.Extensions;
global using XJ.Framework.Library.Domain.Entities;
global using XJ.Framework.Library.Domain.Repositories.Interfaces;

global using XJ.Framework.Library.Application.Contract.OperationDtos;
global using XJ.Framework.Library.Common.Abstraction.Extensions;
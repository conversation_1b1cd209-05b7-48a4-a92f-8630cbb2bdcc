using Microsoft.Extensions.Options;
using System.Text.Json;

namespace XJ.Framework.Library.Application.Mappers;

public class MapperHelper
{
    public static JsonSerializerOptions JsonSerializerOptions = null!;

    public static FileDto? ParseFileDto(string? json)
    {
        return string.IsNullOrEmpty(json)
            ? null
            : JsonSerializer.Deserialize<FileDto>(json, JsonSerializerOptions);
    }
}
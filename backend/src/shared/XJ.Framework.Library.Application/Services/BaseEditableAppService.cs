using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Interfaces;

namespace XJ.Framework.Library.Application.Services;

public abstract class BaseEditableAppService<TKey, TEntity, TDto, TOperationDto, TRepository, TQueryCriteria> :
    BaseAppService<
        TKey, TEntity, TDto, TRepository, TQueryCriteria>
    where TEntity : BaseEntity<TKey>
    where TDto : BaseDto<TKey>
    where TOperationDto : BaseOperationDto
    where TRepository : IEditableRepository<TKey, TEntity>
    where TKey : struct
    where TQueryCriteria : BaseQueryCriteria
{
    protected readonly IUnitOfWork UnitOfWork;

    protected readonly IKeyGenerator<TKey> KeyGenerator;
    protected readonly ICurrentUserContext CurrentUserContext;

    protected BaseEditableAppService(TRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<TKey> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper)
    {
        UnitOfWork = unitOfWork;
        KeyGenerator = keyGenerator;
        CurrentUserContext = currentUserContext;
    }

    protected async virtual Task<IEnumerable<TEntity>> GetEntitiesAsync(IEnumerable<TOperationDto> dtos)
    {
        return await Task.FromResult(Mapper.Map<List<TEntity>>(dtos));
    }

    protected async virtual Task<TEntity?> GetEntityAsync(TOperationDto? dto)
    {
        return await Task.FromResult(Mapper.Map<TEntity>(dto));
    }


    public async Task<bool> CreateAsync(TOperationDto dto)
    {
        var entity = (await GetEntityAsync(dto))!;
        entity.Key = KeyGenerator.GenerateKey();
        return await Repository.InsertAsync(entity);
    }

    public async Task<bool> CreateAsync(TKey key, TOperationDto dto)
    {
        var entity = (await GetEntityAsync(dto))!;
        entity.Key = key;
        return await Repository.InsertAsync(entity);
    }

    public async Task<bool> CreateAsync(List<TOperationDto> dtos)
    {
        var entities = (await GetEntitiesAsync(dtos)).ToList();
        entities.ForEach(q => q.Key = KeyGenerator.GenerateKey());
        return await Repository.InsertAsync(entities);
    }

    // public async Task<bool> UpdateAsync(IEnumerable<TDto> dto)
    // {
    //     return await Repository.UpdateAsync(await GetEntitiesAsync(dto));
    // }

    // public async Task<bool> CreateAsync(IEnumerable<TOperationDto> dto)
    // {
    //     return await Repository.InsertAsync(await GetEntitiesAsync(dto));
    // }

    public async Task<bool> UpdateAsync(TKey key, TOperationDto dto)
    {
        // 从数据库获取现有实体
        var entity = await Repository.GetAsync(q => q.Key.Equals(key));

        entity.NullCheck();

        // 将DTO的值映射到现有实体上
        Mapper.Map(dto, entity);

        // 确保Key不变
        entity!.Key = key;

        return await Repository.UpdateAsync(entity);
    }

    public async Task<bool> DeleteAsync(TKey id)
    {
        var entity = await Repository.GetAsync(q => q.Key.Equals(id));

        entity.NullCheck();

        return entity != null && await Repository.DeleteAsync(entity);
    }
}
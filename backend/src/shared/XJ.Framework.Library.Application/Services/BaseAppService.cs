namespace XJ.Framework.Library.Application.Services;

public class
    BaseAppService<TK<PERSON>, T<PERSON><PERSON><PERSON>, TDto, TRepository, TQueryCriteria> : IAppService<TKey, TDto,
    TQueryCriteria>
    where TEntity : BaseEntity<TKey>
    where TDto : BaseDto<TKey>
    where TRepository : IRepository<TKey, TEntity>
    where TKey : struct
    where TQueryCriteria : BaseQueryCriteria
{
    protected readonly TRepository Repository;

    protected readonly IMapper Mapper;


    protected BaseAppService(TRepository repository, IMapper mapper)
    {
        Repository = repository;
        Mapper = mapper;
        // Logger = logger;
    }


    public async Task<TDto?> GetByIdAsync(TKey id)
    {
        return await GetDtoAsync(await Repository.GetAsync(q => q.Key.Equals(id)));
    }

    public async Task<IEnumerable<TDto>> GetListAsync(TQueryCriteria criteria)
    {
        var expression = criteria.BuildExpression<TK<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TQueryCriteria>();
        return await GetDtosAsync(await Repository.GetListAsync(expression));
    }

    public async Task<PageDtoData<TKey, TDto>> GetPageAsync(PagedQueryCriteria<TQueryCriteria> criteria)
    {
        var whereExpr = criteria.Condition.BuildExpression<TKey, TEntity, TQueryCriteria>();

        var orderbyExpr = criteria.BuildOrderExpression<TKey, TEntity, TQueryCriteria>();

        return await ConvertPageDataAsync(await Repository.GetPageAsync(
            whereExpr,
            criteria.PageParams.ToRowIndex(),
            criteria.PageParams.PageSize,
            orderbyExpr
        ));
    }

    protected async virtual Task<PageDtoData<TKey, TDto>> ConvertPageDataAsync(PageData<TKey, TEntity> pageData)
    {
        return new PageDtoData<TKey, TDto>
        {
            Totals = pageData.Totals,
            Rows = await GetDtosAsync(pageData.Rows)
        };
    }

    protected async virtual Task<IEnumerable<TDto>> GetDtosAsync(IEnumerable<TEntity> entities)
    {
        return await Task.FromResult(Mapper.Map<List<TDto>>(entities));
    }

    protected async virtual Task<List<TDto>> GetDtosAsync(List<TEntity> entities)
    {
        return await Task.FromResult(Mapper.Map<List<TDto>>(entities));
    }

    protected async virtual Task<IEnumerable<TEntity>> GetEntitiesAsync(IEnumerable<TDto> dtos)
    {
        return await Task.FromResult(Mapper.Map<List<TEntity>>(dtos));
    }

    protected async virtual Task<TDto?> GetDtoAsync(TEntity? entity)
    {
        return await Task.FromResult(Mapper.Map<TDto>(entity));
    }

    protected async virtual Task<TEntity?> GetEntityAsync(TDto? dto)
    {
        return await Task.FromResult(Mapper.Map<TEntity>(dto));
    }
}
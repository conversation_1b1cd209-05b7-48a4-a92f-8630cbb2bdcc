using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Serilog;
using XJ.Framework.Library.Application.Mappers;
using XJ.Framework.Library.Application.Options;
using XJ.Framework.Library.Application.Services;
using XJ.Framework.Library.Cache;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.DistributedLock.Cache;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Infrastructure.ApiClient.Extensions;

namespace XJ.Framework.Library.Application.Extensions;

public static class ApplicationExtensions
{
    public static IServiceCollection
        InitApplication<TApplicationWrapper, TInfraWrapper>(
            this IServiceCollection services,
            IConfigurationRoot configuration)
        where TApplicationWrapper : ApplicationWrapper, new()
        where TInfraWrapper : InfrastructureWrapper, new()
    {
        var env = services.BuildServiceProvider().GetRequiredService<IWebHostEnvironment>();


        services.AddAutoMapper(typeof(TApplicationWrapper).Assembly);

        MapperHelper.JsonSerializerOptions = services.BuildServiceProvider()
            .GetRequiredService<IOptions<JsonOptions>>()
            .Value.JsonSerializerOptions;


        var serviceTypes = typeof(TApplicationWrapper).Assembly.GetTypes()
            .Where(t => t is { IsClass: true, IsAbstract: false } &&
                        t.GetInterfaces().Any(i => i.Name.EndsWith("Service")))
            .ToList();

        foreach (var implementationType in serviceTypes)
        {
            var interfaceType = implementationType.GetInterfaces().FirstOrDefault(i => i.Name.EndsWith("Service"));
            if (interfaceType != null)
            {
                services.AddScoped(interfaceType, implementationType);
            }
        }


        services.AddIdGenerator(configuration);

        var cacheConfiguration = new ConfigurationBuilder()
            .AddSolutionJsonFile($"cache.json")
            .AddSolutionJsonFile($"cache.{env.EnvironmentName}.json")
            .Build();

        services.AddPolymorphicCache(cacheConfiguration);

        services.AddCacheBaseDistributedLock(cacheConfiguration);

        services.AddApiClientInfrastructure();

        var wrapper = new TApplicationWrapper();

        wrapper.Init(services, configuration);

        services.InitInfra<TInfraWrapper>(configuration);

        return services;
    }
}

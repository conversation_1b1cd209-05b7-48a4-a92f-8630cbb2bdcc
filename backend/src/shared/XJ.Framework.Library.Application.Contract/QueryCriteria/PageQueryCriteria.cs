namespace XJ.Framework.Library.Application.Contract.QueryCriteria;

/// <summary>
/// 带分页查询的查询条件类
/// </summary>
/// <typeparam name="TCondition">查询条件类的类型</typeparam>
public class PagedQueryCriteria<TCondition> : IPagedQueryCriteria
    where TCondition : BaseQueryCriteria
{
    /// <summary>
    /// 查询条件类
    /// </summary>
    [NotMapped]
    public TCondition Condition {
        get;
        set;
    } = default!;

    /// <summary>
    /// 分页参数
    /// </summary>
    [NotMapped]
    public IPageRequestParams PageParams {
        get;
        set;
    } = new PageRequestParams();

    /// <summary>
    /// 排序
    /// </summary>
    [NotMapped]
    public IOrderByRequestItem[] OrderBy {
        get;
        set;
    } = Array.Empty<OrderByRequestItem>();

    public Type GetConditionType()
    {
        return typeof(TCondition);
    }

    object IPagedQueryCriteria.InitCondition()
    {
        this.Condition = (TCondition)TypeCreator.CreateInstance(this.GetConditionType())!;

        return this.Condition;
    }

    public void CopyTo(IPagedQueryCriteria target)
    {
        target.NullCheck();

        target.PageParams = this.PageParams;
        target.OrderBy = this.OrderBy;
    }
}
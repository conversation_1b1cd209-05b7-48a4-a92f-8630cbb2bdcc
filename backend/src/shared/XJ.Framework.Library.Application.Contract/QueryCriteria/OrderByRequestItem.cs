namespace XJ.Framework.Library.Application.Contract.QueryCriteria;

/// <summary>
/// OrderBy的参数
/// </summary>
public class OrderByRequestItem : IOrderByRequestItem
{
    public OrderByRequestItem()
    {
    }

    public OrderByRequestItem(string dataField, FieldSortDirection sortDirection)
    {
        this.DataField = dataField;
        this.SortDirection = sortDirection;
    }


    /// <summary>
    /// 数据字段
    /// </summary>
    public string DataField {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 排序方向
    /// </summary>
    public FieldSortDirection SortDirection {
        get;
        set;
    }

    /// <summary>
    /// 从源复制
    /// </summary>
    /// <param name="source"></param>
    public void CopyFrom(IOrderByRequestItem source)
    {
        if (source != null)
        {
            this.DataField = source.DataField;
            this.SortDirection = source.SortDirection;
        }
    }
}
<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\Common.props"/>
    <ItemGroup>
        <ProjectReference Include="..\XJ.Framework.Library.EntityFrameworkCore\XJ.Framework.Library.EntityFrameworkCore.csproj"/>
    </ItemGroup>
    <ItemGroup>
        <Compile Remove="Interfaces\IBaseAuditService.cs"/>
        <Compile Remove="Interfaces\IBaseSoftDeleteService.cs"/>
    </ItemGroup>

</Project>

using XJ.Framework.Library.Application.Contract.OperationDtos;

namespace XJ.Framework.Library.Application.Contract.Interfaces;

public interface
    IEditableAppService<in TKey, TOperationDto>
    where TOperationDto : BaseOperationDto
    where TK<PERSON> : notnull
{
    Task<bool> CreateAsync(TOperationDto entity);

    Task<bool> CreateAsync(TKey key, TOperationDto dto);

    Task<bool> CreateAsync(List<TOperationDto> dtos);

    // Task<bool> CreateAsync(IEnumerable<TOperationDto> entities);

    Task<bool> UpdateAsync(TKey id, TOperationDto entity);

    Task<bool> DeleteAsync(TKey id);
}
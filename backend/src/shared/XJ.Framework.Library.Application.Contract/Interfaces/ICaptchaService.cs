namespace XJ.Framework.Library.Application.Contract.Interfaces;

/// <summary>
/// 验证码服务接口
/// </summary>
public interface ICaptchaService
{
    Task<byte[]> GenerateCaptchaImageAsync(string captchaCode);

    Task<int> GetExpirationInSecondsAsync();


    KeyValuePair<string, string> GenerateCaptchaContent();


    /// <summary>
    /// 存储验证码结果
    /// </summary>
    Task StoreCaptchaAsync(string captchaId, string captchaContent);

    /// <summary>
    /// 移除验证码存储结果
    /// </summary>
    /// <returns></returns>
    Task RemoveCaptchaAsync(string captchaId);

    /// <summary>
    /// 获取验证码结果
    /// </summary>
    Task<string> GetCaptchaAsync(string captchaId);

    /// <summary>
    /// 生成验证码
    /// </summary>
    /// <returns>验证码信息</returns>
    Task<CaptchaDto> GenerateCaptchaAsync();


    /// <summary>
    /// 验证验证码
    /// </summary>
    /// <param name="captchaId">验证码ID</param>
    /// <param name="captchaCode">验证码</param>
    /// <returns>是否有效</returns>
    Task<bool> ValidateCaptchaAsync(string captchaId, string captchaCode);
}

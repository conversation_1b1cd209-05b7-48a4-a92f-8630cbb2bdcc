using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Collections;
using System.Collections.Specialized;
using System.Net;
using System.Net.Http;
using System.Net.Http.Json;
using System.Security.Authentication;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Common.Abstraction.Models;
using XJ.Framework.Library.Domain.Shared.Interfaces;

namespace XJ.Framework.Library.Infrastructure.ApiClient.Base;

public abstract class BaseApiClient
{
    protected readonly HttpClient _httpClient;
    private readonly ILogger<BaseApiClient> _logger;
    private readonly IAuthInfoGetter _authInfoGetter;
    private readonly IContextContainer _contextContainer;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    protected BaseApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger, IAuthInfoGetter authInfoGetter,
        IContextContainer contextContainer,
        ICurrentUserContext currentUserContext, IOptions<JsonOptions> jsonOptions)
    {
        _httpClient = httpClient;
        _logger = logger;
        _authInfoGetter = authInfoGetter;
        _contextContainer = contextContainer;
        _currentUserContext = currentUserContext;
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }


    public NameValueCollection BuildCriteriaNameValueCollection<TQueryCriteria>(
        PagedQueryCriteria<TQueryCriteria> criteria)
        where TQueryCriteria : BaseQueryCriteria
    {
        var query = BuildCriteriaNameValueCollection(criteria.Condition);

        query.Add("$pageIndex", criteria.PageParams.PageIndex.ToString());
        query.Add("$pageSize", criteria.PageParams.PageSize.ToString());

        if (criteria.OrderBy.Any())
        {
            foreach (var orderBy in criteria.OrderBy)
            {
                query.Add($"$sortBy", orderBy.DataField);
                query.Add($"$orderBy", orderBy.SortDirection == FieldSortDirection.Ascending ? "asc" : "desc");
            }
        }

        return query;
    }

    public NameValueCollection BuildCriteriaNameValueCollection<TQueryCriteria>(
        TQueryCriteria criteria)
        where TQueryCriteria : BaseQueryCriteria
    {
        var query = new NameValueCollection();
        criteria.GetType().GetProperties().ForEach(property =>
        {
            var propertyName = property.Name; //首字母小写
            if (propertyName.Length > 1)
            {
                propertyName = char.ToLower(propertyName[0]) + propertyName.Substring(1);
            }

            var value = property.GetValue(criteria);
            if (value == null) return;


            //这里要判断 如果是枚举类型 则转换为int
            //如果是List IEnumerable等集合类型则添加多个query name都是[]格式 比如status[] 
            //如果是字典 则形如 query.Add($"dynamicQueries[{dynamicQuery.Key}]", dynamicQuery.Value);
            //如果是集合对象 则形如 query.Add(propertyName, value.ToString()!);
            if (value is Enum e)
            {
                query.Add(propertyName, Convert.ToInt32(e).ToString());
            }
            else if (value is ICollection collection)
            {
                foreach (var item in collection)
                {
                    query.Add($"{propertyName}[]", item.ToString()!);
                }
            }
            else
            {
                query.Add(propertyName, value.ToString()!);
            }
        });
        return query;
    }

    protected async Task<byte[]> GetByteArrayAsync(string url, Dictionary<string, string>? headers = null,
        CancellationToken cancellationToken = default)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, url);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request, cancellationToken);

        return await response.Content.ReadAsByteArrayAsync(cancellationToken);
    }

    protected async Task<T> InternalGetAsync<T>(string url, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Get, url);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }


    private async Task<T> HandleInternalResponseAsync<T>(HttpResponseMessage? response)
    {
        response.NullCheck();

        var responseMessage = await response!.Content.ReadAsStringAsync();


        JsonSerializerOptions options = new()
        {
            PropertyNameCaseInsensitive = false,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters =
            {
                new LongToStringConverter(),
                new ServiceResponseConverter<T>()
            }
        };
        ServiceResponse<T> serviceResponse;
        try
        {
            serviceResponse =
                JsonSerializer.Deserialize<ServiceResponse<T>>(responseMessage, options)!;
        }
        catch (Exception e)
        {
            throw new Exception($"{response.RequestMessage!.RequestUri!.PathAndQuery} {e.Message}", e);
        }


        (response.StatusCode == HttpStatusCode.BadRequest).TrueThrow<ValidationException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.Unauthorized)
            .TrueThrow<AuthenticationException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.Forbidden).TrueThrow<AuthorizationException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.NotFound).TrueThrow<NotFoundException>(serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.TooManyRequests).TrueThrow<RateLimitingException>(
            serviceResponse.Message);
        (response.StatusCode == HttpStatusCode.OK).FalseThrow<SystemSupportException>(serviceResponse.Message);
        return serviceResponse.Data!;
    }

    protected async Task<T> InternalPostAsync<T>(string url, object? data, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, url);

        request.Content = JsonContent.Create(data, options: _jsonSerializerOptions);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }

    protected async Task<T> InternalPostFileAsync<T>(string url, MultipartContent content,
        Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Post, url);

        request.Content = content;

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }


    protected async Task<T> InternalPutAsync<T>(string url, object data, Dictionary<string, string>? headers = null)
    {
        var request = new HttpRequestMessage(HttpMethod.Put, url);

        request.Content = JsonContent.Create(data, options: _jsonSerializerOptions);

        await PrepareAuthorizationHeaderAsync(request, headers);

        var response = await _httpClient.SendAsync(request);

        return await HandleInternalResponseAsync<T>(response);
    }

    /// <summary>
    /// 添加授权头
    /// </summary>
    /// <param name="request">http请求对象</param>
    /// <param name="additionHeaders">附加请求头</param>
    protected async virtual Task PrepareAuthorizationHeaderAsync(HttpRequestMessage request,
        Dictionary<string, string>? additionHeaders)
    {
        var (schema, accessToken, deviceId, deviceInfo, tokenId, exp, sub, uniqueName) =
            await _authInfoGetter.GetAuthInfoAsync();

        if (!schema.IsNullOrEmpty() && !accessToken.IsNullOrEmpty())
        {
            request.Headers.Add("Authorization", $"{schema} {accessToken}");
        }

        if (!deviceId.IsNullOrEmpty())
        {
            request.Headers.Add("x-device-id", deviceId);
        }

        if (!deviceInfo.IsNullOrEmpty())
        {
            request.Headers.Add("x-device-info", deviceInfo);
        }

        if (!_contextContainer.GetCorrelationId().IsNullOrEmpty())
        {
            request.Headers.Add("x-correlation-id", _contextContainer.GetCorrelationId());
        }

        var clientIp = _currentUserContext.GetClientIp();
        if (!clientIp.IsNullOrEmpty())
        {
            request.Headers.Add("x-client-ip", clientIp);
        }

        additionHeaders?.ForEach(header =>
        {
            request.Headers.Add(header.Key, header.Value);
        });

        await Task.CompletedTask;
    }
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using System.Net.Http.Json;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Contract.OperationDtos;
using XJ.Framework.Library.Application.Contract.Security;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;

namespace XJ.Framework.Library.Infrastructure.ApiClient.Base;

public abstract class BaseApplicationApiClient : BaseApiClient
{
    private readonly ICurrentApplicationContext _applicationContext;

    protected BaseApplicationApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        ICurrentApplicationContext applicationContext, IOptions<JsonOptions> jsonOptions) : base(httpClient, logger,
        authInfoGetter, contextContainer,
        currentUserContext, jsonOptions)
    {
        _applicationContext = applicationContext;
    }

    /// <summary>
    /// 添加授权头
    /// </summary>
    /// <param name="request">http请求对象</param>
    /// <param name="additionHeaders">附加请求头</param>
    protected async override Task PrepareAuthorizationHeaderAsync(HttpRequestMessage request,
        Dictionary<string, string>? additionHeaders)
    {
        await base.PrepareAuthorizationHeaderAsync(request, additionHeaders);

        var path = request.RequestUri!.PathAndQuery;

        var body = new Dictionary<string, object?>();
        switch (request.Content)
        {
            case JsonContent json when json.Value != null:
                // 处理 Dictionary<string, T> 或 IDictionary 类型
                if (json.Value is System.Collections.IDictionary dictionary)
                {
                    foreach (System.Collections.DictionaryEntry entry in dictionary)
                    {
                        body.Add(entry.Key.ToString()!, entry.Value?.ToString());
                    }
                }
                // 处理 JObject 或类似动态 JSON 对象（Newtonsoft.Json 或 System.Text.Json）
                else if (json.Value.GetType().Name == "JObject") // Newtonsoft.Json
                {
                    var jobj = (dynamic)json.Value;
                    foreach (var prop in jobj.Properties())
                    {
                        body.Add(prop.Name, prop.Value?.ToString());
                    }
                }
                else if (json.Value.GetType().Name == "JsonObject") // System.Text.Json
                {
                    var jsonElement = (System.Text.Json.JsonElement)json.Value;
                    foreach (var prop in jsonElement.EnumerateObject())
                    {
                        body.Add(prop.Name, prop.Value.ToString());
                    }
                }
                // 默认处理：普通对象（通过反射获取属性）
                else
                {
                    foreach (var prop in json.Value.GetType().GetProperties())
                    {
                        var value = prop.GetValue(json.Value);
                        body.Add(prop.Name, value?.ToString());
                    }
                }
                break;
            case MultipartContent multipart:
            {
                foreach (var content in multipart)
                {
                    //遍历multipart中所有非文件的属性
                    if (content is StringContent stringContent)
                    {
                        body.Add(stringContent.Headers.ContentDisposition!.Name!,
                            await stringContent.ReadAsStringAsync());
                    }
                }

                break;
            }
        }

        var applicationCode = _applicationContext.GetApplicationCode();
        var secretKey = _applicationContext.GetSecretKey();

        var sign = ApplicationSignatureHelper.GenerateSignature(applicationCode, secretKey, path,
            body);

        request.Headers.Add("x-application-code", applicationCode);
        // request.Headers.Add("x-secret-key", secretKey);
        request.Headers.Add("x-signature", sign);
    }
}

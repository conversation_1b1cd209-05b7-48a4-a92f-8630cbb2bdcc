using System;
using System.Collections.Generic;
using System.Net;
using System.Net.NetworkInformation;
using System.Net.Sockets;
using System.Text;
using System.Threading;

namespace XJ.Framework.Library.Domain.Id
{
    /// <summary>
    /// ID生成器，基于雪花算法（Snowflake）
    /// 支持分布式多站点负载
    /// </summary>
    public class IdGenerator
    {
        private const long Twepoch = 1288834974657L; // Twitter的雪花算法元年时间戳（毫秒） 
        private const int WorkerIdBits = 5; // 机器码字节数
        private const int DatacenterIdBits = 5; // 数据中心字节数
        private const int SequenceBits = 12; // 序列号字节数
        private const long MaxWorkerId = -1L ^ (-1L << WorkerIdBits); // 最大机器码
        private const long MaxDatacenterId = -1L ^ (-1L << DatacenterIdBits); // 最大数据中心
        private const int WorkerIdShift = SequenceBits; // 机器码数据左移位数
        private const int DatacenterIdShift = SequenceBits + WorkerIdBits; // 数据中心左移位数
        private const int TimestampLeftShift = SequenceBits + WorkerIdBits + DatacenterIdBits; // 时间戳左移位数
        private const long SequenceMask = -1L ^ (-1L << SequenceBits); // 序列号掩码
        private const int MaxTolerateClockBackwardMs = 10; // 最大容忍时钟回拨毫秒数

        private static long _sequence = 0L; // 序列号
        private static long _lastTimestamp = -1L; // 最后时间戳
        private static readonly object SyncLock = new(); // 锁对象

        private static long _workerId = 0L; // 机器码
        private static long _datacenterId = 0L; // 数据中心
        private static bool _initialized = false; // 是否已初始化
        private static long _lastTimestampBackward = 0L; // 上次时钟回拨时间戳

        /// <summary>
        /// 初始化ID生成器
        /// </summary>
        /// <param name="workerId">机器码，如果为-1则自动生成</param>
        /// <param name="datacenterId">数据中心，如果为-1则自动生成</param>
        public static void Initialize(long workerId = -1, long datacenterId = -1)
        {
            lock (SyncLock)
            {
                if (_initialized)
                {
                    return;
                }

                // 如果workerId为-1，则自动获取
                if (workerId == -1)
                {
                    workerId = GetWorkerId();
                }
                else if (workerId > MaxWorkerId || workerId < 0)
                {
                    throw new ArgumentException($"worker Id can't be greater than {MaxWorkerId} or less than 0");
                }

                // 如果datacenterId为-1，则自动获取
                if (datacenterId == -1)
                {
                    datacenterId = GetDatacenterId();
                }
                else if (datacenterId > MaxDatacenterId || datacenterId < 0)
                {
                    throw new ArgumentException(
                        $"datacenter Id can't be greater than {MaxDatacenterId} or less than 0");
                }

                _workerId = workerId;
                _datacenterId = datacenterId;
                _initialized = true;

                Console.WriteLine($"IdGenerator initialized with workerId={_workerId}, datacenterId={_datacenterId}");
            }
        }

        /// <summary>
        /// 获取下一个ID
        /// </summary>
        /// <returns>分布式全局唯一ID</returns>
        public static long NextId()
        {
            // 确保IdGenerator已初始化
            EnsureInitialized();

            lock (SyncLock)
            {
                var timestamp = GetCurrentTimestamp();

                // 处理时钟回拨
                if (timestamp < _lastTimestamp)
                {
                    var offset = _lastTimestamp - timestamp;

                    // 如果时钟回拨在容忍范围内，则等待
                    if (offset <= MaxTolerateClockBackwardMs)
                    {
                        Thread.Sleep((int)offset + 1);
                        timestamp = GetCurrentTimestamp();
                    }
                    else
                    {
                        // 记录时钟回拨次数和时间
                        _lastTimestampBackward = timestamp;

                        // 如果频繁发生时钟回拨，则抛出异常
                        if (_lastTimestamp - _lastTimestampBackward < 5000)
                        {
                            throw new Exception(
                                $"Clock moved backwards too frequently. Refusing to generate id for {_lastTimestamp - timestamp} milliseconds");
                        }

                        // 使用上一次的时间戳，并增加序列号
                        timestamp = _lastTimestamp;
                    }
                }

                // 如果是同一毫秒内，则增加序列号
                if (_lastTimestamp == timestamp)
                {
                    _sequence = (_sequence + 1) & SequenceMask;
                    // 如果序列号用完，则等待下一毫秒
                    if (_sequence == 0)
                    {
                        timestamp = TilNextMillis(_lastTimestamp);
                    }
                }
                else
                {
                    // 不同毫秒内，重置序列号
                    _sequence = 0L;
                }

                _lastTimestamp = timestamp;
                return ((timestamp - Twepoch) << TimestampLeftShift) |
                       (_datacenterId << DatacenterIdShift) |
                       (_workerId << WorkerIdShift) |
                       _sequence;
            }
        }

        /// <summary>
        /// 获取当前时间戳
        /// </summary>
        /// <returns>时间戳（毫秒）</returns>
        private static long GetCurrentTimestamp()
        {
            return DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
        }

        /// <summary>
        /// 获取下一毫秒时间戳
        /// </summary>
        /// <param name="lastTimestamp">最后时间戳</param>
        /// <returns>下一毫秒时间戳</returns>
        private static long TilNextMillis(long lastTimestamp)
        {
            var timestamp = GetCurrentTimestamp();
            while (timestamp <= lastTimestamp)
            {
                timestamp = GetCurrentTimestamp();
                Thread.Sleep(0);
            }

            return timestamp;
        }

        /// <summary>
        /// 确保IdGenerator已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_initialized)
            {
                lock (SyncLock)
                {
                    if (!_initialized)
                    {
                        Initialize();
                    }
                }
            }
        }

        /// <summary>
        /// 自动获取WorkerId
        /// 基于MAC地址和PID生成，确保分布式环境中的唯一性
        /// </summary>
        private static long GetWorkerId()
        {
            try
            {
                // 尝试获取MAC地址
                var macBytes = GetMacAddress();
                if (macBytes != null && macBytes.Length > 0)
                {
                    // 使用MAC地址的后几位生成workerId
                    var hash = Math.Abs(BitConverter.ToInt32(macBytes, 2));
                    return hash % (MaxWorkerId + 1);
                }
            }
            catch (Exception)
            {
                // 忽略异常，使用备用方法
            }

            // 备用方法：使用IP地址和进程ID的组合
            try
            {
                byte[]? ipBytes = GetIPAddress();
                if (ipBytes != null && ipBytes.Length > 0)
                {
                    var pid = Environment.ProcessId;
                    var ipHashBytes = new byte[4];
                    for (int i = 0; i < 4; i++)
                    {
                        ipHashBytes[i] = (byte)(ipBytes[i] ^ (pid & 0xFF));
                        pid >>= 8;
                    }

                    var hash = Math.Abs(BitConverter.ToInt32(ipHashBytes, 0));
                    return hash % (MaxWorkerId + 1);
                }
            }
            catch (Exception)
            {
                // 忽略异常，使用随机数
            }

            // 最后的备用：使用随机数
            var random = new Random();
            return random.Next(0, (int)MaxWorkerId + 1);
        }

        /// <summary>
        /// 自动获取DatacenterId
        /// 基于IP地址生成，确保不同数据中心的唯一性
        /// </summary>
        private static long GetDatacenterId()
        {
            try
            {
                // 尝试使用IP地址的前几位作为数据中心ID
                byte[]? ipBytes = GetIPAddress();
                if (ipBytes != null && ipBytes.Length > 0)
                {
                    var hash = Math.Abs(BitConverter.ToInt32(ipBytes, 0));
                    return hash % (MaxDatacenterId + 1);
                }
            }
            catch (Exception)
            {
                // 忽略异常，使用随机数
            }

            // 备用方法：使用随机数
            var random = new Random();
            return random.Next(0, (int)MaxDatacenterId + 1);
        }

        /// <summary>
        /// 获取MAC地址
        /// </summary>
        private static byte[]? GetMacAddress()
        {
            try
            {
                foreach (var nic in NetworkInterface.GetAllNetworkInterfaces())
                {
                    Console.WriteLine($"{nic.Description}:{nic.OperationalStatus}");
                    // 过滤掉虚拟、无效或回环接口
                    if (nic.OperationalStatus == OperationalStatus.Up &&
                        !nic.Description.ToLower().Contains("virtual") &&
                        !nic.Description.ToLower().Contains("pseudo"))
                    {
                        var addressBytes = nic.GetPhysicalAddress().GetAddressBytes();
                        Console.WriteLine("MAC Address: " + BitConverter.ToString(addressBytes));
                        return addressBytes;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(ex.Message);
                Console.Error.WriteLine(ex.StackTrace);
                // 忽略异常
            }

            return null;
        }

        /// <summary>
        /// 获取IP地址
        /// </summary>
        private static byte[]? GetIPAddress()
        {
            try
            {
                // 优先获取非回环的IPv4地址
                var host = Dns.GetHostEntry(Dns.GetHostName());
                foreach (var ip in host.AddressList)
                {
                    Console.WriteLine("IP Address: " + ip);
                    if (ip.AddressFamily == AddressFamily.InterNetwork && !IPAddress.IsLoopback(ip))
                    {
                        var addressBytes = ip.GetAddressBytes();
                        Console.WriteLine("IP Address Bytes: " + BitConverter.ToString(addressBytes));
                        return addressBytes;
                    }
                }

                // 如果没有找到合适的IP，则使用本地IP
                return IPAddress.Loopback.GetAddressBytes();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine(ex.Message);
                Console.Error.WriteLine(ex.Message);
                // 忽略异常，返回本地IP
                return IPAddress.Loopback.GetAddressBytes();
            }
        }

        /// <summary>
        /// 从ID解析出时间戳、数据中心ID和工作节点ID
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns>解析结果</returns>
        public static (long timestamp, long datacenterId, long workerId, long sequence) ParseId(long id)
        {
            var timestamp = (id >> TimestampLeftShift) + Twepoch;
            var datacenterId = (id >> DatacenterIdShift) & ((1 << DatacenterIdBits) - 1);
            var workerId = (id >> WorkerIdShift) & ((1 << WorkerIdBits) - 1);
            var sequence = id & SequenceMask;

            return (timestamp, datacenterId, workerId, sequence);
        }
    }
}
using System.Linq.Expressions;

namespace XJ.Framework.Library.Domain.Common;

/// <summary>
/// 排序方向
/// </summary>
public class OrderbyDirection<TEntity>
{
    public Expression<Func<TEntity, object>> KeySelector { get; }
    public SortDirection SortDirection { get; }

    public OrderbyDirection(Expression<Func<TEntity, object>> keySelector, SortDirection sortDirection)
    {
        KeySelector = keySelector;
        SortDirection = sortDirection;
    }
}

/// <summary>
/// 排序方向枚举
/// </summary>
public enum SortDirection
{
    /// <summary>
    /// 升序
    /// </summary>
    Ascending,
    
    /// <summary>
    /// 降序
    /// </summary>
    Descending
} 
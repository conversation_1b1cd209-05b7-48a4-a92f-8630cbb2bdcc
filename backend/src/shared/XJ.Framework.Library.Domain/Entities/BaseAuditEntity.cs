namespace XJ.Framework.Library.Domain.Entities;

public class BaseAuditEntity<TKey> : BaseEntity<TKey>
{
    [Column("created_time")] public DateTimeOffset CreatedTime { get; set; }

    [Column("created_by"), StringLength(50)]
    public string CreatedBy { get; set; } = null!;

    [Column("last_modified_time")] public DateTimeOffset? LastModifiedTime { get; set; }

    [Column("last_modified_by"), StringLength(50)]
    public string? LastModifiedBy { get; set; }

    public void AuditCreate()
    {
        if (this.CreatedTime == DateTimeOffset.MinValue)
        {
            this.CreatedTime = DateTimeOffset.UtcNow;
        }

        if (string.IsNullOrEmpty(this.CreatedBy))
        {
            this.CreatedBy = "System";
        }
    }

    public void AuditModify()
    {
        if (this.LastModifiedTime == null || this.LastModifiedTime == DateTimeOffset.MinValue)
        {
            this.LastModifiedTime = DateTimeOffset.UtcNow;
        }

        if (string.IsNullOrEmpty(this.LastModifiedBy))
        {
            this.LastModifiedBy = "System";
        }
    }
}
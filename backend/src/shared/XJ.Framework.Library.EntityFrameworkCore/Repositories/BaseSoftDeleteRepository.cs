using System.Reflection;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Library.EntityFrameworkCore.Repositories;

public class BaseSoftDeleteRepository<TContext, TKey, TEntity> : BaseAuditRepository<TContext, TKey, TEntity>
    where TEntity : BaseSoftDeleteEntity<TKey>
    where TKey : struct
    where TContext : BaseDbContext
{
    private readonly List<string[]> _uniquePropertyList;

    public BaseSoftDeleteRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
        _uniquePropertyList = GetUniquePropertyList();
    }

    private List<string[]> GetUniquePropertyList()
    {
        var softDeleteIndexAttributes =
            typeof(TEntity).GetCustomAttributes<SoftDeleteIndexAttribute>().Where(q => q.IsUnique);

        var uniqueFields = softDeleteIndexAttributes.Select(q => q.PropertyNames.ToArray()).ToList();
        return uniqueFields;
    }

    private async Task RemoveDeletedUniqueDataAsync(TEntity entity, bool isSaveChange = true)
    {
        // 遍历_uniquePropertyList 找到唯一键组合后 动态拼接表达达式 使用entity对应属性上的值做为查询条件 并拼接Deleted为true表达式 找到对应数据并物理删除
        foreach (var uniqueField in _uniquePropertyList)
        {
            var parameter = Expression.Parameter(typeof(TEntity), "entity");
            Expression body = Expression.Equal(
                Expression.Property(parameter, uniqueField[0]),
                Expression.Constant(entity.GetType().GetProperty(uniqueField[0])?.GetValue(entity))
            );

            for (var i = 1; i < uniqueField.Length; i++)
            {
                var propertyValue = entity.GetType().GetProperty(uniqueField[i])?.GetValue(entity);
                var propertyType = parameter.Type.GetProperty(uniqueField[i])?.PropertyType;

// 如果属性是 Nullable<T>，但获取的值是 T，则包装成 Nullable<T>
                if (propertyType != null && Nullable.GetUnderlyingType(propertyType) != null && propertyValue != null)
                {
                    var underlyingType = Nullable.GetUnderlyingType(propertyType);
                    propertyValue = Convert.ChangeType(propertyValue, underlyingType!);
                }

                var next = Expression.Equal(
                    Expression.Property(parameter, uniqueField[i]),
                    Expression.Constant(propertyValue, propertyType!) // 显式指定类型
                );
                body = Expression.AndAlso(body, next);
            }

            var deletedExpr = Expression.Equal(
                Expression.Property(parameter, nameof(BaseSoftDeleteEntity<TKey>.Deleted)),
                Expression.Constant(true)
            );
            body = Expression.AndAlso(body, deletedExpr);

            var predicate = Expression.Lambda<Func<TEntity, bool>>(body, parameter);

            var data = (await base.GetListAsync(predicate)).ToList();
            if (data.Any())
            {
                await base.DeleteAsync(data, isSaveChange);
            }
        }
    }

    public async new Task<bool> InsertAsync(TEntity entity, bool isSaveChange = true)
    {
        entity.AuditCreate();
        return await base.InsertAsync(entity, isSaveChange);
    }


    public async new Task<bool> InsertAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        entities.ForEach(entity => entity.AuditCreate());
        return await base.InsertAsync(entities, isSaveChange);
    }

    protected async new Task<IQueryable<TEntity>> GetQueryableAsync()
    {
        return await Task.FromResult(DbContext.Set<TEntity>().Where(q => !q.Deleted).AsQueryable());
    }

    private List<string> GetUpdatePropertyList()
    {
        return
        [
            nameof(BaseSoftDeleteEntity<TKey>.Deleted),
            nameof(BaseSoftDeleteEntity<TKey>.LastModifiedTime),
            nameof(BaseSoftDeleteEntity<TKey>.LastModifiedBy)
        ];
    }

    public async new Task<bool> DeleteAsync(TEntity entity, bool isSaveChange = true)
    {
        await RemoveDeletedUniqueDataAsync(entity, isSaveChange);

        entity.AuditDelete();

        await UpdateAsync(entity, false, GetUpdatePropertyList());

        return isSaveChange && await SaveChangesAsync() > 0;
    }

    public async new Task<bool> DeleteAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        await entities.ForEachAsync(async entity =>
        {
            await RemoveDeletedUniqueDataAsync(entity, isSaveChange);

            entity.AuditDelete();

            await UpdateAsync(entity, false, GetUpdatePropertyList());
        });


        return isSaveChange && await SaveChangesAsync() > 0;
    }


    public async new Task<long> CountAsync(Expression<Func<TEntity, bool>> predicate)
    {
        return await base.CountAsync(predicate.AndNotDeleted<TKey, TEntity>());
    }


    public async new Task<TEntity?> GetAsync(TKey id)
    {
        Expression<Func<TEntity, bool>> expr = (entity) => entity.Key.Equals(id);

        return await GetAsync(expr.AndNotDeleted<TKey, TEntity>());
    }

    public async new Task<TEntity?> GetAsync(Expression<Func<TEntity, bool>> predicate, bool isNoTracking = true)
    {
        return await base.GetAsync(predicate.AndNotDeleted<TKey, TEntity>(), isNoTracking);
    }

    public async new Task<IEnumerable<TEntity>> GetListAsync(Expression<Func<TEntity, bool>> predicate,
        bool isNoTracking = true)
    {
        return await base.GetListAsync(predicate.AndNotDeleted<TKey, TEntity>(), isNoTracking);
    }

    public async new Task<IQueryable<TEntity>> LoadAsync(Expression<Func<TEntity, bool>> predicate,
        bool isNoTracking = true)
    {
        return await base.LoadAsync(predicate.AndNotDeleted<TKey, TEntity>(), isNoTracking);
    }

    public async new Task<PageData<TKey, TEntity>> GetPageAsync(Expression<Func<TEntity, bool>> whereLambda,
        int rowIndex, int pageSize,
        List<OrderbyDirection<TEntity>> orderBy,
        bool isNoTracking = true)
    {
        return await base.GetPageAsync(whereLambda.AndNotDeleted<TKey, TEntity>(), rowIndex, pageSize, orderBy,
            isNoTracking);
    }
}

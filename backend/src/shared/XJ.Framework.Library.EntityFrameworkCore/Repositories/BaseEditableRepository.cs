namespace XJ.Framework.Library.EntityFrameworkCore.Repositories;

public class BaseEditableRepository<TContext, TKey, TEntity> : BaseRepository<TContext, TKey, TEntity>,
    IEditableRepository<TKey, TEntity>
    where TEntity : BaseEntity<TKey>
    where TContext : BaseDbContext
    where TKey : struct

{
    public int SaveChanges()
    {
        return DbContext.SaveChanges();
    }

    public async Task<int> SaveChangesAsync()
    {
        return await DbContext.SaveChangesAsync();
    }

    #region 插入数据

    public async Task<bool> InsertAsync(TEntity entity, bool isSaveChange = true)
    {
        DbSet.Add(entity);
        if (isSaveChange)
        {
            return await SaveChangesAsync() > 0;
        }

        return false;
    }

    public async Task<bool> InsertAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        DbSet.AddRange(entities);
        if (isSaveChange)
        {
            return await SaveChangesAsync() > 0;
        }

        return false;
    }

    #endregion

    #region 删除

    public async Task<bool> DeleteAsync(TEntity entity, bool isSaveChange = true)
    {
        DbSet.Attach(entity);
        DbSet.Remove(entity);
        return isSaveChange && await SaveChangesAsync() > 0;
    }

    public async Task<bool> DeleteAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        entities.ForEach(entity =>
        {
            DbSet.Attach(entity);
            DbSet.Remove(entity);
        });
        return isSaveChange && await SaveChangesAsync() > 0;
    }

    #endregion

    #region 更新数据

    public async Task<bool> UpdateAsync(TEntity entity, bool isSaveChange = true,
        List<string>? updatePropertyList = null)
    {
        var entry = DbContext.Entry(entity);

        if (entry.State == EntityState.Detached)
        {
            DbSet.Attach(entity);
            entry = DbContext.Entry(entity);
        }

        if (updatePropertyList == null)
        {
            entry.State = EntityState.Modified; //全字段更新
        }
        else
        {
            updatePropertyList.ForEach(c =>
            {
                entry.Property(c).IsModified = true; //部分字段更新的写法
            });
        }

        if (isSaveChange)
        {
            return await SaveChangesAsync() > 0;
        }

        return false;
    }

    public async Task<bool> UpdateAsync(List<TEntity> entities, bool isSaveChange = true)
    {
        if (!entities.Any())
        {
            return false;
        }

        entities.ForEach(c =>
        {
            var entry = DbContext.Entry(c);
            if (entry.State == EntityState.Detached)
            {
                DbSet.Attach(c);
            }

            entry.State = EntityState.Modified;
        });
        if (isSaveChange)
        {
            return await SaveChangesAsync() > 0;
        }

        return false;
    }

    #endregion

    public BaseEditableRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}
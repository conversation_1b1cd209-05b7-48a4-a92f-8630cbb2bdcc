using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.EntityFrameworkCore.Repositories;

public abstract class BaseRepository<TContext, TKey, TEntity> : IRepository<TKey, TEntity>
    where TEntity : BaseEntity<TKey>
    where TContext : BaseDbContext
    where TKey : struct
{
    protected readonly DbSet<TEntity> DbSet;
    protected readonly TContext DbContext;

    protected BaseRepository(IServiceProvider serviceProvider)
    {
        DbContext = serviceProvider
            .GetRequiredService<TContext>();

        DbSet = DbContext.Set<TEntity>();
    }

    public DatabaseFacade Database => DbContext.Database;

    public IQueryable<TEntity> Entities => DbSet.AsQueryable().AsNoTracking();

    protected void DetachWhenExists(TKey key)
    {
        var tracked = DbContext.ChangeTracker.Entries<TEntity>()
            .FirstOrDefault(e => e.Entity.Key.Equals(key));
        if (tracked != null)
        {
            tracked.State = EntityState.Detached;
        }
    }

    /// <summary>
    /// 获取可查询对象
    /// </summary>
    /// <returns>可查询对象</returns>
    protected virtual async Task<IQueryable<TEntity>> GetQueryableAsync()
    {
        return await Task.FromResult(DbSet.AsQueryable());
    }

    public async Task<bool> AnyAsync(Expression<Func<TEntity, bool>> predicate)
    {
        return await DbSet.Where(predicate).AnyAsync();
    }

    #region 查找

    public async Task<long> CountAsync(Expression<Func<TEntity, bool>> predicate)
    {
        return await DbSet.LongCountAsync(predicate);
    }


    public async Task<TEntity?> GetAsync(TKey id)
    {
        return await DbSet.FindAsync(id);
    }

    public async Task<TEntity?> GetAsync(Expression<Func<TEntity, bool>> predicate, bool isNoTracking = true)
    {
        var data = isNoTracking ? DbSet.Where(predicate).AsNoTracking() : DbSet.Where(predicate);
        return await data.FirstOrDefaultAsync();
    }

    public async Task<IEnumerable<TEntity>> GetListAsync(Expression<Func<TEntity, bool>> predicate,
        bool isNoTracking = true)
    {
        var data = isNoTracking ? DbSet.Where(predicate).AsNoTracking() : DbSet.Where(predicate);

        return await data.ToListAsync();
    }


    public async Task<IQueryable<TEntity>> LoadAsync(Expression<Func<TEntity, bool>> predicate,
        bool isNoTracking = true)
    {
        return await Task.Run(() => isNoTracking ? DbSet.Where(predicate).AsNoTracking() : DbSet.Where(predicate));
    }

    #endregion


    #region 分页查找

    public async Task<PageData<TKey, TEntity>> GetPageAsync(Expression<Func<TEntity, bool>> whereLambda,
        int rowIndex, int pageSize,
        List<OrderbyDirection<TEntity>> orderBy,
        bool isNoTracking = true)
    {
        var data = isNoTracking ? DbSet.Where(whereLambda).AsNoTracking() : DbSet.Where(whereLambda);

        data = data.Orderby<TKey, TEntity>(orderBy);

        var pageData = new PageData<TKey, TEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };
        return pageData;
    }

    #endregion
}

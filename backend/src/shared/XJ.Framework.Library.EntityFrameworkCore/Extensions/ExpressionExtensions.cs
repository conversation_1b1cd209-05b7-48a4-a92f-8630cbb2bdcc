using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.EntityFrameworkCore.Extensions;

public static class ExpressionExtensions
{
    public static Expression<Func<TEntity, bool>> AndNotDeleted<TKey, TEntity>(
        this Expression<Func<TEntity, bool>> predicate)
        where TKey : struct
        where TEntity : BaseSoftDeleteEntity<TKey>
    {
        return predicate.And(entity => !entity.Deleted);
    }
}
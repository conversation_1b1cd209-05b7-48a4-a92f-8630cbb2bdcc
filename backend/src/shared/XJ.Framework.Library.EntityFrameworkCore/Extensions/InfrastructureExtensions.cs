using Microsoft.Extensions.Hosting;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Library.EntityFrameworkCore.Extensions;

public static class InfrastructureExtensions
{
    public static IServiceCollection InitInfra<TWrapper>(this IServiceCollection services,
        IConfigurationRoot configuration)
        where TWrapper : InfrastructureWrapper, new()
    {
        var environmentName = services.BuildServiceProvider().GetRequiredService<IHostEnvironment>().EnvironmentName;

        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddSolutionJsonFile($"database.{environmentName}.json")
            .AddUserSecrets<TWrapper>().AddEnvironmentVariables();

        var builtConfiguration = configurationBuilder.Build();

        services.Configure<DatabaseOption>(builtConfiguration.GetSection("Database"));

        var repositoryTypes = typeof(TWrapper).Assembly.GetTypes()
            .Where(t => t is { IsClass: true, IsAbstract: false } &&
                        t.GetInterfaces().Any(i => i.Name.EndsWith("Repository")))
            .ToList();

        foreach (var implementationType in repositoryTypes)
        {
            var interfaceType = implementationType.GetInterfaces().FirstOrDefault(i => i.Name.EndsWith("Repository"));
            if (interfaceType != null)
            {
                services.AddScoped(interfaceType, implementationType);
            }
        }

        var wrapper = new TWrapper();

        wrapper.Init(services, configuration);

        return services;
    }
}

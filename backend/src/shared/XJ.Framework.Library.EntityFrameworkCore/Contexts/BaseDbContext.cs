using Microsoft.Extensions.Options;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Library.EntityFrameworkCore.Contexts;

public class BaseDbContext : DbContext
{
    protected readonly IOptions<DatabaseOption> DatabaseOptions;
    public BaseDbContext(DbContextOptions options, IOptions<DatabaseOption> databaseOptions) : base(options)
    {
        DatabaseOptions = databaseOptions;
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // // var assembly = Assembly.GetCallingAssembly().GetTypes();
        //
        // var callingAssembly = Assembly.GetCallingAssembly();
        //
        // var domainAssemblyName = callingAssembly.GetName().Name!.Replace(".EntityFrameworkCore", ".Domain");
        //
        // var domainAssembly = Assembly.Load(domainAssemblyName);
        //
        // domainAssembly.GetTypes().Where(q => q.BaseType == typeof(BaseEntity<>));
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        base.OnConfiguring(optionsBuilder);
    }
}
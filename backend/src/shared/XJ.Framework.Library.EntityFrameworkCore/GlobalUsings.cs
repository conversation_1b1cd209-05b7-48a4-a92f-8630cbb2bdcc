// global using 指令

global using Microsoft.EntityFrameworkCore;
global using Microsoft.EntityFrameworkCore.Infrastructure;
global using Microsoft.EntityFrameworkCore.Storage;
global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using System.Linq.Expressions;
global using XJ.Framework.Library.Common.Abstraction.Extensions;
global using XJ.Framework.Library.Domain.UOW;
global using XJ.Framework.Library.EntityFrameworkCore.Contexts;
global using XJ.Framework.Library.EntityFrameworkCore.Extensions;

global using XJ.Framework.Library.Domain.Entities;
global using XJ.Framework.Library.Domain.Repositories.Interfaces;
global using XJ.Framework.Library.Domain.Common;
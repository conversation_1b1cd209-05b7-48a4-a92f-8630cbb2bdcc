using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace XJ.Framework.Library.EntityFrameworkCore.Converters;

/// <summary>
/// 枚举值转换器工厂
/// </summary>
public static class EnumValueConverterFactory
{
    /// <summary>
    /// 创建枚举到整数的转换器
    /// </summary>
    /// <typeparam name="TEnum">枚举类型</typeparam>
    /// <returns>值转换器</returns>
    public static ValueConverter<TEnum, int> Create<TEnum>() where TEnum : Enum
    {
        return new ValueConverter<TEnum, int>(
            v => (int)(object)v,
            v => (TEnum)(object)v);
    }
} 
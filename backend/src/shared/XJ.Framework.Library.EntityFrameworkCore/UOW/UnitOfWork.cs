using System.Data;
using System.Data.Common;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using DomainUOW = XJ.Framework.Library.Domain.UOW;

namespace XJ.Framework.Library.EntityFrameworkCore.UOW;

/// <summary>
/// 工作单元实现
/// </summary>
/// <typeparam name="TContext">数据库上下文类型</typeparam>
public class UnitOfWork<TContext> : DomainUOW.IUnitOfWork where TContext : DbContext
{
    private readonly TContext _dbContext;
    private readonly ILogger<UnitOfWork<TContext>> _logger;
    private IDbContextTransaction? _transaction;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="dbContext">数据库上下文</param>
    /// <param name="logger">日志记录器</param>
    public UnitOfWork(TContext dbContext, ILogger<UnitOfWork<TContext>> logger)
    {
        _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <inheritdoc />
    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        await BeginTransactionAsync(30, IsolationLevel.ReadCommitted, cancellationToken);
    }

    /// <inheritdoc />
    public async Task BeginTransactionAsync(int timeout, IsolationLevel isolationLevel, CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            _logger.LogWarning("事务已经开启，跳过重复开启");
            return;
        }

        _logger.LogInformation("开启事务，超时时间：{Timeout}秒，隔离级别：{IsolationLevel}", timeout, isolationLevel);
        
        // 设置命令超时
        _dbContext.Database.SetCommandTimeout(TimeSpan.FromSeconds(timeout));
        
        _transaction = await _dbContext.Database.BeginTransactionAsync(isolationLevel, cancellationToken);
    }

    /// <inheritdoc />
    public async Task CommitAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_transaction == null)
            {
                _logger.LogWarning("没有活动的事务，跳过提交");
                return;
            }

            await _dbContext.SaveChangesAsync(cancellationToken);
            await _transaction.CommitAsync(cancellationToken);
            _logger.LogInformation("事务提交成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "事务提交失败");
            await RollbackAsync(cancellationToken);
            throw;
        }
        finally
        {
            if (_transaction != null)
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
            
            // 重置命令超时
            _dbContext.Database.SetCommandTimeout(TimeSpan.FromSeconds(30));
        }
    }

    /// <inheritdoc />
    public async Task RollbackAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            if (_transaction == null)
            {
                _logger.LogWarning("没有活动的事务，跳过回滚");
                return;
            }

            await _transaction.RollbackAsync(cancellationToken);
            _logger.LogInformation("事务回滚成功");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "事务回滚失败");
            throw;
        }
        finally
        {
            if (_transaction != null)
            {
                await _transaction.DisposeAsync();
                _transaction = null;
            }
            
            // 重置命令超时
            _dbContext.Database.SetCommandTimeout(TimeSpan.FromSeconds(30));
        }
    }

    /// <inheritdoc />
    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _dbContext.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// 获取DbContext
    /// </summary>
    public TContext GetDbContext()
    {
        return _dbContext;
    }

    /// <inheritdoc />
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <inheritdoc />
    public async ValueTask DisposeAsync()
    {
        await DisposeAsyncCore();
        Dispose(false);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (_disposed)
        {
            return;
        }

        if (disposing)
        {
            _transaction?.Dispose();
            _dbContext.Dispose();
        }

        _disposed = true;
    }

    protected virtual async ValueTask DisposeAsyncCore()
    {
        if (_disposed)
        {
            return;
        }

        if (_transaction != null)
        {
            await _transaction.DisposeAsync();
        }
        await _dbContext.DisposeAsync();

        _disposed = true;
    }
}
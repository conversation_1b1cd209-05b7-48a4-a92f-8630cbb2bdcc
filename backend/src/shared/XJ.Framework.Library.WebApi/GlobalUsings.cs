// global using 指令

// Microsoft 命名空间
global using Microsoft.AspNetCore.Builder;
global using Microsoft.AspNetCore.Http;
global using Microsoft.AspNetCore.Mvc;
global using Microsoft.AspNetCore.Mvc.ApplicationModels;
global using Microsoft.AspNetCore.Mvc.ModelBinding;
global using Microsoft.Extensions.DependencyInjection;
global using Microsoft.OpenApi.Any;
global using Microsoft.OpenApi.Models;

// 第三方库
global using Swashbuckle.AspNetCore.SwaggerGen;

// XJ.Framework 基础库
global using XJ.Framework.Library.Application.Contract.Interfaces;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Common.Abstraction.Contexts;
global using XJ.Framework.Library.Common.Abstraction.Extensions;
global using XJ.Framework.Library.Domain.Shared.Dtos;
global using XJ.Framework.Library.Domain.UOW;

// XJ.Framework WebApi组件
global using XJ.Framework.Library.WebApi.Attributes;
global using XJ.Framework.Library.WebApi.Fields;
global using XJ.Framework.Library.WebApi.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Options;
using System.Reflection;
using XJ.Framework.Library.Application.Options;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Common.Extensions;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Library.Logging.Abstraction.DI;
using XJ.Framework.Library.Logging.Database;
using XJ.Framework.Library.WebApi.Binders;
using XJ.Framework.Library.WebApi.Filters;
using XJ.Framework.Library.WebApi.Middlewares;
using XJ.Framework.Library.WebApi.Services;

namespace XJ.Framework.Library.WebApi.Extensions;

public static class BasicApplicationExtensions
{
    public static WebApplicationBuilder AddBasicApplication<TAuthProvider, TAuthInfoGetter, TWrapper>(
        this WebApplicationBuilder builder, TWrapper wrapper)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
        where TWrapper : WebApiWrapper
    {
        builder.Services.AddScoped<IAuthProvider, TAuthProvider>();
        builder.Services.AddScoped<IAuthInfoGetter, TAuthInfoGetter>();

        builder.AddLoggingProvider();

        builder.Services.TryAddSingleton<IContextContainer, AsyncLocalContextContainer>();

        builder.Services.AddSingleton<ApiExplorer>();

        builder.Services.AddHttpContextAccessor();

        builder.Services.AddCurrentUserContext();

        builder.Services.AddCurrentApplicationContext();

        builder.Configuration.AddUserSecrets<TWrapper>();

        builder.Configuration.AddEnvironmentVariables();

        var environmentName = builder.Services.BuildServiceProvider().GetRequiredService<IHostEnvironment>()
            .EnvironmentName;

        var fileName = $"application.{environmentName}.json";

        var configurationBuilder = new ConfigurationBuilder();
        configurationBuilder.AddSolutionJsonFile(fileName)
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: false)
            .AddJsonFile($"appsettings.{environmentName}.json", optional: true, reloadOnChange: false)
            .AddUserSecrets<TWrapper>()
            .AddEnvironmentVariables()
            ;


        var builtConfiguration = configurationBuilder.Build();

        builder.Services.Configure<ApplicationOption>(builtConfiguration.GetSection("Application"));


        var secretKeyConfigurationBuilder = new ConfigurationBuilder();
        secretKeyConfigurationBuilder
            .AddSolutionJsonFile($"secretKey.{environmentName}.json")
            .AddUserSecrets<TWrapper>();
        var builtSecretKeyConfiguration = secretKeyConfigurationBuilder.Build();

        builder.Services.Configure<SecretKeyOption>(builtSecretKeyConfiguration.GetSection("SecretKeys"));


        var corsOrigins = builder.Configuration.GetSection("Cors:Origins").Get<string[]>();

        builder.Services.AddCors(options =>
        {
            options.AddPolicy("Default", policy =>
            {
                if (corsOrigins != null && corsOrigins.Length > 0 && corsOrigins[0] != "*")
                {
                    policy.WithOrigins(corsOrigins)
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                }
                else
                {
                    policy.AllowAnyOrigin()
                        .AllowAnyHeader()
                        .AllowAnyMethod();
                }
            });
        });

        // Add services to the container.
        builder.Services.AddControllers(option =>
            {
                // option.Filters.Add<UnitOfWorkFilter>();
                option.Filters.Add<ResponseHandleFilter>();
                // option.Filters.Add<PermissionAuthorizationFilter>();
                // option.Filters.Add<ExceptionHandleFilter>();

                wrapper.AddFilters(option);

                option.ModelBinderProviders.Insert(0, new BasicModelBinderProvider());

                // mvcOptions?.Invoke(option);
            })
            .ConfigureApiBehaviorOptions(options => { options.SuppressModelStateInvalidFilter = true; })
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.Converters.Add(new LongToStringConverter());
                options.JsonSerializerOptions.Converters.Add(new NullableLongToStringConverter());
                options.JsonSerializerOptions.Encoder = options.JsonSerializerOptions.Encoder =
                    System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping;
            });


        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();

        builder.Services.AddSwaggerGen(options =>
        {
            var fullName = Assembly.GetEntryAssembly()!.GetName().Name!;

            // 声明layerName变量 使用fullName 先去掉开始部分的“XJ.Framework.” 然后去掉第一个点之前的部分 剩下的部分就是layerName

            var layerName = fullName.Replace("XJ.Framework.", "");

            layerName = layerName.Substring(layerName.IndexOf(".", StringComparison.Ordinal) + 1);


            IncludeXmlCommentsWithAssembly(options, Assembly.GetEntryAssembly());

            IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Application") + ".dll");

            IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Application.Contract") + ".dll");

            IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Domain") + ".dll");

            IncludeXmlCommentsWithAssembly(options, fullName.Replace(layerName, "Domain.Shared") + ".dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Domain.Shared.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Domain.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Application.dll");

            IncludeXmlCommentsWithAssembly(options, "XJ.Framework.Library.Application.Contract.dll");


            options.DocumentFilter<DocumentFilter>();

            options.SchemaFilter<EnumSchemaFilter>();
        });

        wrapper.Init(builder.Services, builder.Configuration);
        return builder;
    }

    private static void IncludeXmlCommentsWithAssembly(SwaggerGenOptions options, string dllFileName)
    {
        var fullDllPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, dllFileName);
        if (File.Exists(fullDllPath))
        {
            IncludeXmlCommentsWithAssembly(options, Assembly.LoadFrom(fullDllPath));
        }
    }

    private static void IncludeXmlCommentsWithAssembly(SwaggerGenOptions options, Assembly? assembly)
    {
        var assemblyName = assembly?.GetName().Name;
        var assemblyXmlFilePath = Path.Combine(AppContext.BaseDirectory, $"{assemblyName}.xml");
        if (File.Exists(assemblyXmlFilePath))
            options.IncludeXmlComments(Path.Combine(AppContext.BaseDirectory, assemblyXmlFilePath), true);
    }

    public static IApplicationBuilder UseBasicApplication<TWrapper>(this WebApplication app, TWrapper wrapper)
        where TWrapper : WebApiWrapper
    {
        var applicationOption = app.Services.GetRequiredService<IOptions<ApplicationOption>>();
        if (applicationOption.Value.EnableSerilog)
        {
            app.UseDatabaseLoggingProvider<TWrapper>();
        }


        // Configure the HTTP request pipeline.

        if (applicationOption.Value.EnableSwagger)
        {
            app.UseSwagger(options =>
            {
                options.PreSerializeFilters.Add((swagger, httpReq) =>
                {
                    // 处理网关层面带二级路由的情况，如果有二级路由，swagger的 url 也需要加上二级路由

                    var serverUrl = "";

                    var fetchHeaders = new[] { "X-Forwarded-Path", "X-Request-Uri" };

                    foreach (var header in fetchHeaders)
                    {
                        if (httpReq.Headers.TryGetValue(header, out var value))
                        {
                            serverUrl = value;
                            break;
                        }
                    }

                    if (serverUrl!.Length > 0)
                    {
                        var index = serverUrl.IndexOf("/swagger/", StringComparison.CurrentCultureIgnoreCase);
                        if (index > 0)
                        {
                            swagger.Servers = [new OpenApiServer { Url = $"{serverUrl[..index]}/" }];
                        }
                    }
                });
            });
            app.UseSwaggerUI();
        }

        app.UseHttpsRedirection();

        app.UseRouting();

        app.UseCors("Default");

        app.UseMiddleware<ContextContainerMiddleware>();
        app.UseMiddleware<ResponseLoggingMiddleware>();
        app.UseMiddleware<ExceptionHandlingMiddleware>();
        app.UseMiddleware<PermissionAuthorizationMiddleware>();
        app.UseMiddleware<UnitOfWorkMiddleware>();
        app.UseMiddleware<GzipSelectiveMiddleware>();

        app.UseAuthorization();

        // app.UseMiddleware<JwtAuthenticationMiddleware>();

        // app.UseMiddleware<UnitOfWorkMiddleware>();

        wrapper.UseMiddleware(app);

        app.MapControllers();


        return app;
    }
}

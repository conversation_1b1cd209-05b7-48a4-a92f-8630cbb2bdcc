using Microsoft.Net.Http.Headers;

namespace XJ.Framework.Library.WebApi.Extensions;

public static class HttpResponseExtensions
{
    public static bool MatchMediaType(this HttpResponse response, List<string> mediaTypes)
    {
        if (!mediaTypes.Any())
        {
            return false;
        }

        if (!MediaTypeHeaderValue.TryParse(response.ContentType, out var mediaType))
        {
            return false;
        }

        //mediaType.MediaType
        return mediaTypes.Any(q =>
        {
            var mediaTypeHeaderValue = MediaTypeHeaderValue.Parse(q);
            return mediaTypeHeaderValue.MatchesMediaType(mediaType.MediaType);
        });
    }

    public static bool MatchMediaType(this HttpRequest request, List<string> mediaTypes)
    {
        if (!mediaTypes.Any())
        {
            return false;
        }

        if (!MediaTypeHeaderValue.TryParse(request.ContentType, out var mediaType))
        {
            return false;
        }

        //mediaType.MediaType
        return mediaTypes.Any(q =>
        {
            var mediaTypeHeaderValue = MediaTypeHeaderValue.Parse(q);
            return mediaTypeHeaderValue.MatchesMediaType(mediaType.MediaType);
        });
    }
}
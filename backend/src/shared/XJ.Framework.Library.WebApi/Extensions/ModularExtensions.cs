using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using XJ.Framework.Library.WebApi.Services;

namespace XJ.Framework.Library.WebApi.Extensions;

/// <summary>
/// 模块化扩展方法
/// </summary>
public static class ModularExtensions
{
    /// <summary>
    /// 添加模块化支持
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddModularSupport(this IServiceCollection services, IConfiguration configuration)
    {
        // 注册服务代理工厂
        services.AddScoped<IServiceProxyFactory, ServiceProxyFactory>();
        
        // 配置服务代理选项
        services.Configure<ServiceProxyOptions>(configuration.GetSection("ServiceProxy"));
        
        // 注册模块注册表
        services.AddSingleton<IModuleRegistry, ModuleRegistry>();
        
        return services;
    }

    /// <summary>
    /// 注册模块
    /// </summary>
    /// <typeparam name="TWrapper">WebApiWrapper类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <param name="routePrefix">路由前缀（可选）</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddModule<TWrapper>(
        this IServiceCollection services, 
        IConfiguration configuration,
        string? routePrefix = null)
        where TWrapper : WebApiWrapper, new()
    {
        var wrapper = new TWrapper();
        
        // 初始化模块
        wrapper.Init(services, configuration);
        
        // 注册模块信息
        var moduleInfo = new ModuleInfo
        {
            Name = typeof(TWrapper).Assembly.GetName().Name ?? "Unknown",
            WrapperType = typeof(TWrapper),
            Assembly = typeof(TWrapper).Assembly,
            RoutePrefix = routePrefix,
            IsManagement = typeof(TWrapper).Name.Contains("Mgt")
        };
        
        services.AddSingleton(moduleInfo);
        
        // 注册到模块注册表
        var serviceProvider = services.BuildServiceProvider();
        var moduleRegistry = serviceProvider.GetService<IModuleRegistry>();
        moduleRegistry?.RegisterModule(moduleInfo);
        
        return services;
    }

    /// <summary>
    /// 配置MVC以包含模块的Controller
    /// </summary>
    /// <param name="mvcBuilder">MVC构建器</param>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>MVC构建器</returns>
    public static IMvcBuilder AddModuleControllers(this IMvcBuilder mvcBuilder, IServiceProvider serviceProvider)
    {
        var moduleInfos = serviceProvider.GetServices<ModuleInfo>();
        
        foreach (var moduleInfo in moduleInfos)
        {
            mvcBuilder.PartManager.ApplicationParts.Add(new AssemblyPart(moduleInfo.Assembly));
        }
        
        return mvcBuilder;
    }

    /// <summary>
    /// 获取服务代理
    /// </summary>
    /// <typeparam name="TService">服务接口类型</typeparam>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>服务代理</returns>
    public static IServiceProxy<TService> GetServiceProxy<TService>(this IServiceProvider serviceProvider)
        where TService : class
    {
        var factory = serviceProvider.GetRequiredService<IServiceProxyFactory>();
        return factory.CreateProxy<TService>();
    }

    /// <summary>
    /// 获取服务实例（通过代理）
    /// </summary>
    /// <typeparam name="TService">服务接口类型</typeparam>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>服务实例</returns>
    public static TService GetProxiedService<TService>(this IServiceProvider serviceProvider)
        where TService : class
    {
        var proxy = serviceProvider.GetServiceProxy<TService>();
        return proxy.GetService();
    }
}

/// <summary>
/// 模块信息
/// </summary>
public class ModuleInfo
{
    /// <summary>
    /// 模块名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Wrapper类型
    /// </summary>
    public Type WrapperType { get; set; } = null!;
    
    /// <summary>
    /// 模块程序集
    /// </summary>
    public Assembly Assembly { get; set; } = null!;
    
    /// <summary>
    /// 路由前缀
    /// </summary>
    public string? RoutePrefix { get; set; }
    
    /// <summary>
    /// 是否为管理模块
    /// </summary>
    public bool IsManagement { get; set; }
    
    /// <summary>
    /// 是否已启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 模块描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 模块版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";
}

/// <summary>
/// 模块注册表接口
/// </summary>
public interface IModuleRegistry
{
    /// <summary>
    /// 注册模块
    /// </summary>
    /// <param name="moduleInfo">模块信息</param>
    void RegisterModule(ModuleInfo moduleInfo);
    
    /// <summary>
    /// 获取所有模块
    /// </summary>
    /// <returns>模块列表</returns>
    IEnumerable<ModuleInfo> GetAllModules();
    
    /// <summary>
    /// 根据名称获取模块
    /// </summary>
    /// <param name="moduleName">模块名称</param>
    /// <returns>模块信息</returns>
    ModuleInfo? GetModule(string moduleName);
}

/// <summary>
/// 模块注册表实现
/// </summary>
public class ModuleRegistry : IModuleRegistry
{
    private readonly List<ModuleInfo> _modules = new();
    private readonly ILogger<ModuleRegistry> _logger;

    public ModuleRegistry(ILogger<ModuleRegistry> logger)
    {
        _logger = logger;
    }

    public void RegisterModule(ModuleInfo moduleInfo)
    {
        if (_modules.Any(m => m.Name == moduleInfo.Name))
        {
            _logger.LogWarning("Module {ModuleName} is already registered", moduleInfo.Name);
            return;
        }

        _modules.Add(moduleInfo);
        _logger.LogInformation("Module {ModuleName} registered with route prefix: {RoutePrefix}", 
            moduleInfo.Name, moduleInfo.RoutePrefix ?? "none");
    }

    public IEnumerable<ModuleInfo> GetAllModules()
    {
        return _modules.AsReadOnly();
    }

    public ModuleInfo? GetModule(string moduleName)
    {
        return _modules.FirstOrDefault(m => m.Name.Equals(moduleName, StringComparison.OrdinalIgnoreCase));
    }
}

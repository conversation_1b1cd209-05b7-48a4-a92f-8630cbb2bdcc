using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace XJ.Framework.Library.WebApi.Extensions;

/// <summary>
/// 模块扩展方法
/// </summary>
public static class ModuleExtensions
{
    /// <summary>
    /// 添加WebApi模块
    /// </summary>
    /// <typeparam name="TWrapper">模块的WebApiWrapper类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddWebApiModule<TWrapper>(this IServiceCollection services, IConfiguration configuration)
        where TWrapper : WebApiWrapper, new()
    {
        var wrapper = new TWrapper();
        
        // 初始化模块服务
        wrapper.Init(services, configuration);
        
        // 注册模块信息
        var moduleInfo = new WebApiModuleInfo
        {
            Name = typeof(TWrapper).Assembly.GetName().Name ?? "Unknown",
            WrapperType = typeof(TWrapper),
            Assembly = typeof(TWrapper).Assembly
        };
        
        services.AddSingleton(moduleInfo);
        
        return services;
    }

    /// <summary>
    /// 添加WebApi模块的Controller
    /// </summary>
    /// <param name="mvcBuilder">MVC构建器</param>
    /// <param name="moduleAssembly">模块程序集</param>
    /// <returns>MVC构建器</returns>
    public static IMvcBuilder AddModuleControllers(this IMvcBuilder mvcBuilder, Assembly moduleAssembly)
    {
        // 添加程序集作为应用程序部件
        mvcBuilder.PartManager.ApplicationParts.Add(new AssemblyPart(moduleAssembly));
        
        return mvcBuilder;
    }

    /// <summary>
    /// 添加多个模块的Controller
    /// </summary>
    /// <param name="mvcBuilder">MVC构建器</param>
    /// <param name="moduleAssemblies">模块程序集列表</param>
    /// <returns>MVC构建器</returns>
    public static IMvcBuilder AddModuleControllers(this IMvcBuilder mvcBuilder, params Assembly[] moduleAssemblies)
    {
        foreach (var assembly in moduleAssemblies)
        {
            mvcBuilder.AddModuleControllers(assembly);
        }
        
        return mvcBuilder;
    }

    /// <summary>
    /// 自动发现并添加所有已注册模块的Controller
    /// </summary>
    /// <param name="mvcBuilder">MVC构建器</param>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>MVC构建器</returns>
    public static IMvcBuilder AddAllModuleControllers(this IMvcBuilder mvcBuilder, IServiceProvider serviceProvider)
    {
        var moduleInfos = serviceProvider.GetServices<WebApiModuleInfo>();
        
        foreach (var moduleInfo in moduleInfos)
        {
            mvcBuilder.AddModuleControllers(moduleInfo.Assembly);
        }
        
        return mvcBuilder;
    }
}

/// <summary>
/// WebApi模块信息
/// </summary>
public class WebApiModuleInfo
{
    /// <summary>
    /// 模块名称
    /// </summary>
    public string Name { get; set; } = string.Empty;
    
    /// <summary>
    /// Wrapper类型
    /// </summary>
    public Type WrapperType { get; set; } = null!;
    
    /// <summary>
    /// 模块程序集
    /// </summary>
    public Assembly Assembly { get; set; } = null!;
    
    /// <summary>
    /// 是否已启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
    
    /// <summary>
    /// 模块描述
    /// </summary>
    public string Description { get; set; } = string.Empty;
    
    /// <summary>
    /// 模块版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";
}

/// <summary>
/// 模块配置选项
/// </summary>
public class ModuleOptions
{
    /// <summary>
    /// 启用的模块列表
    /// </summary>
    public Dictionary<string, bool> EnabledModules { get; set; } = new();
    
    /// <summary>
    /// 模块特定配置
    /// </summary>
    public Dictionary<string, Dictionary<string, object>> ModuleConfigs { get; set; } = new();
}

/// <summary>
/// 模块服务调用器
/// </summary>
public interface IModuleServiceCaller
{
    /// <summary>
    /// 调用指定模块的Controller Action
    /// </summary>
    /// <param name="moduleName">模块名称</param>
    /// <param name="controllerName">控制器名称</param>
    /// <param name="actionName">Action名称</param>
    /// <param name="parameters">参数</param>
    /// <returns>调用结果</returns>
    Task<object?> CallActionAsync(string moduleName, string controllerName, string actionName, params object[] parameters);
    
    /// <summary>
    /// 调用指定模块的Controller Action
    /// </summary>
    /// <typeparam name="T">返回类型</typeparam>
    /// <param name="moduleName">模块名称</param>
    /// <param name="controllerName">控制器名称</param>
    /// <param name="actionName">Action名称</param>
    /// <param name="parameters">参数</param>
    /// <returns>调用结果</returns>
    Task<T?> CallActionAsync<T>(string moduleName, string controllerName, string actionName, params object[] parameters);
    
    /// <summary>
    /// 获取指定模块的服务实例
    /// </summary>
    /// <typeparam name="T">服务类型</typeparam>
    /// <param name="moduleName">模块名称</param>
    /// <returns>服务实例</returns>
    T? GetModuleService<T>(string moduleName) where T : class;
}

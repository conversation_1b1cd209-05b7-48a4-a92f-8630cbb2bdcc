namespace XJ.Framework.Library.WebApi.Attributes;

[AttributeUsage(AttributeTargets.Class, AllowMultiple = false, Inherited = true)]
public class ControllerNameAttribute : Attribute, IControllerModelConvention
{
    private readonly string _name;

    public ControllerNameAttribute(string name)
    {
        _name = name;
    }

    public void Apply(ControllerModel controller)
    {
        controller.ControllerName = _name;
    }
}
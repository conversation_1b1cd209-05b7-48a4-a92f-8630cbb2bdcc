using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog.Context;
using System.Text.Json;
using XJ.Framework.Library.Common.Abstraction.Models;
using XJ.Framework.Library.Logging.Abstraction.DI;

#pragma warning disable CS8604

namespace XJ.Framework.Library.WebApi.Filters;

public class ResponseHandleFilter : IActionFilter
{
    private readonly IContextContainer _contextContainer;
    private readonly ILogger<ResponseHandleFilter> _logger;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    public ResponseHandleFilter(IContextContainer contextContainer, ILogger<ResponseHandleFilter> logger,
        IOptions<JsonOptions> jsonOptions)
    {
        _contextContainer = contextContainer;
        _logger = logger;
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        var isObjectResult = context.Result is ObjectResult;

        //如果不是ObjectResult 则不需要做标准格式化
        if (!isObjectResult) return;

        // //如果Response的ContentType不匹配 也不需要做标准格式化 
        // 方式不行，此时Response的ContentType是空 无法做判断 使用IResultFilter可以获取到值，但Result只读
        // if (!context.HttpContext.Response.MatchMediaType(_options.CurrentValue.SupportResponseFormatMediaTypes))
        // {
        //     return;
        // }

        var endpoint = GetEndpoint(context.HttpContext);

        var ignoreFormatAttribute = endpoint?.Metadata.GetMetadata<IgnoreFormatAttribute>();

        //如果action上包含IgnoreFormatAttribute属性 也不需要做标准格式化
        if (ignoreFormatAttribute != null) return;

        var result = (ObjectResult)context.Result!;

        var response = new ObjectResult(GetResponse(result.Value));

        var ignoreLoggingAttribute = endpoint?.Metadata.GetMetadata<IgnoreLoggingAttribute>();
        if (ignoreLoggingAttribute == null)
        {
            _logger.LoggingInformation("response",JsonSerializer.Serialize(response.Value, _jsonSerializerOptions));
        }

        context.Result = response;
    }


    private Endpoint? GetEndpoint(HttpContext context)
    {
        if (context == null)
        {
            throw new ArgumentNullException(nameof(context));
        }

        return context.Features.Get<IEndpointFeature>()?.Endpoint;
    }

    private ServiceResponse<object> GetResponse(object? data)
    {
        return ServiceResponse<object>.SuccessResponseWithStatus(data, _contextContainer.GetCorrelationId());
    }
}
namespace XJ.Framework.Library.WebApi.Conventions;

public class ControllerNameAttributeConvention : IApplicationModelConvention
{
    public void Apply(ApplicationModel application)
    {
        foreach (var controller in application.Controllers)
        {
            var customControllerNameAttribute = controller.Attributes
                .OfType<ControllerNameAttribute>()
                .FirstOrDefault();

            if (customControllerNameAttribute != null)
            {
                customControllerNameAttribute.Apply(controller);
            }
        }
    }
}
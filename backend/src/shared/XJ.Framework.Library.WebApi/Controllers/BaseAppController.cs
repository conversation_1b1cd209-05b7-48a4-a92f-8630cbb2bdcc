using XJ.Framework.Library.EntityFrameworkCore.Repositories;

namespace XJ.Framework.Library.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
public class BaseAppController<TKey, TDto, TService, TQueryCriteria> : ControllerBase
    where TService : IAppService<TKey, TDto, TQueryCriteria>
    where TK<PERSON> : struct
    where TDto : BaseDto<TKey>
    where TQueryCriteria : BaseQueryCriteria
{
    protected readonly TService Service;

    public BaseAppController(IServiceProvider serviceProvider)
    {
        Service = serviceProvider.GetRequiredService<TService>();
    }
}
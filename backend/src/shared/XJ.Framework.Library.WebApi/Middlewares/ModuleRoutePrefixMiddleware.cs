using Microsoft.AspNetCore.Http;
using XJ.Framework.Library.WebApi.Extensions;

namespace XJ.Framework.Library.WebApi.Middlewares;

/// <summary>
/// 模块路由前缀中间件
/// </summary>
public class ModuleRoutePrefixMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ModuleRoutePrefixMiddleware> _logger;
    private readonly IModuleRegistry _moduleRegistry;

    public ModuleRoutePrefixMiddleware(
        RequestDelegate next,
        ILogger<ModuleRoutePrefixMiddleware> logger,
        IModuleRegistry moduleRegistry)
    {
        _next = next;
        _logger = logger;
        _moduleRegistry = moduleRegistry;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var originalPath = context.Request.Path.Value;
        
        if (!string.IsNullOrEmpty(originalPath))
        {
            // 检查是否需要处理路由前缀
            var processedPath = ProcessRoutePrefix(originalPath);
            if (processedPath != originalPath)
            {
                _logger.LogDebug("Route rewritten from {OriginalPath} to {ProcessedPath}", originalPath, processedPath);
                context.Request.Path = processedPath;
            }
        }

        await _next(context);
    }

    private string ProcessRoutePrefix(string originalPath)
    {
        var modules = _moduleRegistry.GetAllModules();
        
        foreach (var module in modules)
        {
            if (!string.IsNullOrEmpty(module.RoutePrefix))
            {
                var prefixPattern = $"/{module.RoutePrefix.Trim('/')}/";
                if (originalPath.StartsWith(prefixPattern, StringComparison.OrdinalIgnoreCase))
                {
                    // 移除前缀，让原始路由生效
                    var newPath = originalPath.Substring(module.RoutePrefix.Length + 1);
                    if (!newPath.StartsWith("/"))
                    {
                        newPath = "/" + newPath;
                    }
                    return newPath;
                }
            }
        }

        return originalPath;
    }
}

/// <summary>
/// 模块路由前缀中间件扩展方法
/// </summary>
public static class ModuleRoutePrefixMiddlewareExtensions
{
    /// <summary>
    /// 使用模块路由前缀中间件
    /// </summary>
    /// <param name="builder">应用程序构建器</param>
    /// <returns>应用程序构建器</returns>
    public static IApplicationBuilder UseModuleRoutePrefix(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<ModuleRoutePrefixMiddleware>();
    }
}

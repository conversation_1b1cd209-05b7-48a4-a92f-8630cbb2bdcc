using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Serilog.Context;
using System.Net;
using System.Security.Authentication;
using System.Text.Json;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Models;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Library.WebApi.Middlewares;

public class ExceptionHandlingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<ExceptionHandlingMiddleware> _logger;
    private readonly IContextContainer _contextContainer;
    private readonly IOptions<ApplicationOption> _applicationOption;
    private readonly JsonSerializerOptions _jsonSerializerOptions;
    private readonly IServiceProvider _serviceProvider;

    public ExceptionHandlingMiddleware(RequestDelegate next, ILogger<ExceptionHandlingMiddleware> logger,
        IContextContainer contextContainer, IOptions<ApplicationOption> applicationOption,
        IOptions<JsonOptions> jsonOptions, IServiceProvider serviceProvider)
    {
        _next = next;
        _logger = logger;
        _contextContainer = contextContainer;
        _applicationOption = applicationOption;
        _serviceProvider = serviceProvider;
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            using var scope = _serviceProvider.CreateScope();

            var currentUserContext = scope.ServiceProvider.GetRequiredService<ICurrentUserContext>();

            var authInfoGetter = scope.ServiceProvider.GetRequiredService<IAuthInfoGetter>();

            var authInfo = await authInfoGetter.GetAuthInfoAsync();

            var clientIp = currentUserContext.GetClientIp();

            var realException = ex.GetRealException()!;

            var endpoint = context.GetEndpoint();

            var controllerActionDescriptor = endpoint?.Metadata
                .OfType<Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor>()
                .FirstOrDefault();

            var controller = controllerActionDescriptor?.ControllerName;
            var action = controllerActionDescriptor?.ActionName;
            var routeTemplate = controllerActionDescriptor?.AttributeRouteInfo?.Template;
            
            using (LogContext.PushProperty("Controller", controller ?? ""))
            using (LogContext.PushProperty("Action", action ?? ""))
            using (LogContext.PushProperty("RouteTemplate", routeTemplate ?? ""))
            using (LogContext.PushProperty("CurrentUser", authInfo.uniqueName ?? "Anonymous"))
            using (LogContext.PushProperty("ClientIp", clientIp))
            {
                _logger.LoggingException("application-exception", realException);
            }

            var responseData = GetErrorResponse(realException);

            await ResponseJsonMessageAsync(context, responseData);
        }
    }

    private async Task ResponseJsonMessageAsync(HttpContext context, ServiceResponse<object> responseData)
    {
        context.Response.StatusCode = responseData.Code;
        context.Response.ContentType = "application/json";
        var json = JsonSerializer.Serialize(responseData, _jsonSerializerOptions);
        await context.Response.WriteAsync(json);
    }

    private ServiceResponse<object> GetErrorResponse(Exception exception)
    {
        var httpStatusCode = GetHttpStatusCode(exception);
        return new ServiceResponse<object>(
            correlationId: _contextContainer.GetCorrelationId(),
            code: (int)httpStatusCode,
            message: exception.Message,
            detail: httpStatusCode == HttpStatusCode.InternalServerError
                ? GetExceptionStackTrace(exception)
                : string.Empty
        );
    }

    private string GetExceptionStackTrace(Exception exception)
    {
        var isFullStackTrack = IsFullStackTrace();
        return isFullStackTrack && exception.StackTrace != null ? exception.StackTrace : string.Empty;
    }

    private bool IsFullStackTrace()
    {
        return _applicationOption.Value.EnableStackTrace;
    }

    private HttpStatusCode GetHttpStatusCode(Exception exception)
    {
        return exception switch
        {
            ValidationException => HttpStatusCode.BadRequest,
            NotFoundException => HttpStatusCode.NotFound,
            AuthenticationException => HttpStatusCode.Unauthorized,
            AuthorizationException => HttpStatusCode.Forbidden,
            _ => HttpStatusCode.InternalServerError
        };
    }
}

namespace XJ.Framework.Library.WebApi.Middlewares;

using System.IO.Compression;
using Microsoft.AspNetCore.Http;
using System.Reflection;

public class GzipSelectiveMiddleware
{
    private readonly RequestDelegate _next;

    public GzipSelectiveMiddleware(RequestDelegate next)
    {
        _next = next;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 只处理API请求
        var endpoint = context.GetEndpoint();
        if (endpoint != null)
        {
            var hasGzip = endpoint.Metadata.GetMetadata<EnableGzipAttribute>() != null;
            if (hasGzip)
            {
                var acceptEncoding = context.Request.Headers["Accept-Encoding"].ToString();
                if (acceptEncoding.Contains("gzip"))
                {
                    var originalBodyStream = context.Response.Body;
                    await using var compressedStream =
                        new GZipStream(originalBodyStream, CompressionLevel.Fastest, leaveOpen: true);
                    context.Response.Headers.Append("Content-Encoding", "gzip");
                    context.Response.Body = compressedStream;
                    try
                    {
                        await _next(context);
                    }
                    finally
                    {
                        context.Response.Body = originalBodyStream;
                    }

                    return;
                }
            }
        }

        await _next(context);
    }
}

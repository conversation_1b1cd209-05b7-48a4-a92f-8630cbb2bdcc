using Serilog.Context;
using System.Web;

namespace XJ.Framework.Library.WebApi.Middlewares;

public class ContextContainerMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IContextContainer _contextContainer;

    public ContextContainerMiddleware(RequestDelegate next, IContextContainer contextContainer)
    {
        this._next = next;
        this._contextContainer = contextContainer;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        // 从请求头中获取默认的CorrelationId
        // 优先级：x-correlation-id > trace-Id > traceId > request_id
        // x-correlation-id表示的是一个交易，是一组操作的唯一标识。trace_id同理，只不过是 open-telemetry的标准。
        // span_id也是一个操作ID，更多地用于在一个交易中的不同操作之间的标识，而且最好有序，能表示先后顺序。
        // x_request_id是W3C的TraceContext标准中的，仅表示一个请求。
        var defaultCorrelationId =
            HttpUtility.UrlDecode(context.Request.Headers["x-correlation-id"].ToString())
                .IsNullOrEmpty(() => HttpUtility.UrlDecode(context.Request.Headers["trace-Id"].ToString()))
                .IsNullOrEmpty(() => HttpUtility.UrlDecode(context.Request.Headers["traceId"].ToString()))
                .IsNullOrEmpty(() => HttpUtility.UrlDecode(context.Request.Headers["x_span_id"].ToString()))
                .IsNullOrEmpty(() => HttpUtility.UrlDecode(context.Request.Headers["span_id"].ToString()))
                .IsNullOrEmpty(() => HttpUtility.UrlDecode(context.Request.Headers["x_request_id"].ToString()))
                .IsNullOrEmpty(() => HttpUtility.UrlDecode(context.Request.Headers["request_id"].ToString()));

        this._contextContainer.Init();
        this._contextContainer.InitCorrelationId(defaultCorrelationId);

        using (LogContext.PushProperty("CorrelationId", this._contextContainer.GetCorrelationId()))
            await this._next(context);
    }
}
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.Extensions.Logging;
using System.Reflection;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Library.WebApi.Middlewares;

public class UnitOfWorkMiddleware
{
    private readonly RequestDelegate _next;
    private readonly ILogger<UnitOfWorkMiddleware> _logger;

    public UnitOfWorkMiddleware(RequestDelegate next, ILogger<UnitOfWorkMiddleware> logger)
    {
        _next = next;
        _logger = logger;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var endpoint = context.GetEndpoint();
        var controllerActionDescriptor = endpoint?.Metadata.GetMetadata<ControllerActionDescriptor>();
        var unitOfWorkAttr = controllerActionDescriptor?.MethodInfo.GetCustomAttribute<UnitOfWorkAttribute>();

        if (unitOfWorkAttr == null || !unitOfWorkAttr.IsTransactional)
        {
            await _next(context);
            return;
        }

        var unitOfWork = context.RequestServices.GetRequiredService<IUnitOfWork>();
        var controllerName = controllerActionDescriptor?.ControllerName;
        var actionName = controllerActionDescriptor?.ActionName;

        try
        {
            _logger.LogInformation(
                "开始事务 - Controller: {Controller}, Action: {Action}, Timeout: {Timeout}s, IsolationLevel: {IsolationLevel}",
                controllerName, actionName, unitOfWorkAttr.Timeout, unitOfWorkAttr.IsolationLevel);

            await unitOfWork.BeginTransactionAsync(
                unitOfWorkAttr.Timeout,
                unitOfWorkAttr.IsolationLevel,
                context.RequestAborted);

            await _next(context);

            await unitOfWork.CommitAsync(context.RequestAborted);
            _logger.LogInformation(
                "提交事务 - Controller: {Controller}, Action: {Action}",
                controllerName, actionName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex,
                "事务执行失败/回滚 - Controller: {Controller}, Action: {Action}",
                controllerName, actionName);
            await unitOfWork.RollbackAsync(context.RequestAborted);
            throw;
        }
    }
} 
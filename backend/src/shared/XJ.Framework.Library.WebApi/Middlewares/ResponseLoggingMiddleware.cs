using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Primitives;
using Serilog.Context;
using System.Text;
using System.Text.Json;
using XJ.Framework.Library.Common.Abstraction.Models;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Logging.Abstraction.DI;
using XJ.Framework.Library.WebApi.Extensions;

namespace XJ.Framework.Library.WebApi.Middlewares;

public class ResponseLoggingMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IContextContainer _contextContainer;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly ILogger<ResponseLoggingMiddleware> _logger;
    private readonly JsonSerializerOptions _jsonSerializerOptions;
    private readonly IAuthInfoGetter _authInfoGetter;

    public ResponseLoggingMiddleware(RequestDelegate next, IContextContainer contextContainer,
        ILogger<ResponseLoggingMiddleware> logger, IOptions<JsonOptions> jsonOptions,
        ICurrentUserContext currentUserContext, IServiceProvider serviceProvider)
    {
        _next = next;
        _contextContainer = contextContainer;
        _logger = logger;
        _currentUserContext = currentUserContext;
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;

        var scope = serviceProvider.CreateScope();
        _authInfoGetter = scope.ServiceProvider.GetRequiredService<IAuthInfoGetter>();
    }


    public async Task InvokeAsync(HttpContext context)
    {
        var endpoint = context.GetEndpoint();
        string? controller = null;
        string? action = null;
        string? routeTemplate = null;
        string? clientIp = _currentUserContext.GetClientIp();
        object? httpExtend = null;

        context.Request.EnableBuffering();

        if (endpoint != null)
        {
            var ignoreLoggingAttribute = endpoint.Metadata.GetMetadata<IgnoreLoggingAttribute>();
            if (ignoreLoggingAttribute != null)
            {
                await _next(context);
            }
            else
            {
                var controllerActionDescriptor = endpoint.Metadata
                    .OfType<Microsoft.AspNetCore.Mvc.Controllers.ControllerActionDescriptor>()
                    .FirstOrDefault();

                controller = controllerActionDescriptor?.ControllerName;
                action = controllerActionDescriptor?.ActionName;
                routeTemplate = controllerActionDescriptor?.AttributeRouteInfo?.Template;

                var httpMethod = context.Request.Method;

                var parameters = context.Request.Query;

                var headers = context.Request.Headers;

                var requestBody = "";

                var includeMediaTypes = new List<string>
                {
                    "application/json",
                    "application/*+json",
                    "application/xml",
                    "application/*+xml",
                    "text/*"
                };

                if (context.Request.MatchMediaType(includeMediaTypes))
                {
                    context.Request.Body.Seek(0, SeekOrigin.Begin); // 确保从头开始读
                    using var reader = new StreamReader(context.Request.Body, encoding: Encoding.UTF8,
                        detectEncodingFromByteOrderMarks: false, leaveOpen: true);
                    requestBody = await reader.ReadToEndAsync();
                    context.Request.Body.Seek(0, SeekOrigin.Begin); // 读完后重置，保证后续中间件/控制器还能读取
                }


                httpExtend = new
                {
                    HtttpMethod = httpMethod,
                    Parameters = parameters.Select(q => new { Key = q.Key, Value = q.Value.ToString() }),
                    Headers = FilterHeaders(headers),
                    Url = context.Request.Path,
                    RequestBody = !requestBody.IsNullOrEmpty() ? JsonSerializer.Deserialize<object>(requestBody) : null
                };

                var authInfo = await _authInfoGetter.GetAuthInfoAsync();

                using (LogContext.PushProperty("Controller", controller ?? ""))
                using (LogContext.PushProperty("Action", action ?? ""))
                using (LogContext.PushProperty("RouteTemplate", routeTemplate ?? ""))
                using (LogContext.PushProperty("CurrentUser", authInfo.uniqueName ?? "Anonymous"))
                    // using (LogContext.PushProperty("HttpExtend", httpExtend?.ToJson()))
                using (LogContext.PushProperty("ClientIp", clientIp))
                {
                    _logger.LoggingInformation("request", JsonSerializer.Serialize(httpExtend, _jsonSerializerOptions));

                    await _next(context);
                }
            }
        }
        else
        {
            await _next(context);
        }
    }

    private IEnumerable<object> FilterHeaders(IHeaderDictionary headers)
    {
        var excludeHeaders = new List<string> { "Authorization" };
        return headers.Select(q => new { Key = q.Key, Value = q.Value.ToString() })
            .Where(q => !excludeHeaders.Contains(q.Key));
    }
}

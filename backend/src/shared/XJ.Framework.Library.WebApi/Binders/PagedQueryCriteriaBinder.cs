using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Converters;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.WebApi.Binders
{
    /// <summary>
    /// 用于绑定PagedQueryCriteria的绑定器
    /// </summary>
    public class PagedQueryCriteriaBinder : IModelBinder
    {
        private static readonly Dictionary<string, Action<IValueProvider, IPageRequestParams>> ReservedPageParams =
            new(StringComparer.OrdinalIgnoreCase)
            {
                {
                    "$pageIndex",
                    (valueProvider, pageParams) =>
                        SetPageValue<int>(valueProvider, "$pageIndex", value => pageParams.PageIndex = value)
                },
                {
                    "$pageSize",
                    (valueProvider, pageParams) =>
                        SetPageValue<int>(valueProvider, "$pageSize", value => pageParams.PageSize = value)
                }
            };

        private static readonly Dictionary<string, FieldSortDirection> StringToSortDirection =
            new(StringComparer.OrdinalIgnoreCase)
            {
                { "asc", FieldSortDirection.Ascending },
                { "desc", FieldSortDirection.Descending },
            };

        public Task BindModelAsync(ModelBindingContext bindingContext)
        {
            bindingContext.NullCheck(nameof(bindingContext));

            var model = TypeCreator.CreateInstance(bindingContext.ModelType);

            if (model is IPagedQueryCriteria pagedQueryCriteria)
            {
                BindPageParams(bindingContext.ValueProvider, pagedQueryCriteria.PageParams);
                BindOrderByParams(bindingContext.ValueProvider, pagedQueryCriteria);

                var condition = pagedQueryCriteria.InitCondition();

                FillModelProperties(bindingContext.ValueProvider, condition, bindingContext.ActionContext.HttpContext);
            }

            bindingContext.Result = ModelBindingResult.Success(model);

            return Task.CompletedTask;
        }

        private static void BindPageParams(IValueProvider valueProvider, IPageRequestParams pageParams)
        {
            foreach (KeyValuePair<string, Action<IValueProvider, IPageRequestParams>> kv in ReservedPageParams)
            {
                var valueResult = valueProvider.GetValue(kv.Key);

                if (valueResult != ValueProviderResult.None)
                    kv.Value(valueProvider, pageParams);
            }
        }

        private static void BindOrderByParams(IValueProvider valueProvider, IPagedQueryCriteria criteria)
        {
            var sortByResult = valueProvider.GetValue("$sortBy");

            if (sortByResult != ValueProviderResult.None)
            {
                var orderbyList = new List<OrderByRequestItem>();
                sortByResult.ForEach((index, result) =>
                {
                    var sortBy = result;

                    if (sortBy.IsNotEmpty())
                    {
                        orderbyList.Add(new OrderByRequestItem()
                        { DataField = sortBy, SortDirection = GetOrderBy(valueProvider, index) });
                    }
                });


                criteria.OrderBy = orderbyList.ToArray();
            }
        }

        private static FieldSortDirection GetOrderBy(IValueProvider valueProvider, int index)
        {
            var orderByResult = valueProvider.GetValue("$orderBy");

            var orderByString = "asc";

            if (orderByResult != ValueProviderResult.None)
                orderByString = orderByResult.Values.ElementAt(index)!;

            if (StringToSortDirection.TryGetValue(orderByString, out var orderBy) == false)
                orderBy = FieldSortDirection.Ascending;

            return orderBy;
        }

        private static void SetPageValue<T>(IValueProvider valueProvider, string valueName, Action<T> setter)
        {
            var valueResult = valueProvider.GetValue(valueName);

            if (valueResult != ValueProviderResult.None)
            {
                var value = DataConverter.ChangeType<string, T>(valueResult.FirstValue)!;

                setter(value);
            }
        }

        private static void FillModelProperties(IValueProvider valueProvider, object condition,
            Microsoft.AspNetCore.Http.HttpContext httpContext)
        {
            IEnumerable<PropertyInfo> properties = condition.GetType().ToPropertyDictionary().Values;

            if (!properties.Any())
            {
                return;
            }

            IDictionary<string, object?> propDict =
                BuildValueDictionary(valueProvider, condition.GetType(), httpContext);

            propDict.FillProperties(properties, condition);
        }

        /// <summary>
        /// 根据model type的属性，与valueProvider合并为一个大小写无关的字典
        /// </summary>
        /// <param name="valueProvider"></param>
        /// <param name="conditionType"></param>
        /// <param name="httpContext"></param>
        /// <returns></returns>
        private static IDictionary<string, object?> BuildValueDictionary(IValueProvider valueProvider,
            System.Type conditionType, Microsoft.AspNetCore.Http.HttpContext httpContext)
        {
            Dictionary<string, object?> dictionary = new(StringComparer.OrdinalIgnoreCase);

            var propertyDict = conditionType.ToPropertyDictionary();

            foreach (var kv in propertyDict)
            {
                if (dictionary.ContainsKey(kv.Key))
                    continue;

                var propType = kv.Value.PropertyType;

                // 1. 处理 Dictionary 类型
                if (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(Dictionary<,>))
                {
                    var keyType = propType.GetGenericArguments()[0];
                    var valueType = propType.GetGenericArguments()[1];

                    // 1.1 处理复杂对象字典 (如 Dictionary<string, DynamicFormQueryDto>)
                    if (valueType.IsClass && valueType != typeof(string))
                    {
                        System.Diagnostics.Debug.WriteLine($"Processing complex object dictionary: {kv.Key} -> {valueType.Name}");
                        System.Diagnostics.Debug.WriteLine($"Available properties: {string.Join(", ", valueType.GetProperties().Select(p => p.Name))}");
                        
                        var dict = (System.Collections.IDictionary)Activator.CreateInstance(propType)!;

                        // 获取所有参数名
                        var allKeys = new List<string>();
                        allKeys.AddRange(httpContext.Request.Query.Keys);
                        if (httpContext.Request.HasFormContentType)
                            allKeys.AddRange(httpContext.Request.Form.Keys);

                        System.Diagnostics.Debug.WriteLine($"All parameters for {kv.Key}: {string.Join(", ", allKeys)}");

                        // 解析嵌套对象参数
                        var objectDict = new Dictionary<string, object>();

                        foreach (var paramName in allKeys.Distinct())
                        {
                            // 匹配格式: propName[dictKey][nestedProperty] 或 propName[dictKey][nestedProperty][index]
                            // 也支持: dictKey[nestedProperty] 或 dictKey[nestedProperty][index] (无属性名前缀)
                            bool isPropertyPrefixed = paramName.StartsWith(kv.Key + "[", StringComparison.CurrentCultureIgnoreCase);
                            bool isDirectFormat = !isPropertyPrefixed && paramName.Contains("[") && paramName.Contains("]");
                            
                            // 检查是否是当前属性相关的参数
                            bool isRelevantParameter = isPropertyPrefixed || 
                                                     (isDirectFormat && propertyDict.ContainsKey(paramName.Split('[')[0]));
                            
                            if (isRelevantParameter)
                            {
                                // Debug output
                                System.Diagnostics.Debug.WriteLine($"Processing parameter: {paramName}");
                                
                                string remainingPart;
                                if (isPropertyPrefixed)
                                {
                                    remainingPart = paramName.Substring(kv.Key.Length + 1);
                                    System.Diagnostics.Debug.WriteLine($"Property prefixed format, remaining part: {remainingPart}");
                                }
                                else
                                {
                                    // 直接格式，整个参数名就是剩余部分
                                    remainingPart = paramName;
                                    System.Diagnostics.Debug.WriteLine($"Direct format, remaining part: {remainingPart}");
                                }

                                if (remainingPart.EndsWith("]"))
                                {
                                    remainingPart = remainingPart.Substring(0, remainingPart.Length - 1);

                                    // 解析 dictKey 和 nestedPart
                                    var bracketParts = remainingPart.Split(new[] { "][" }, StringSplitOptions.None);
                                    if (bracketParts.Length >= 2)
                                    {
                                        var dictKey = bracketParts[0];
                                        var nestedPart = string.Join("][", bracketParts.Skip(1));
                                        
                                        System.Diagnostics.Debug.WriteLine($"Parsed - dictKey: {dictKey}, nestedPart: {nestedPart}");

                                        if (!objectDict.ContainsKey(dictKey))
                                        {
                                            objectDict[dictKey] = Activator.CreateInstance(valueType)!;
                                            System.Diagnostics.Debug.WriteLine($"Created new object for key: {dictKey}");
                                        }

                                        var obj = objectDict[dictKey];

                                        // 解析嵌套属性
                                        // 检查是否是数组索引格式: nestedProperty[index]
                                        if (nestedPart.Contains("[") && nestedPart.Contains("]"))
                                        {
                                            var lastBracketIndex = nestedPart.LastIndexOf('[');
                                            if (lastBracketIndex > 0)
                                            {
                                                var propertyName = nestedPart.Substring(0, lastBracketIndex);
                                                var indexPart = nestedPart.Substring(lastBracketIndex + 1);
                                                
                                                // 移除可能的结尾 ]
                                                if (indexPart.EndsWith("]"))
                                                    indexPart = indexPart.Substring(0, indexPart.Length - 1);
                                                
                                                // 移除 propertyName 可能的结尾 ]
                                                if (propertyName.EndsWith("]"))
                                                    propertyName = propertyName.Substring(0, propertyName.Length - 1);
                                                
                                                System.Diagnostics.Debug.WriteLine($"Array format - propertyName: {propertyName}, indexPart: {indexPart}");

                                                if (int.TryParse(indexPart, out var index))
                                                {
                                                    // 处理数组属性
                                                    var property = valueType.GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                                                    System.Diagnostics.Debug.WriteLine($"Available properties on {valueType.Name}: {string.Join(", ", valueType.GetProperties().Select(p => p.Name))}");
                                                    System.Diagnostics.Debug.WriteLine($"Looking for property: {propertyName}, Found: {property != null}");
                                                    
                                                    if (property != null)
                                                    {
                                                        var nestedValueResult = valueProvider.GetValue(paramName);
                                                        if (nestedValueResult != ValueProviderResult.None)
                                                        {
                                                            System.Diagnostics.Debug.WriteLine($"Found value for {paramName}: {nestedValueResult.FirstValue}");
                                                            System.Diagnostics.Debug.WriteLine($"Raw values: {string.Join(", ", nestedValueResult.Values)}");
                                                            
                                                            var currentValue = property.GetValue(obj);
                                                            if (currentValue == null)
                                                            {
                                                                // 创建新的List
                                                                var listType = typeof(List<>).MakeGenericType(property.PropertyType.GetGenericArguments()[0]);
                                                                currentValue = Activator.CreateInstance(listType);
                                                                property.SetValue(obj, currentValue);
                                                                System.Diagnostics.Debug.WriteLine($"Created new list for property: {propertyName}");
                                                            }

                                                            // 添加到List
                                                            var addMethod = currentValue!.GetType().GetMethod("Add");
                                                            var convertedValue = DataConverter.ChangeType<string>(nestedValueResult.FirstValue,
                                                                property.PropertyType.GetGenericArguments()[0]);
                                                            addMethod!.Invoke(currentValue, [convertedValue]);
                                                            System.Diagnostics.Debug.WriteLine($"Added value to list: {convertedValue}");
                                                        }
                                                        else
                                                        {
                                                            System.Diagnostics.Debug.WriteLine($"No value found for parameter: {paramName}");
                                                        }
                                                    }
                                                    else
                                                    {
                                                        System.Diagnostics.Debug.WriteLine($"Property not found: {propertyName}");
                                                    }
                                                }
                                                else
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"Failed to parse index from: {indexPart}");
                                                }
                                            }
                                        }
                                        else
                                        {
                                            // 处理简单属性
                                            var property = valueType.GetProperty(nestedPart, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                                            System.Diagnostics.Debug.WriteLine($"Available properties on {valueType.Name}: {string.Join(", ", valueType.GetProperties().Select(p => p.Name))}");
                                            System.Diagnostics.Debug.WriteLine($"Looking for simple property: {nestedPart}, Found: {property != null}");
                                            
                                            if (property != null)
                                            {
                                                var simpleValueResult = valueProvider.GetValue(paramName);
                                                if (simpleValueResult != ValueProviderResult.None)
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"Found simple value for {paramName}: {simpleValueResult.FirstValue}");
                                                    var convertedValue = DataConverter.ChangeType<string>(simpleValueResult.FirstValue, property.PropertyType);
                                                    property.SetValue(obj, convertedValue);
                                                    System.Diagnostics.Debug.WriteLine($"Set property {nestedPart} to: {convertedValue}");
                                                }
                                                else
                                                {
                                                    System.Diagnostics.Debug.WriteLine($"No simple value found for parameter: {paramName}");
                                                }
                                            }
                                            else
                                            {
                                                System.Diagnostics.Debug.WriteLine($"Simple property not found: {nestedPart}");
                                            }
                                        }
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine($"Invalid bracket format in: {remainingPart}");
                                    }
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"Parameter doesn't end with ']': {paramName}");
                                }
                            }
                        }

                        // 将解析的对象添加到字典
                        foreach (var objKv in objectDict)
                        {
                            object? dictKey = objKv.Key;
                            var dictValue = objKv.Value;

                            // 类型转换
                            if (keyType != typeof(string))
                                dictKey = DataConverter.ChangeType<string>(objKv.Key, keyType);

                            dict.Add(dictKey!, dictValue);
                        }

                        dictionary.Add(kv.Key, dict);
                        continue;
                    }

                    // 1.2 处理简单字典 (原有逻辑)
                    // 约定：请求参数为 propName[key]=value 形式
                    var simpleDict = (System.Collections.IDictionary)Activator.CreateInstance(propType)!;

                    // 获取所有参数名
                    var allSimpleKeys = new List<string>();
                    allSimpleKeys.AddRange(httpContext.Request.Query.Keys);
                    if (httpContext.Request.HasFormContentType)
                        allSimpleKeys.AddRange(httpContext.Request.Form.Keys);

                    foreach (var paramName in allSimpleKeys.Distinct())
                    {
                        if (paramName.StartsWith(kv.Key + "[", StringComparison.CurrentCultureIgnoreCase) &&
                            paramName.EndsWith("]"))
                        {
                            var dictKeyStr =
                                paramName.Substring(kv.Key.Length + 1, paramName.Length - kv.Key.Length - 2);
                            object? dictKey = dictKeyStr;
                            var dictValueResult = valueProvider.GetValue(paramName);
                            object? dictValue = dictValueResult.FirstValue;

                            // 类型转换
                            if (keyType != typeof(string))
                                dictKey = DataConverter.ChangeType<string>(dictKeyStr, keyType);
                            if (valueType != typeof(string) && dictValue != null)
                                dictValue = DataConverter.ChangeType<string>(dictValue.ToString(), valueType);

                            simpleDict.Add(dictKey!, dictValue);
                        }
                    }

                    dictionary.Add(kv.Key, simpleDict);
                    continue;
                }

                // 2. 处理数组、List、普通类型（原有逻辑）
                ValueProviderResult valueResult;
                if (propType.IsArray ||
                    (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(List<>)))
                {
                    var elementType = propType.IsArray ? propType.GetElementType() : propType.GetGenericArguments()[0];
                    // 对象数组处理
                    if (elementType!.IsClass && elementType != typeof(string))
                    {
                        var items = new List<object>();
                        int index = 0;
                        while (true)
                        {
                            var item = Activator.CreateInstance(elementType)!;
                            bool hasAny = false;
                            foreach (var prop in elementType.GetProperties())
                            {
                                var paramName = $"{kv.Key}[{index}][{prop.Name}]";
                                valueResult = valueProvider.GetValue(paramName);
                                if (valueResult != ValueProviderResult.None)
                                {
                                    hasAny = true;
                                    var value = DataConverter.ChangeType<string>(valueResult.FirstValue,
                                        prop.PropertyType);
                                    prop.SetValue(item, value);
                                }
                            }

                            if (!hasAny) break;
                            items.Add(item);
                            index++;
                        }

                        if (items.Count > 0)
                        {
                            if (propType.IsArray)
                                dictionary.Add(kv.Key, items.ToArray());
                            else
                                dictionary.Add(kv.Key, items);
                            continue;
                        }
                    }

                    // 原有简单数组处理（如 status[]）
                    valueResult = valueProvider.GetValue(kv.Key + "[]");
                    if (valueResult == ValueProviderResult.None)
                    {
                        // 尝试 status=1,2,3 的形式
                        valueResult = valueProvider.GetValue(kv.Key);
                        if (valueResult != ValueProviderResult.None && valueResult.FirstValue != null &&
                            valueResult.FirstValue.Contains(','))
                        {
                            var values = valueResult.FirstValue.Split(',')
                                .Select(v => DataConverter.ChangeType<string>(v.Trim(), elementType))
                                .ToArray();

                            if (propType.IsArray)
                                dictionary.Add(kv.Key, values);
                            else
                                dictionary.Add(kv.Key, values.ToList());
                            continue;
                        }
                    }

                    if (valueResult != ValueProviderResult.None)
                    {
                        var values = valueResult.Values
                            .SelectMany(v => v?.Split(',') ?? Enumerable.Empty<string>())
                            .Select(v => DataConverter.ChangeType<string>(v.Trim(), elementType))
                            .ToArray();
                        if (propType.IsArray)
                            dictionary.Add(kv.Key, values);
                        else
                            dictionary.Add(kv.Key, values.ToList());
                        continue;
                    }
                }
                else
                {
                    // 普通类型只用原始 key
                    valueResult = valueProvider.GetValue(kv.Key);
                }

                if (valueResult != ValueProviderResult.None)
                {
                    if (valueResult.Values.Count > 1)
                    {
                        if (propType.IsArray)
                        {
                            dictionary.Add(kv.Key, valueResult.Values.Select(q => q?.ToString()).ToArray());
                        }
                        else if (propType.IsGenericType && propType.GetGenericTypeDefinition() == typeof(List<>))
                        {
                            dictionary.Add(kv.Key, valueResult.Values.Select(q => q?.ToString()).ToList());
                        }
                        else
                        {
                            dictionary.Add(kv.Key, valueResult.Values.Select(q => q?.ToString()).ToList());
                        }
                    }
                    else
                    {
                        dictionary.Add(kv.Key, valueResult.FirstValue);
                    }
                }
            }

            return dictionary;
        }
    }
}

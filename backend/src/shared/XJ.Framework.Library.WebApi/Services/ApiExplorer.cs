using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Mvc.Routing;
using Microsoft.Extensions.Options;
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Library.WebApi.Services;

public class ApiExplorer
{
    private readonly IActionDescriptorCollectionProvider _actionDescriptorCollectionProvider;
    private readonly IOptions<ApplicationOption> _options;

    public ApiExplorer(IActionDescriptorCollectionProvider actionDescriptorCollectionProvider,
        IOptions<ApplicationOption> options)
    {
        _actionDescriptorCollectionProvider = actionDescriptorCollectionProvider;
        _options = options;
    }

    public List<ApiEndpointInfo> GetAllApiEndpoints()
    {
        var endpoints = new List<ApiEndpointInfo>();

        var actionDescriptors = _actionDescriptorCollectionProvider.ActionDescriptors.Items;

        var assembly = Assembly.GetEntryAssembly()!;

        var xmlFile = $"{assembly.GetName().Name}.xml";
        var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
        XmlDocumentationProvider? docProvider = null;
        if (File.Exists(xmlPath))
        {
            docProvider = new XmlDocumentationProvider(xmlPath);
        }

        foreach (var descriptor in actionDescriptors)
        {
            // 过滤掉非Controller的ActionDescriptor
            if (!(descriptor is ControllerActionDescriptor controllerActionDescriptor))
                continue;

            // 获取Controller信息
            var controllerTypeInfo = controllerActionDescriptor.ControllerTypeInfo;
            var controllerName = controllerActionDescriptor.ControllerName;

            // 获取Controller的注释


            var actionName = controllerActionDescriptor.ActionName;
            var methodInfo = controllerActionDescriptor.MethodInfo;

            // 获取Controller和Action的注释
            var controllerDescription = string.Empty;
            var actionDescription = string.Empty;

            if (docProvider != null)
            {
                controllerDescription = docProvider.GetTypeDocumentation(controllerTypeInfo.AsType());
                actionDescription = docProvider.GetMethodDocumentation(methodInfo);
            }

            var allowAnonymous = controllerActionDescriptor.EndpointMetadata
                .OfType<AllowAnonymousAttribute>()
                .FirstOrDefault();

            if (allowAnonymous == null)
            {
                var publicPermission = controllerActionDescriptor.EndpointMetadata
                    .OfType<PublicPermissionAttribute>()
                    .FirstOrDefault();

                var requirePermission = controllerActionDescriptor.EndpointMetadata
                    .OfType<RequirePermissionAttribute>()
                    .FirstOrDefault();

                // 获取路由信息
                var routeTemplate =
                    descriptor.AttributeRouteInfo!.Template!; //GetRouteTemplate(controllerActionDescriptor);

                // 获取HTTP方法
                var httpMethods = GetHttpMethods(controllerActionDescriptor);

                // 创建API端点信息
                var endpoint = new ApiEndpointInfo
                {
                    ApplicationCode = _options.Value.ApplicationCode,
                    ControllerName = controllerName,
                    ControllerDescription = controllerDescription,
                    ActionName = actionName,
                    ActionDescription = actionDescription,
                    RouteTemplate = routeTemplate,
                    HttpMethods = httpMethods,
                    Public = publicPermission != null,
                    NamedCode = requirePermission?.PermissionCode
                };

                endpoints.Add(endpoint);
            }
        }

        return endpoints;
    }

    private string GetRouteTemplate(ControllerActionDescriptor descriptor)
    {
        // 尝试获取Action级别的路由
        var actionRouteTemplates = descriptor.EndpointMetadata
            .OfType<RouteAttribute>()
            .Select(r => r.Template)
            .ToList();

        if (actionRouteTemplates.Any())
            return actionRouteTemplates.First();

        // 尝试获取Controller级别的路由
        var controllerRouteTemplates = descriptor.ControllerTypeInfo
            .GetCustomAttributes<RouteAttribute>(true)
            .Select(r => r.Template)
            .ToList();

        var controllerRoute = controllerRouteTemplates.FirstOrDefault() ?? string.Empty;

        // 尝试获取Action级别的HTTP方法特性中的路由
        var httpMethodAttributes = descriptor.MethodInfo
            .GetCustomAttributes<HttpMethodAttribute>(true);

        foreach (var httpMethodAttribute in httpMethodAttributes)
        {
            if (!string.IsNullOrEmpty(httpMethodAttribute.Template))
                return CombineRoutes(controllerRoute, httpMethodAttribute.Template);
        }

        // 如果没有明确的路由，则使用默认的路由模板
        return CombineRoutes(controllerRoute, descriptor.ActionName);
    }

    private string CombineRoutes(string baseRoute, string routeTemplate)
    {
        if (string.IsNullOrEmpty(baseRoute))
            return routeTemplate;

        if (string.IsNullOrEmpty(routeTemplate))
            return baseRoute;

        return $"{baseRoute.TrimEnd('/')}/{routeTemplate.TrimStart('/')}";
    }

    private List<string> GetHttpMethods(ControllerActionDescriptor descriptor)
    {
        var httpMethods = new List<string>();

        // 获取HTTP方法特性
        var httpMethodAttributes = descriptor.MethodInfo
            .GetCustomAttributes<HttpMethodAttribute>(true);

        foreach (var httpMethodAttribute in httpMethodAttributes)
        {
            httpMethods.AddRange(httpMethodAttribute.HttpMethods);
        }

        // 如果没有明确的HTTP方法，则默认为GET
        if (!httpMethods.Any())
            httpMethods.Add("GET");

        return httpMethods;
    }
}

public class ApiEndpointInfo
{
    /// <summary>
    /// 应用编码
    /// </summary>
    public string ApplicationCode { get; set; } = null!;

    /// <summary>
    /// Controller名称
    /// </summary>
    public string ControllerName { get; set; } = null!;

    /// <summary>
    /// Controller说明
    /// </summary>

    public string? ControllerDescription { get; set; }

    /// <summary>
    /// Action名称
    /// </summary>
    public string ActionName { get; set; } = null!;

    /// <summary>
    /// Action说明
    /// </summary>
    public string? ActionDescription { get; set; }

    /// <summary>
    /// Action路由模板
    /// </summary>
    public string RouteTemplate { get; set; } = null!;

    /// <summary>
    /// Action的HTTP方法集合
    /// </summary>
    public List<string> HttpMethods { get; set; } = new();

    /// <summary>
    /// 是否为公开权限
    /// </summary>
    public bool Public { get; set; }

    /// <summary>
    /// 是否指定了命名编码
    /// </summary>
    public string? NamedCode { get; set; }

    public override string ToString()
    {
        return $"{string.Join(", ", HttpMethods)} {RouteTemplate} ({ControllerName}.{ActionName})";
    }
}

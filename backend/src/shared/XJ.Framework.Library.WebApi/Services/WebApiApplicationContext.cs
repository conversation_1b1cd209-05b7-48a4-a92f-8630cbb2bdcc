using Microsoft.Extensions.Options;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Library.WebApi.Services;

public class WebApiApplicationContext : ICurrentApplicationContext
{
    private readonly IOptions<ApplicationOption> _applicationOption;
    private readonly IOptions<SecretKeyOption> _secretKeyOption;

    public WebApiApplicationContext(IOptions<ApplicationOption> applicationOption,
        IOptions<SecretKeyOption> secretKeyOption)
    {
        _applicationOption = applicationOption;
        _secretKeyOption = secretKeyOption;
    }

    public string GetApplicationCode()
    {
        return _applicationOption.Value.ApplicationCode;
    }

    public string GetSecretKey()
    {
        return _secretKeyOption.Value[_applicationOption.Value.ApplicationCode]!.SecretKey;
    }
}

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace XJ.Framework.Library.WebApi.Services;

/// <summary>
/// 服务代理工厂实现
/// </summary>
public class ServiceProxyFactory : IServiceProxyFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ServiceProxyFactory> _logger;
    private readonly ServiceProxyOptions _options;

    public ServiceProxyFactory(
        IServiceProvider serviceProvider,
        ILogger<ServiceProxyFactory> logger,
        IOptions<ServiceProxyOptions> options)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _options = options.Value;
    }

    public IServiceProxy<TService> CreateProxy<TService>() where TService : class
    {
        var serviceType = typeof(TService);
        var serviceName = serviceType.Name;
        
        // 获取服务特定配置
        var config = _options.ServiceConfigs.GetValueOrDefault(serviceName) ?? new ServiceProxyConfig
        {
            CallMode = _options.DefaultCallMode
        };

        return new ServiceProxy<TService>(_serviceProvider, _logger, config);
    }
}

/// <summary>
/// 服务代理实现
/// </summary>
/// <typeparam name="TService">服务接口类型</typeparam>
public class ServiceProxy<TService> : IServiceProxy<TService> where TService : class
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger _logger;
    private readonly ServiceProxyConfig _config;
    private readonly string _serviceName;

    public ServiceProxy(
        IServiceProvider serviceProvider,
        ILogger logger,
        ServiceProxyConfig config)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
        _config = config;
        _serviceName = typeof(TService).Name;
    }

    public TService GetService()
    {
        return GetServiceAsync().GetAwaiter().GetResult();
    }

    public async Task<TService> GetServiceAsync()
    {
        try
        {
            return _config.CallMode switch
            {
                ServiceCallMode.Local => GetLocalService(),
                ServiceCallMode.Remote => GetRemoteService(),
                ServiceCallMode.Auto => GetAutoService(),
                _ => throw new ArgumentOutOfRangeException()
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get service {ServiceName}", _serviceName);
            throw;
        }
    }

    private TService GetLocalService()
    {
        // 尝试从DI容器获取本地服务实现
        var localService = _serviceProvider.GetService<TService>();
        if (localService == null)
        {
            throw new InvalidOperationException($"Local service {_serviceName} not available");
        }

        _logger.LogDebug("Using local service {ServiceName}", _serviceName);
        return localService;
    }

    private TService GetRemoteService()
    {
        // 获取对应的ApiClient作为远程服务实现
        var remoteService = _serviceProvider.GetService<TService>();
        if (remoteService == null)
        {
            throw new InvalidOperationException($"Remote service {_serviceName} not available");
        }

        _logger.LogDebug("Using remote service {ServiceName}", _serviceName);
        return remoteService;
    }

    private TService GetAutoService()
    {
        // 自动选择：优先本地服务，如果不存在则使用远程服务
        var localService = _serviceProvider.GetService<TService>();
        if (localService != null)
        {
            // 检查是否为ApiClient（远程服务）
            var serviceTypeName = localService.GetType().Name;
            if (serviceTypeName.EndsWith("ApiClient"))
            {
                // 这是ApiClient，说明没有本地实现，使用远程
                _logger.LogDebug("Using remote service {ServiceName} (ApiClient)", _serviceName);
                return localService;
            }
            else
            {
                // 这是本地服务实现
                _logger.LogDebug("Using local service {ServiceName}", _serviceName);
                return localService;
            }
        }

        throw new InvalidOperationException($"Service {_serviceName} not available (neither local nor remote)");
    }
}

using Microsoft.AspNetCore.Http;
using XJ.Framework.Library.Domain.Shared.Interfaces;

namespace XJ.Framework.Library.WebApi.Services;

/// <summary>
/// WebApi层的当前用户上下文服务实现
/// </summary>
public class WebApiCurrentUserContext : ICurrentUserContext
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public WebApiCurrentUserContext(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    /// <summary>
    /// 获取当前用户
    /// </summary>
    public UserProfileDto? GetCurrentUser()
    {
        return _httpContextAccessor.HttpContext?.Items["CurrentUser"] as UserProfileDto;
    }

    /// <summary>
    /// 获取当前用户ID
    /// </summary>
    public long? GetCurrentUserId()
    {
        return GetCurrentUser()?.Key;
    }

    /// <summary>
    /// 当前是否已认证
    /// </summary>
    public bool IsAuthenticated => GetCurrentUser() != null;

    public string GetAccessToken()
    {
        return _httpContextAccessor.HttpContext?.Request.Headers["Authorization"].FirstOrDefault() ?? string.Empty;
    }

    public string GetClientIp()
    {
        if (_httpContextAccessor.HttpContext?.Request.Headers.TryGetValue("x-client-ip", out var clientIp) ?? false)
        {
            return clientIp.ToString();
        }

        // 1. 先查 X-Forwarded-For
        var ip = _httpContextAccessor.HttpContext?.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ip))
        {
            // 可能有多个IP，用逗号分隔，取第一个
            ip = ip.Split(',').First().Trim();
            if (!string.IsNullOrEmpty(ip))
                return ip;
        }

        // 2. 再查 X-Real-IP
        ip = _httpContextAccessor.HttpContext?.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ip))
            return ip;

        // 3. 最后用 RemoteIpAddress
        return _httpContextAccessor.HttpContext?.Connection.RemoteIpAddress?.ToString() ?? "unknown";
    }
}

using System.IdentityModel.Tokens.Jwt;

namespace XJ.Framework.Library.WebApi.Services;

public class WebApiAuthInfoGetter : IAuthInfoGetter
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public WebApiAuthInfoGetter(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public async Task<(string? schema, string? accessToken, string? deviceId, string? deviceInfo, string? tokenId, long?
            exp, long? sub, string? uniqueName)>
        GetAuthInfoAsync()
    {
        if (_httpContextAccessor.HttpContext == null)
        {
            return default;
        }

        var authorization = _httpContextAccessor.HttpContext!.Request.Headers["Authorization"].FirstOrDefault()
            ?.Split(" ");
        var deviceId = _httpContextAccessor.HttpContext!.Request.Headers["x-device-id"].FirstOrDefault();
        var deviceInfo = _httpContextAccessor.HttpContext!.Request.Headers["x-device-info"].FirstOrDefault();
        var accessToken = authorization?.Last();
        var schema = authorization?.First();
        var tokenId = GetJti(accessToken);
        var exp = GetExpiration(accessToken);
        var sub = GetSub(accessToken);
        var uniqueName = GetUniqueName(accessToken);

        return await Task.FromResult((schema, accessToken, deviceId, deviceInfo, tokenId, exp, sub, uniqueName));
    }

    private long? GetExpiration(string? token)
    {
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }

        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(token);

        // 获取过期时间
        var exp = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Exp)?.Value;
        return long.Parse(exp!);
    }

    private string? GetJti(string? token)
    {
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }

        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(token);

        // 获取 jti
        var jti = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;
        return jti;
    }

    private long? GetSub(string? token)
    {
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }

        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(token);

        // 获取 sub
        var sub = jwtToken.Claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Sub)?.Value;
        return long.Parse(sub!);
    }

    private string? GetUniqueName(string? token)
    {
        if (string.IsNullOrEmpty(token))
        {
            return null;
        }

        var tokenHandler = new JwtSecurityTokenHandler();
        var jwtToken = tokenHandler.ReadJwtToken(token);

        // 获取 unique_name
        var uniqueName = jwtToken.Claims.FirstOrDefault(c => c.Type == "unique_name")?.Value;
        return uniqueName;
    }
}

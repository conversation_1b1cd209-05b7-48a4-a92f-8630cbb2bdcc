<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\Common.props"/>
    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions"/>
    </ItemGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.Extensions.Configuration.UserSecrets"/>
        <PackageReference Include="Swashbuckle.AspNetCore"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Common\XJ.Framework.Library.Common.csproj" />
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Image\XJ.Framework.Library.Image.csproj"/>
        <ProjectReference Include="..\..\core\impls\XJ.Framework.Library.Logging.Database\XJ.Framework.Library.Logging.Database.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.Domain.Shared\XJ.Framework.Library.Domain.Shared.csproj"/>
        <ProjectReference Include="..\..\domains\rbac\XJ.Framework.Rbac.Domain.Shared\XJ.Framework.Rbac.Domain.Shared.csproj" />
        <ProjectReference Include="..\..\domains\rbac\XJ.Framework.Rbac.Application.Contract\XJ.Framework.Rbac.Application.Contract.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Controllers\BaseAuditController.cs"/>
        <Compile Remove="Controllers\BaseSoftDeleteController.cs"/>
        <Compile Remove="Controllers\IAppController.cs"/>
        <Compile Remove="New\ContextContainerMiddleware.cs" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="System.IdentityModel.Tokens.Jwt"/>
    </ItemGroup>
</Project>

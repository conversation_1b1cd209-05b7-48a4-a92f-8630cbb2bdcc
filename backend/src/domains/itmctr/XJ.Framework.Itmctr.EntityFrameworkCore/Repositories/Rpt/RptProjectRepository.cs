using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.EntityFrameworkCore;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// 项目 仓储实现  
/// </summary>  
public class RptProjectRepository : BaseAuditRepository<ItmctrDbContext, long, RptProjectEntity>,
   IRptProjectRepository
{
    public RptProjectRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {

    }

}

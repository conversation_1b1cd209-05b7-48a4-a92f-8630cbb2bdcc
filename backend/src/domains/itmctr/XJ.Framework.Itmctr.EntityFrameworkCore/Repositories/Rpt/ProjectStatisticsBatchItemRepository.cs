using System.Linq.Expressions;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.EntityFrameworkCore;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>
/// ProjectStatisticsBatchItem 仓储实现
/// </summary>
public class ProjectStatisticsBatchItemRepository :
    BaseEditableRepository<ItmctrDbContext, long, ProjectStatisticsBatchItemEntity>,
    IProjectStatisticsBatchItemRepository
{
    public ProjectStatisticsBatchItemRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }

    public async Task<bool> DetachAndUpdateAsync(List<ProjectStatisticsBatchItemEntity> entities,
        bool isSaveChange = true)
    {
        entities.ForEach(entity =>
        {
            DetachWhenExists(entity.Key);
        });
        return await base.UpdateAsync(entities, isSaveChange);
    }
}

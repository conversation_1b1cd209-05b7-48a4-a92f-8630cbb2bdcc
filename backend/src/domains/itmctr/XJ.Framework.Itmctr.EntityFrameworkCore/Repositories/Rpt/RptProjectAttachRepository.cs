using XJ.Framework.Itmctr.EntityFrameworkCore;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// AsyncTask 仓储实现  
/// </summary>  
public class RptProjectAttachRepository : BaseAuditRepository<ItmctrDbContext, Guid, RptProjectAttachEntity>,
    IRptProjectAttachRepository
{
    public RptProjectAttachRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }
}

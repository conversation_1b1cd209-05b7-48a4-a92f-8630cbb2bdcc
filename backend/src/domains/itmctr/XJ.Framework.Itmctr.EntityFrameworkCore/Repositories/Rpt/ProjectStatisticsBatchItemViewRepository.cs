using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Itmctr.EntityFrameworkCore.Repositories;

public class ProjectStatisticsBatchItemViewRepository :
    BaseRepository<ItmctrDbContext, long, ProjectStatisticsBatchItemViewEntity>
    , IProjectStatisticsBatchItemViewRepository
{
    public ProjectStatisticsBatchItemViewRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    protected async override Task<IQueryable<ProjectStatisticsBatchItemViewEntity>> GetQueryableAsync()
    {
        var queryable =
            from i in DbContext.Set<ProjectStatisticsBatchItemEntity>()
            from p in DbContext.Set<RptProjectEntity>()
                .Where(p => p.ProjectStatisticsBatchItemId == i.Key)
                .DefaultIfEmpty()
            select new ProjectStatisticsBatchItemViewEntity()
            {
                Key = i.Key,
                Project = p,
                Item = i,
            };
        return await Task.FromResult(queryable);
    }

    public async new Task<PageData<long, ProjectStatisticsBatchItemViewEntity>> GetPageAsync(
        Expression<Func<ProjectStatisticsBatchItemViewEntity, bool>> whereLambda,
        int rowIndex, int pageSize,
        List<OrderbyDirection<ProjectStatisticsBatchItemViewEntity>> orderBy,
        bool isNoTracking = true)
    {
        var queryable = await GetQueryableAsync();
        var data = isNoTracking ? queryable.Where(whereLambda).AsNoTracking() : DbSet.Where(whereLambda);

        data = data.Orderby<long, ProjectStatisticsBatchItemViewEntity>(orderBy);

        var pageData = new PageData<long, ProjectStatisticsBatchItemViewEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };
        return pageData;
    }
}

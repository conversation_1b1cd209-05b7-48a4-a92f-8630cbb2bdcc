using XJ.Framework.Itmctr.EntityFrameworkCore;

namespace XJ.Framework.Itmctr.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// 干预措施 仓储实现
    /// </summary>
    public class ProjectInterventionRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectInterventionEntity>,
       IProjectInterventionRepository
    {
        public ProjectInterventionRepository(IServiceProvider serviceProvider) : base(
            serviceProvider)
        {
        }

        public async Task<bool> DetachAndInsertAsync(List<ProjectInterventionEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.InsertAsync(entities);
        }

        public async Task<bool> DetachAndDeleteAsync(List<ProjectInterventionEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.DeleteAsync(entities);
        }
    }
}

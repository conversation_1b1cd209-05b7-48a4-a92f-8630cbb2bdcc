namespace XJ.Framework.Itmctr.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// 研究实施地点 仓储实现
    /// </summary>
    public class ProjectResearchSiteRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectResearchSiteEntity>,
       IProjectResearchSiteRepository
    {
        public ProjectResearchSiteRepository(IServiceProvider serviceProvider) : base(
            serviceProvider)
        {
        }

        public async Task<bool> DetachAndInsertAsync(List<ProjectResearchSiteEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.InsertAsync(entities);
        }

        public async Task<bool> DetachAndDeleteAsync(List<ProjectResearchSiteEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.DeleteAsync(entities);
        }
    }

}

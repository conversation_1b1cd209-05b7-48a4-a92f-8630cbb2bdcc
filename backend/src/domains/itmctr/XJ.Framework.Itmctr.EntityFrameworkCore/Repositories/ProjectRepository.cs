using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.EntityFrameworkCore;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// 项目 仓储实现  
/// </summary>  
public class ProjectRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectEntity>,
    IProjectRepository
{
    public ProjectRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }

    public async Task<PageData<long, ProjectEntity>> GetProjectPageAsync(
        Expression<Func<ProjectEntity, bool>> whereLambda, Dictionary<string, string?>? dynamicQueries, int rowIndex,
        int pageSize,
        List<OrderbyDirection<ProjectEntity>> orderBy, bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(q => !q.Deleted);

        whereLambda = whereLambda.And(BuildProjectConditionQueries(dynamicQueries));

        var queryable = await GetQueryableAsync();

        var data = queryable.Where(whereLambda)
            .Orderby<long, ProjectEntity>(orderBy);

        var pageData = new PageData<long, ProjectEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };

        return pageData;
    }

    public async Task<PageData<long, ProjectEntity>> GetNewestProjectPageAsync(
        Expression<Func<ProjectEntity, bool>> whereLambda, Dictionary<string, string?>? dynamicQueries, int rowIndex,
        int pageSize,
        List<OrderbyDirection<ProjectEntity>> orderBy, bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(q => !q.Deleted);

        whereLambda = whereLambda.And(BuildProjectConditionQueries(dynamicQueries));

        var queryable = await GetNewestQueryableAsync();

        var data = queryable.Where(whereLambda)
            .Orderby<long, ProjectEntity>(orderBy);

        //添加not exists 条件
        data = data.Where(q => !DbContext.Set<ProjectEntity>().Any(p =>
            p.BusinessId.ToLower().Equals(q.BusinessId.ToLower()) &&
            p.Key != q.Key &&
            p.release_time > q.release_time
        ));

        var pageData = new PageData<long, ProjectEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };

        return pageData;
    }

    public async Task<bool> DetachAndInsertAsync(ProjectEntity entity)
    {
        DetachWhenExists(entity.Key);
        return await base.InsertAsync(entity);
    }

    public async Task<bool> DetachAndDeleteAsync(ProjectEntity entity)
    {
        DetachWhenExists(entity.Key);
        return await base.DeleteAsync(entity);
    }

    public async Task<IQueryable<ProjectEntity>> AppendFilterAsync(IQueryable<ProjectEntity> queryable,
        DateTimeOffset start, DateTimeOffset end)
    {
        return await Task.FromResult(queryable.Where(q => q.CreatedTime >= start && q.CreatedTime <= end));
    }

    public async new Task<IQueryable<ProjectEntity>> GetNewestQueryableAsync()
    {
        var query = await GetQueryableAsync();

        var newestQuery = query.Where(q => !DbContext.Set<ProjectEntity>().Any(p =>
            p.BusinessId.ToLower().Equals(q.BusinessId.ToLower()) &&
            p.Key != q.Key &&
            p.release_time > q.release_time
        ));

        return await Task.FromResult(newestQuery);
    }

    public async new Task<IQueryable<ProjectEntity>> GetQueryableAsync()
    {
        var query = DbContext.Set<ProjectEntity>();

        return await Task.FromResult(query);
    }

    private Expression<Func<ProjectEntity, bool>> BuildProjectConditionQueries(
        Dictionary<string, string?>? dynamicQueries)
    {
        dynamicQueries ??= new Dictionary<string, string?>();
        var expr = DynamicLinqExpressions.True<ProjectEntity>();
        if (dynamicQueries.TryGetValue("FormCode", out var formCode) && !string.IsNullOrWhiteSpace(formCode))
            expr = expr.And(q => q.FormCode == formCode);
        if (dynamicQueries.TryGetValue("publictitle", out var publictitle) && !string.IsNullOrWhiteSpace(publictitle))
            expr = expr.And(q =>
                (q.publictitle_en != null && q.publictitle_en.Contains(publictitle)) ||
                q.publictitle_zh != null && q.publictitle_zh.Contains(publictitle));
        if (dynamicQueries.TryGetValue("scientific_title", out var scientific_title) &&
            !string.IsNullOrWhiteSpace(scientific_title))
            expr = expr.And(q =>
                (q.scientific_title_en != null && q.scientific_title_en.Contains(scientific_title)) ||
                q.scientific_title_zh != null && q.scientific_title_zh.Contains(scientific_title));
        if (dynamicQueries.TryGetValue("study_subject_id", out var study_subject_id) &&
            !string.IsNullOrWhiteSpace(study_subject_id))
            expr = expr.And(q => q.study_subject_id != null && q.study_subject_id.Contains(study_subject_id));
        if (dynamicQueries.TryGetValue("registration_status", out var registration_status) &&
            !string.IsNullOrWhiteSpace(registration_status))
            expr = expr.And(q => q.registration_status == registration_status);
        if (dynamicQueries.TryGetValue("registration_number", out var registration_number) &&
            !string.IsNullOrWhiteSpace(registration_number))
            expr = expr.And(q => q.registration_number != null && q.registration_number.Contains(registration_number));
        if (dynamicQueries.TryGetValue("partner_registry_number", out var partner_registry_number) &&
            !string.IsNullOrWhiteSpace(partner_registry_number))
            expr = expr.And(q =>
                q.partner_registry_number != null && q.partner_registry_number.Contains(partner_registry_number));
        if (dynamicQueries.TryGetValue("applicant", out var applicant) && !string.IsNullOrWhiteSpace(applicant))
            expr = expr.And(q =>
                (q.applicant_en != null && q.applicant_en.Contains(applicant)) ||
                q.applicant_zh != null && q.applicant_zh.Contains(applicant));
        if (dynamicQueries.TryGetValue("study_leader", out var study_leader) &&
            !string.IsNullOrWhiteSpace(study_leader))
            expr = expr.And(q =>
                (q.study_leader_en != null && q.study_leader_en.Contains(study_leader)) ||
                q.study_leader_zh != null && q.study_leader_zh.Contains(study_leader));
        if (dynamicQueries.TryGetValue("primary_sponsor", out var primary_sponsor) &&
            !string.IsNullOrWhiteSpace(primary_sponsor))
            expr = expr.And(q =>
                (q.primary_sponsor_en != null && q.primary_sponsor_en.Contains(primary_sponsor)) ||
                q.primary_sponsor_zh != null && q.primary_sponsor_zh.Contains(primary_sponsor));
        if (dynamicQueries.TryGetValue("funding_source", out var funding_source) &&
            !string.IsNullOrWhiteSpace(funding_source))
            expr = expr.And(q =>
                (q.funding_source_en != null && q.funding_source_en.Contains(funding_source)) ||
                q.funding_source_zh != null && q.funding_source_zh.Contains(funding_source));
        if (dynamicQueries.TryGetValue("target_disease", out var target_disease) &&
            !string.IsNullOrWhiteSpace(target_disease))
            expr = expr.And(q =>
                (q.target_disease_en != null && q.target_disease_en.Contains(target_disease)) ||
                q.target_disease_zh != null && q.target_disease_zh.Contains(target_disease));
        if (dynamicQueries.TryGetValue("target_disease_code", out var target_disease_code) &&
            !string.IsNullOrWhiteSpace(target_disease_code))
            expr = expr.And(q => q.target_disease_code != null && q.target_disease_code.Contains(target_disease_code));
        if (dynamicQueries.TryGetValue("study_type", out var study_type) && !string.IsNullOrWhiteSpace(study_type))
            expr = expr.And(q => q.study_type == study_type);
        if (dynamicQueries.TryGetValue("study_phase", out var study_phase) && !string.IsNullOrWhiteSpace(study_phase))
            expr = expr.And(q => q.study_phase == study_phase);
        if (dynamicQueries.TryGetValue("study_design", out var study_design) &&
            !string.IsNullOrWhiteSpace(study_design))
            expr = expr.And(q => q.study_design == study_design);
        if (dynamicQueries.TryGetValue("study_time_start", out var study_time_start) &&
            !string.IsNullOrWhiteSpace(study_time_start) &&
            DateTime.TryParse(study_time_start, out var dt_study_time_start))
            expr = expr.And(q => q.study_time_start.HasValue && q.study_time_start >= dt_study_time_start);
        if (dynamicQueries.TryGetValue("study_time_end", out var study_time_end) &&
            !string.IsNullOrWhiteSpace(study_time_end) && DateTime.TryParse(study_time_end, out var dt_study_time_end))
            expr = expr.And(q => q.study_time_end.HasValue && q.study_time_end >= dt_study_time_end);
        if (dynamicQueries.TryGetValue("recruiting_status", out var recruiting_status) &&
            !string.IsNullOrWhiteSpace(recruiting_status))
            expr = expr.And(q => q.recruiting_status == recruiting_status);
        if (dynamicQueries.TryGetValue("gender", out var gender) && !string.IsNullOrWhiteSpace(gender))
            expr = expr.And(q => q.gender == gender);
        if (dynamicQueries.TryGetValue("sign_informed_consent", out var sign_informed_consent) &&
            !string.IsNullOrWhiteSpace(sign_informed_consent))
            expr = expr.And(q => q.sign_informed_consent == sign_informed_consent);

        if (dynamicQueries.TryGetValue("ethic_committee_approved", out var ethic_committee_approved) &&
            !string.IsNullOrWhiteSpace(ethic_committee_approved))
            expr = expr.And(q => q.ethic_committee_approved == ethic_committee_approved);
        if (dynamicQueries.TryGetValue("calculated_results_public", out var calculated_results_public) &&
            !string.IsNullOrWhiteSpace(calculated_results_public))
            expr = expr.And(q => q.calculated_results_public == calculated_results_public);


        //试验主办单位
        if (dynamicQueries.TryGetValue("sponsor_institution", out var sponsor_institution) &&
            !string.IsNullOrWhiteSpace(sponsor_institution))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectSponsorEntity>().Any(sponsor =>
                    sponsor.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    sponsor.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (sponsor.sponsor_institution_en != null &&
                     sponsor.sponsor_institution_en.Contains(sponsor_institution)) ||
                    (sponsor.sponsor_institution_zh != null &&
                     sponsor.sponsor_institution_zh.Contains(sponsor_institution))
                )
            );
        }

        //实施地点 国家  
        if (dynamicQueries.TryGetValue("site_country", out var site_country) &&
            !string.IsNullOrWhiteSpace(site_country))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectResearchSiteEntity>().Any(sponsor =>
                    sponsor.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    sponsor.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (sponsor.site_country_en != null && sponsor.site_country_en.Contains(site_country)) ||
                    (sponsor.site_country_zh != null && sponsor.site_country_zh.Contains(site_country))
                )
            );
        }

        //实施地点 省  
        if (dynamicQueries.TryGetValue("site_province", out var site_province) &&
            !string.IsNullOrWhiteSpace(site_province))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectResearchSiteEntity>().Any(site =>
                    site.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    site.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (site.site_province_en != null && site.site_province_en.Contains(site_province)) ||
                    (site.site_province_zh != null && site.site_province_zh.Contains(site_province))
                )
            );
        }

        //实施地点 市  
        if (dynamicQueries.TryGetValue("site_city", out var site_city) && !string.IsNullOrWhiteSpace(site_city))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectResearchSiteEntity>().Any(site =>
                    site.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    site.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (site.site_city_en != null && site.site_city_en.Contains(site_city)) ||
                    (site.site_city_zh != null && site.site_city_zh.Contains(site_city))
                )
            );
        }

        //实施地点 单位  
        if (dynamicQueries.TryGetValue("site_institution", out var site_institution) &&
            !string.IsNullOrWhiteSpace(site_institution))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectResearchSiteEntity>().Any(site =>
                    site.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    site.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (site.site_institution_en != null && site.site_institution_en.Contains(site_institution)) ||
                    (site.site_institution_zh != null && site.site_institution_zh.Contains(site_institution))
                )
            );
        }

        //实施地点 单位级别  
        if (dynamicQueries.TryGetValue("site_level", out var site_level) && !string.IsNullOrWhiteSpace(site_level))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectResearchSiteEntity>().Any(site =>
                    site.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    site.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (site.site_level_en != null && site.site_level_en.Contains(site_level)) ||
                    (site.site_level_zh != null && site.site_level_zh.Contains(site_level))
                )
            );
        }

        //干预措施 干预措施  
        if (dynamicQueries.TryGetValue("intervention_name", out var intervention_name) &&
            !string.IsNullOrWhiteSpace(intervention_name))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectInterventionEntity>().Any(intervention =>
                    intervention.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    intervention.Version.ToLower().Equals(project.Version.ToLower()) &&
                    (intervention.intervention_name_en != null &&
                     intervention.intervention_name_en.Contains(intervention_name)) ||
                    (intervention.intervention_name_zh != null &&
                     intervention.intervention_name_zh.Contains(intervention_name))
                )
            );
        }

        //干预措施 干预措施代码  
        if (dynamicQueries.TryGetValue("intervention_code", out var intervention_code) &&
            !string.IsNullOrWhiteSpace(intervention_code))
        {
            expr = expr.And(project =>
                DbContext.Set<ProjectInterventionEntity>().Any(intervention =>
                    intervention.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                    intervention.Version.ToLower().Equals(project.Version.ToLower()) &&
                    intervention.intervention_code != null && intervention.intervention_code.Contains(intervention_code)
                )
            );
        }

        //上传试验完成后的统计结果
        if (dynamicQueries.TryGetValue("statistical_results_file", out var statistical_results_file) &&
            !string.IsNullOrWhiteSpace(statistical_results_file))
        {
            if (statistical_results_file == "True")
            {
                expr = expr.And(project =>
                    DbContext.Set<ProjectAttachEntity>().Any(attach =>
                        attach.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                        attach.Version.ToLower().Equals(project.Version.ToLower()) &&
                        attach.FileTypeCode == "statistical_results_file"
                    )
                );
            }
            else
            {
                expr = expr.And(project =>
                    !DbContext.Set<ProjectAttachEntity>().Any(attach =>
                        attach.BusinessId.ToLower().Equals(project.BusinessId.ToLower()) &&
                        attach.Version.ToLower().Equals(project.Version.ToLower()) &&
                        attach.FileTypeCode == "statistical_results_file"
                    )
                );
            }
        }

        //
        return expr;
    }
}

using XJ.Framework.Itmctr.EntityFrameworkCore;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// AsyncTask 仓储实现  
/// </summary>  
public class ProjectAttachRepository : BaseAuditRepository<ItmctrDbContext, Guid, ProjectAttachEntity>,
   IProjectAttachRepository
{
    public ProjectAttachRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }

    public async Task<bool> DetachAndInsertAsync(List<ProjectAttachEntity> entities)
    {
        entities.ForEach(entity =>
        {
            DetachWhenExists(entity.Key);
        });
        return await base.InsertAsync(entities);
    }

    public async Task<bool> DetachAndDeleteAsync(List<ProjectAttachEntity> entities)
    {
        entities.ForEach(entity =>
        {
            DetachWhenExists(entity.Key);
        });
        return await base.DeleteAsync(entities);
    }
}

using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Services;
using XJ.Framework.Itmctr.WebApi.Mgt.Works;

namespace XJ.Framework.Itmctr.WebApi;

public class ItmctrWebApiMgtWrapper : WebApiMgtWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<ItmctrApplicationWrapper, ItmctrInfrastructureWrapper>(configuration);

        services.AddHostedService<ProcessProjectToResultService>();

        services.AddHostedService<ProcessExtractProjectConvertService>();

        services.AddHostedService<ProcessSubmitContentCompareService>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}

using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Application.Services;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Itmctr.WebApi.Mgt.Controllers;

[ApiController]
[Route("[controller]")]
public class ProjectStatisticsController : ControllerBase
{
    private readonly IProjectStatisticsService _projectStatisticsService;

    public ProjectStatisticsController(IProjectStatisticsService projectStatisticsService)
    {
        _projectStatisticsService = projectStatisticsService;
    }

    [HttpPost("create-batch")]
    [UnitOfWork]
    public async Task<long> CreateBatchAsync([FromBody] CreateProjectStatisticsBatchRequest request)
    {
        return await _projectStatisticsService.CreateBatchAsync(request);
    }

    [HttpGet("{batchId:long}")]
    public async Task<ProjectStatisticsBatchDto?> GetProjectStatisticsBatcheAsync(long batchId)
    {
        return await _projectStatisticsService.GetByIdAsync(batchId);
    }

    [HttpGet("get-batches")]
    public async Task<PageDtoData<long, ProjectStatisticsBatchDto>> GetProjectStatisticsBatchesAsync(
        [FromQuery] PagedQueryCriteria<ProjectStatisticsQueryCriteria> criteria)
    {
        return await _projectStatisticsService.GetPageAsync(criteria);
    }

    [IgnoreLogging]
    [HttpGet("get-batch-items/{batchId}")]
    public async Task<PageDtoData<long, ProjectStatisticsBatchItemViewDto>> GetProjectStatisticsBatchItemsAsync(
        long batchId,
        [FromQuery] PagedQueryCriteria<ProjectStatisticsBatchItemViewQueryCriteria> criteria)
    {
        return await _projectStatisticsService.GetPageItemsAsync(batchId, criteria);
    }

    [HttpPost("export/{batchId}")]
    public async Task<FileDto> ExportAsync(long batchId)
    {
        return await _projectStatisticsService.ExportAsync(batchId);
    }

    [UnitOfWork]
    [HttpPost("re-statistics/{batchId}")]
    public async Task<bool> ReStatisticsAsync(long batchId)
    {
        return await _projectStatisticsService.ReStatisticsAsync(batchId);
    }

    [HttpPost("remove-item")]
    [UnitOfWork]
    public async Task<bool> RemoveItemAsync([FromBody] RemoveProjectStatisticsBatchItemRequest request)
    {
        return await _projectStatisticsService.RemoveItemAsync(request.BatchId, request.ItemId);
    }

    [HttpPost("add-item")]
    [UnitOfWork]
    public async Task<bool> AddItemAsync([FromBody] AddProjectStatisticsBatchItemRequest request)
    {
        return await _projectStatisticsService.AddItemAsync(request.BatchId, request.ProjectIds);
    }

    [HttpGet("query/{batchId}")]
    public async Task<PageDtoData<long, ProjectsDto>> GetProjectStatisticsQueryAsync(
        long batchId,
        [FromQuery] PagedQueryCriteria<RptProjectQueryCriteria> criteria)
    {
        return await _projectStatisticsService.GetProjectStatisticsQueryAsync(batchId, criteria);
    }

    [HttpPost("create-extract-project-task")]
    public async Task<bool> CreateExtractProjectConvertTaskAsync([FromBody] CreateXmlConvertTaskRequest request)
    {
        return await _projectStatisticsService.CreateExtractProjectTaskAsync(request.BatchId, request.Force,
            request.ItemIds);
    }
}

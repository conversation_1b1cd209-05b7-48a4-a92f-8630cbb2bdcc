using AutoMapper;
using System.Diagnostics;
using System.Text.Json;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Itmctr.WebApi.Mgt.Works;

public class ProcessProjectToResultService : BackgroundService
{
    private readonly ILogger<ProcessProjectToResultService> _logger;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IMapper _mapper;
    private readonly IContextContainer _contextContainer;
    private readonly IServiceScopeFactory _serviceScopeFactory;


    public ProcessProjectToResultService(ILogger<ProcessProjectToResultService> logger,
        IServiceScopeFactory serviceScopeFactory, IMapper mapper, IContextContainer contextContainer)
    {
        _logger = logger;
        _serviceScopeFactory = serviceScopeFactory;

        var scope = _serviceScopeFactory.CreateScope();
        _asyncTaskService = scope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
        _mapper = mapper;
        _contextContainer = contextContainer;
    }

    protected async override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var pending = await _asyncTaskService.GetPageAsync(new PagedQueryCriteria<AsyncTaskQueryCriteria>()
            {
                Condition = new AsyncTaskQueryCriteria
                {
                    TaskStatus = AsyncTaskStatus.Pending,
                    TaskCode = "ProcessProjectToResult"
                },
                PageParams = new PageRequestParams(1, 10)
            });

            await pending.Rows.ForEachAsync(async asyncTask =>
            {
                _contextContainer.Init();
                var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);
                
                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var dict = JsonSerializer.Deserialize<Dictionary<string, object?>>(asyncTask.TaskData)!;

                    var businessId = dict["BusinessId"]!.ToString()!;
                    var version = dict["Version"]!.ToString()!;
                    var formCode = dict["FormCode"]!.ToString()!;


                    var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();
                    var asyncTaskService = scope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
                    var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                    await unitOfWork.BeginTransactionAsync(stoppingToken);
                    try
                    {
                        Stopwatch watch = new();
                        watch.Start();

                        // _logger.LoggingInformation("project-to-result",
                        //     "开始处理项目到结果表,BusinessId:{BusinessId},Version:{Version}", businessId, version);


                        // asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Processing;
                        // await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);


                        _logger.LoggingInformation("project-to-result",
                            "开始处理项目到结果表,BusinessId:{BusinessId},Version:{Version}", businessId, version);


                        await projectService.InsertProjectAndHistoryAsync(formCode, businessId, version);
                        //
                        // await _projectService.SaveToResultDataAsync(formCode, businessId, version, language, dataValue,
                        //     formData);

                        asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Completed;
                        await asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);

                        watch.Stop();

                        _logger.LoggingInformation("project-to-result",
                            "处理项目到结果表完成,BusinessId:{BusinessId},Version:{Version},Duration:{Duration}", businessId,
                            version,
                            watch.Elapsed.TotalSeconds);
                        await unitOfWork.CommitAsync(stoppingToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LoggingException("project-to-result", ex,
                            "处理项目到结果表失败,BusinessId:{BusinessId},Version:{Version}", businessId, version);

                        await unitOfWork.RollbackAsync(stoppingToken);
                        
                        asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Failed;
                        asyncTaskOperationDto.TaskResult = ex.Message;
                    }
                }

                // 在事务外更新任务状态
                if (asyncTaskOperationDto.TaskStatus == AsyncTaskStatus.Failed)
                {
                    using (var updateScope = _serviceScopeFactory.CreateScope())
                    {
                        var updateAsyncTaskService = updateScope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
                        await updateAsyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
                    }
                }
            });

            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
}

using AutoMapper;
using System.Data;
using System.Diagnostics;
using System.Text.Json;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Application.Services;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Diagnostics;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Itmctr.WebApi.Mgt.Works;

public class ProcessExtractProjectConvertService : BackgroundService
{
    private readonly ILogger<ProcessProjectToResultService> _logger;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IMapper _mapper;
    private readonly IContextContainer _contextContainer;
    private readonly IServiceScopeFactory _serviceScopeFactory;


    public ProcessExtractProjectConvertService(ILogger<ProcessProjectToResultService> logger,
        IMapper mapper, IContextContainer contextContainer,
        IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;


        _logger = logger;
        _mapper = mapper;
        _contextContainer = contextContainer;

        _asyncTaskService = _serviceScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IAsyncTaskService>();
    }

    private static List<T>? JsonElementToObjectList<T>(object? obj)
    {
        if (obj is List<T> list)
        {
            return list;
        }

        if (obj is System.Text.Json.JsonElement je && je.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<T>>(je.GetRawText());
        }

        return null;
    }

    protected async override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var pending = await _asyncTaskService.GetPageAsync(new PagedQueryCriteria<AsyncTaskQueryCriteria>()
            {
                Condition = new AsyncTaskQueryCriteria
                {
                    TaskStatus = AsyncTaskStatus.Pending,
                    TaskCode = "ExtractProjectTask"
                },
                PageParams = new PageRequestParams(1, 10)
            });

            await pending.Rows.ForEachAsync(async asyncTask =>
            {
                var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);

                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var asyncTaskService = scope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
                    var projectStatisticsBatchItemRepository =
                        scope.ServiceProvider.GetRequiredService<IProjectStatisticsBatchItemRepository>();
                    var projectStatisticsBatchRepository =
                        scope.ServiceProvider.GetRequiredService<IProjectStatisticsBatchRepository>();
                    var rptProjectServiceFactory = scope.ServiceProvider.GetRequiredService<RptProjectServiceFactory>();
                    var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                    _contextContainer.Init();

                    var dict = JsonSerializer.Deserialize<Dictionary<string, object?>>(asyncTask.TaskData)!;

                    var batchId = Convert.ToInt64(dict["BatchId"]!.ToString());
                    var itemIds = JsonElementToObjectList<long>(dict["ItemIds"]) ?? new List<long>();

                    await unitOfWork.BeginTransactionAsync(120, IsolationLevel.ReadUncommitted, stoppingToken);

                    await TimeTaken.DurationAsync(async () =>
                        {
                            try
                            {
                                _logger.LoggingInformation("extract-project", "开始处理对象转换,BatchId:{BatchId}", batchId);

                                var batch = await projectStatisticsBatchRepository.GetAsync(batchId);

                                var rptProjectService = rptProjectServiceFactory.GetService(batch!.Category);

                                // var items = await TimeTaken.DurationAsync(async () =>
                                // {
                                var items = (await projectStatisticsBatchItemRepository.GetListAsync(q =>
                                        q.BatchId == batchId
                                        &&
                                        itemIds.Contains(q.Key)
                                    ))
                                    .ToList();

                                //     return items;
                                // }, elapsed => _logger.LoggingInformation("extract-project",
                                //     "获取统计数据完成,BatchId:{BatchId},Duration:{Duration}", batchId,
                                //     elapsed.TotalSeconds));

                                var keys = items.Select(q => new ProjectInfoKey(q.BusinessId, q.Version)).ToArray();

                                // var result = await TimeTaken.DurationAsync(async () =>
                                // {
                                var result = await rptProjectService.GetProjectInfosAsync(keys);
                                //     return result;
                                // }, elapsed => _logger.LoggingInformation("extract-project",
                                //     "获取项目数据完成,BatchId:{BatchId},Duration:{Duration}", batchId,
                                //     elapsed.TotalSeconds));

                                await items.ForEachAsync(async (idx, item) =>
                                {
                                    // await TimeTaken.DurationAsync(async () =>
                                    // {
                                    var key = new ProjectInfoKey(item.BusinessId, item.Version);
                                    if (result.TryGetValue(key, out var value))
                                    {
                                        await rptProjectService.ClearAsync(item.Key);
                                        await rptProjectService.InsertAsync(item.Key, value);

                                        item.Status = 3;
                                    }
                                    else
                                    {
                                        item.Status = 4;
                                    }
                                    //
                                    //     return true;
                                    // }, elapsed => _logger.LoggingInformation("extract-project",
                                    //     "处理项目数据完成,BatchId:{BatchId},Index:{Index},Duration:{Duration}", batchId,
                                    //     idx, elapsed.TotalSeconds));
                                });


                                // await TimeTaken.DurationAsync(async () =>
                                // {
                                await projectStatisticsBatchItemRepository.DetachAndUpdateAsync(items);
                                //     return true;
                                // }, elapsed => _logger.LoggingInformation("extract-project",
                                //     "更新统计数据完成,BatchId:{BatchId},Duration:{Duration}", batchId,
                                //     elapsed.TotalSeconds));

                                asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Completed;

                                await asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);

                                await unitOfWork.CommitAsync(stoppingToken);
                            }
                            catch (Exception ex)
                            {
                                _logger.LoggingException("extract-project", ex,
                                    "处理对象转换失败,BatchId:{BatchId}", batchId);

                                await unitOfWork.RollbackAsync(stoppingToken);

                                asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Failed;
                                asyncTaskOperationDto.TaskResult = ex.Message;
                            }

                            return true;
                        },
                        elapsed => _logger.LoggingInformation("extract-project",
                            "处理对象转换完成,BatchId:{BatchId},Duration:{Duration}", batchId,
                            elapsed.TotalSeconds)
                    );
                }

                // 在事务外更新任务状态
                if (asyncTaskOperationDto.TaskStatus == AsyncTaskStatus.Failed)
                {
                    using (var updateScope = _serviceScopeFactory.CreateScope())
                    {
                        var updateAsyncTaskService =
                            updateScope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
                        await updateAsyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
                    }
                }
            });

            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
}

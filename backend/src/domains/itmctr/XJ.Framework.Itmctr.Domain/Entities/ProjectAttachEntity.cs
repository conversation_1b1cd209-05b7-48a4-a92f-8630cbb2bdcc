using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Itmctr.Domain.Entities
{
    /// <summary>
    /// 项目基本信息附件
    /// </summary>
    [Table("project_attach", Schema = "i")]
    [SoftDeleteIndex("IX_ProjectAttach_FileName", nameof(FileName))]
    [SoftDeleteIndex("IX_ProjectAttach_FileTypeCode", nameof(FileTypeCode))]
    [SoftDeleteIndex("IX_ProjectAttach_ProjectId", nameof(ProjectId))]
    [SoftDeleteIndex("IX_ProjectAttach_BusinessId", nameof(BusinessId))]
    [SoftDeleteIndex("IX_ProjectAttach_Version", nameof(Version))]
    public class ProjectAttachEntity : BaseSoftDeleteEntity<Guid>
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column("project_id")]
        public required long ProjectId { get; set; }

        /// <summary>
        /// 关联业务id
        /// </summary>
        [Column("business_id")]
        public required string BusinessId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Column("version")]
        [StringLength(20)]
        public required string Version { get; set; }

        /// <summary>
        /// 文件id
        /// </summary>
        [Column("file_id")]
        public required Guid FileId { get; set; }

        /// <summary>
        /// 文件原始名称
        /// </summary>
        [Column("file_name")]
        [StringLength(510)]
        public required string FileName { get; set; } = null!;

        /// <summary>
        /// 文件总大小（字节）
        /// </summary>
        [Column("file_size")]
        public required long FileSize { get; set; }

        /// <summary>
        /// 文件类型code
        /// </summary>
        [Column("file_type_code")]
        public required string FileTypeCode { get; set; }
    }
}

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Itmctr.Domain.Entities
{
    /// <summary>
    /// 测量指标
    /// </summary>
    [Table("project_measurement", Schema = "rpt")]
    [SoftDeleteIndex("IX_ProjectMeasurement_ProjectId", nameof(ProjectId))]
    [SoftDeleteIndex("IX_ProjectMeasurement_BusinessId", nameof(BusinessId))]
    [SoftDeleteIndex("IX_ProjectMeasurement_Version", nameof(Version))]
    [SoftDeleteIndex("IX_ProjectMeasurement_RowIndex", nameof(RowIndex))]
    public class RptProjectMeasurementEntity : BaseSoftDeleteEntity<long>
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column("project_id")]
        public required long ProjectId { get; set; }

        /// <summary>
        /// 关联业务id
        /// </summary>
        [Column("business_id")]
        public required string BusinessId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Column("version")]
        [StringLength(20)]
        public required string Version { get; set; }

        /// <summary>
        /// 指标中文名（中文）
        /// </summary>
        [Column("outcome_name_Zh")]
        [StringLength(500)]
        public string? outcome_name_zh { get; set; }

        /// <summary>
        /// 指标中文名（英文）
        /// </summary>
        [Column("outcome_name_En")]
        [StringLength(500)]
        public string? outcome_name_en { get; set; }

        /// <summary>
        /// 指标类型
        /// </summary>
        [Column("outcome_type")]
        [StringLength(500)]
        public string? outcome_type { get; set; }

        /// <summary>
        /// 测量时间点（中文）
        /// </summary>
        [Column("measure_time_point_Zh")]
        [StringLength(500)]
        public string? measure_time_point_zh { get; set; }

        /// <summary>
        /// 测量时间点（英文）
        /// </summary>
        [Column("measure_time_point_En")]
        [StringLength(500)]
        public string? measure_time_point_en { get; set; }

        /// <summary>
        /// 测量方法（中文）
        /// </summary>
        [Column("measure_method_Zh")]
        [StringLength(500)]
        public string? measure_method_zh { get; set; }

        /// <summary>
        /// 测量方法（英文）
        /// </summary>
        [Column("measure_method_En")]
        [StringLength(500)]
        public string? measure_method_en { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column("row_index")]
        public int? RowIndex { get; set; }
    }
}

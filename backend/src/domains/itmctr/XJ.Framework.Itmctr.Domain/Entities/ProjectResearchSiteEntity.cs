using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Itmctr.Domain.Entities
{
    /// <summary>
    /// 研究实施地点
    /// </summary>
    [Table("project_research_site", Schema = "i")]
    [SoftDeleteIndex("IX_ProjectResearchSite_ProjectId", nameof(ProjectId))]
    [SoftDeleteIndex("IX_ProjectResearchSite_BusinessId", nameof(BusinessId))]
    [SoftDeleteIndex("IX_ProjectResearchSite_Version", nameof(Version))]
    [SoftDeleteIndex("IX_ProjectResearchSite_RowIndex", nameof(RowIndex))]
    public class ProjectResearchSiteEntity : BaseSoftDeleteEntity<long>
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column("project_id")]
        public required long ProjectId { get; set; }

        /// <summary>
        /// 关联业务id
        /// </summary>
        [Column("business_id")]
        public required string BusinessId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Column("version")]
        [StringLength(20)]
        public required string Version { get; set; }

        /// <summary>
        /// 国家（中文）
        /// </summary>
        [Column("site_country_Zh")]
        [StringLength(500)]
        public string? site_country_zh { get; set; }

        /// <summary>
        /// 国家（英文）
        /// </summary>
        [Column("site_country_En")]
        [StringLength(500)]
        public string? site_country_en { get; set; }

        /// <summary>
        /// 省(直辖市)（中文）
        /// </summary>
        [Column("site_province_Zh")]
        [StringLength(500)]
        public string? site_province_zh { get; set; }

        /// <summary>
        /// 省(直辖市)（英文）
        /// </summary>
        [Column("site_province_En")]
        [StringLength(500)]
        public string? site_province_en { get; set; }

        /// <summary>
        /// 市(区县)（中文）
        /// </summary>
        [Column("site_city_Zh")]
        [StringLength(500)]
        public string? site_city_zh { get; set; }

        /// <summary>
        /// 市(区县)（英文）
        /// </summary>
        [Column("site_city_En")]
        [StringLength(500)]
        public string? site_city_en { get; set; }

        /// <summary>
        /// 单位(医院)（中文）
        /// </summary>
        [Column("site_institution_Zh")]
        [StringLength(500)]
        public string? site_institution_zh { get; set; }

        /// <summary>
        /// 单位(医院)（英文）
        /// </summary>
        [Column("site_institution_En")]
        [StringLength(500)]
        public string? site_institution_en { get; set; }

        /// <summary>
        /// 单位级别（中文）
        /// </summary>
        [Column("site_level_Zh")]
        [StringLength(500)]
        public string? site_level_zh { get; set; }

        /// <summary>
        /// 单位级别（英文）
        /// </summary>
        [Column("site_level_En")]
        [StringLength(500)]
        public string? site_level_en { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column("row_index")]
        public int? RowIndex { get; set; }
    }
}

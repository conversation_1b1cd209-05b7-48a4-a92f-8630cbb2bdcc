using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Itmctr.Domain.Entities
{
    /// <summary>
    /// 干预措施历史记录
    /// </summary>
    [Table("project_intervention_history", Schema = "i")]
    [SoftDeleteIndex("IX_ProjectInterventionHistory_ProjectId", nameof(ProjectId))]
    [SoftDeleteIndex("IX_ProjectInterventionHistory_BusinessId", nameof(BusinessId))]
    [SoftDeleteIndex("IX_ProjectInterventionHistory_Version", nameof(Version))]
    [SoftDeleteIndex("IX_ProjectInterventionHistory_RowIndex", nameof(RowIndex))]
    public class ProjectInterventionHistoryEntity : BaseSoftDeleteEntity<long>
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column("project_id")]
        public required long ProjectId { get; set; }

        /// <summary>
        /// 关联业务id
        /// </summary>
        [Column("business_id")]
        public required string BusinessId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Column("version")]
        [StringLength(20)]
        public required string Version { get; set; }

        /// <summary>
        /// 组别（中文）
        /// </summary>
        [Column("intervention_group_Zh")]
        [StringLength(500)]
        public string? intervention_group_zh { get; set; }

        /// <summary>
        /// 组别（英文）
        /// </summary>
        [Column("intervention_group_En")]
        [StringLength(500)]
        public string? intervention_group_en { get; set; }

        /// <summary>
        /// 样本量
        /// </summary>
        [Column("intervention_sample_size")]
        [StringLength(500)]
        public string? intervention_sample_size { get; set; }

        /// <summary>
        /// 干预措施（中文）
        /// </summary>
        [Column("intervention_name_Zh")]
        [StringLength(-1)]
        public string? intervention_name_zh { get; set; }

        /// <summary>
        /// 干预措施（英文）
        /// </summary>
        [Column("intervention_name_En")]
        [StringLength(-1)]
        public string? intervention_name_en { get; set; }

        /// <summary>
        /// 干预措施代码
        /// </summary>
        [Column("intervention_code")]
        [StringLength(500)]
        public string? intervention_code { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column("row_index")]
        public int? RowIndex { get; set; }
    }
}

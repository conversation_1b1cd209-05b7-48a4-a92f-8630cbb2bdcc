using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Itmctr.Domain.Entities
{
    /// <summary>
    /// 采集人体标本历史记录
    /// </summary>
    [Table("project_humansample_history", Schema = "i")]
    [SoftDeleteIndex("IX_ProjectHumanSampleHistory_ProjectId", nameof(ProjectId))]
    [SoftDeleteIndex("IX_ProjectHumanSampleHistory_BusinessId", nameof(BusinessId))]
    [SoftDeleteIndex("IX_ProjectHumanSampleHistory_Version", nameof(Version))]
    [SoftDeleteIndex("IX_ProjectHumanSampleHistory_RowIndex", nameof(RowIndex))]
    public class ProjectHumanSampleHistoryEntity : BaseSoftDeleteEntity<long>
    {
        /// <summary>
        /// 项目ID
        /// </summary>
        [Column("project_id")]
        public required long ProjectId { get; set; }

        /// <summary>
        /// 关联业务id
        /// </summary>
        [Column("business_id")]
        public required string BusinessId { get; set; }

        /// <summary>
        /// 版本
        /// </summary>
        [Column("version")]
        [StringLength(20)]
        public required string Version { get; set; }

        /// <summary>
        /// 标本中文名（中文）
        /// </summary>
        [Column("sample_name_Zh")]
        [StringLength(500)]
        public string? sample_name_zh { get; set; }

        /// <summary>
        /// 标本中文名（英文）
        /// </summary>
        [Column("sample_name_En")]
        [StringLength(500)]
        public string? sample_name_en { get; set; }

        /// <summary>
        /// 组织（中文）
        /// </summary>
        [Column("tissue_Zh")]
        [StringLength(500)]
        public string? tissue_zh { get; set; }

        /// <summary>
        /// 组织（英文）
        /// </summary>
        [Column("tissue_En")]
        [StringLength(500)]
        public string? tissue_en { get; set; }

        /// <summary>
        /// 人体标本去向
        /// </summary>
        [Column("fate_of_sample")]
        [StringLength(500)]
        public string? fate_of_sample { get; set; }

        /// <summary>
        /// 说明（中文）
        /// </summary>
        [Column("sample_note_Zh")]
        [StringLength(500)]
        public string? sample_note_zh { get; set; }

        /// <summary>
        /// 说明（英文）
        /// </summary>
        [Column("sample_note_En")]
        [StringLength(500)]
        public string? sample_note_en { get; set; }

        /// <summary>
        /// 排序
        /// </summary>
        [Column("row_index")]
        public int? RowIndex { get; set; }
    }
}

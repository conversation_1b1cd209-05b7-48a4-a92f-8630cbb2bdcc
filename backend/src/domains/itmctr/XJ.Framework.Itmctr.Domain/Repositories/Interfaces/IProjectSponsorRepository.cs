using System.Threading.Tasks;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Library.Domain.Repositories.Interfaces;

namespace XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

/// <summary>
/// 试验主办单位 仓储接口
/// </summary>
public interface IProjectSponsorRepository : IAuditRepository<long, ProjectSponsorEntity>
{


    Task<bool> DetachAndInsertAsync(List<ProjectSponsorEntity> entities);
    
    Task<bool> DetachAndDeleteAsync(List<ProjectSponsorEntity> entities);
}

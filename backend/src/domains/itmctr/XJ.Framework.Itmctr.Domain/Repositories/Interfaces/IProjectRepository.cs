using System.Linq.Expressions;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IProjectRepository : IAuditRepository<long, ProjectEntity>
{
    Task<PageData<long, ProjectEntity>> GetProjectPageAsync(
        Expression<Func<ProjectEntity, bool>> whereLambda, Dictionary<string, string?>? condition, int rowIndex,
        int pageSize,
        List<OrderbyDirection<ProjectEntity>> orderBy, bool isNoTracking = true);
    Task<PageData<long, ProjectEntity>> GetNewestProjectPageAsync(
        Expression<Func<ProjectEntity, bool>> whereLambda, Dictionary<string, string?>? condition, int rowIndex,
        int pageSize,
        List<OrderbyDirection<ProjectEntity>> orderBy, bool isNoTracking = true);


    Task<bool> DetachAndInsertAsync(ProjectEntity entity);

    Task<bool> DetachAndDeleteAsync(ProjectEntity entity);

    Task<IQueryable<ProjectEntity>> AppendFilterAsync(IQueryable<ProjectEntity> queryable,DateTimeOffset start, DateTimeOffset end);
    Task<IQueryable<ProjectEntity>> GetQueryableAsync();
    Task<IQueryable<ProjectEntity>> GetNewestQueryableAsync();
}

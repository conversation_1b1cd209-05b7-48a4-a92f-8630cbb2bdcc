using XJ.Framework.Itmctr.Domain.Entities;

namespace XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

/// <summary>
/// 干预措施 仓储接口
/// </summary>
public interface IProjectInterventionRepository : IAuditRepository<long, ProjectInterventionEntity>
{

    Task<bool> DetachAndInsertAsync(List<ProjectInterventionEntity> entities);
    
    Task<bool> DetachAndDeleteAsync(List<ProjectInterventionEntity> entities);
}

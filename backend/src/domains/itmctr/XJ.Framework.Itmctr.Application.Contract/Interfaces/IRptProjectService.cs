using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Itmctr.Application.Contract.Interfaces;

public interface IRptProjectService
{
    Task<Dictionary<ProjectInfoKey, ProjectInfoView>> GetProjectInfosAsync(
        params ProjectInfoKey[] keys);

    Task<bool> ClearAsync(long projectStatisticsBatchItemId);

    Task<bool> InsertAsync(long projectStatisticsBatchItemId, ProjectInfoView projectInfo);

    Task<List<ProjectInfoKey>> GetProjectKeysAsync(DateTimeOffset start, DateTimeOffset end);

    Task<(string fileName, byte[] bytes)> ExportAsync(long batchId);

    Task<PageData<long, RptProjectEntity>> GetPageAsync(
        PagedQueryCriteria<RptProjectQueryCriteria> criteria);
}

public interface IRptProjectService<T> : IRptProjectService
    where T : BaseEntity<long>
{
    Task<IQueryable<T>> AppendFilterAsync(IQueryable<T> queryable, DateTimeOffset start, DateTimeOffset end);
    Task<IQueryable<T>> GetQueryableAsync();
}

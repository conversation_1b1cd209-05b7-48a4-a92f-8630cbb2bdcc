using Microsoft.AspNetCore.Mvc;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.Interfaces;

public interface IProjectService
{
    Task<bool> SubmitToContentCompareAsync(long userId,string businessId,string version);
    
    Task<string?> FixFormRecognitionAsync(string businessId);
    
    Task<int> GetCountAsync(ProjectStatisticsEnum category);

    Task<FormInstancePageDto> GetPageAsync(ProjectStatisticsEnum category,
        PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria);

    Task<bool> SaveToResultDataAsync(string formCode, string businessId, string version, string language,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData);

    Task<FormDefinitionDto> GetFormDefinitionAsync();

    Task<string> SaveAsync(string businessId, FormDefinitionDto formDefinitionDto);

    Task<bool> MaintenanceSaveAsync(string businessId, string version, FormDefinitionDto formDefinitionDto);


    Task<FormDefinitionDto> GetAsync(string businessId, bool validateUser = true, bool filterFormDefinition = true);

    Task<FormDefinitionDto> GetAsync(string businessId, string version, bool validateUser = true,
        bool filterFormDefinition = true);

    Task<FormDefinitionDto> GetWithPreviousCompareAsync(string businessId, bool validateUser = true,
        bool filterFormDefinition = true);

    Task<bool> SubmitAsync(string businessId);
    Task<string> CreateAsync(string taskId);
    Task<string> CreateAsync(FormDefinitionDto formDefinitionDto);
    Task<bool> JudgeAsync(string businessId, JudgeProjectOperationDto input);
    Task<bool> JudgeRejectAsync(string businessId, JudgeRejectProjectOperationDto input);
    Task<bool> JudgeTerminationAsync(string businessId, JudgeTerminationProjectOperationDto input);
    Task<string> TranslateAsync(string businessId);
    Task<bool> AssignAsync(string businessId, AssignProjectOperationDto input);
    Task<bool> AssignReviewAsync(string businessId, AssignReviewProjectOperationDto input);

    Task<bool> ReturnLevel2Async(string businessId, AssignProjectReturnOperationDto input);
    Task<bool> RejectLevel4Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> SaveLevel4Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> ApprovalLevel4Async(string businessId, FormApprovalDto formApprovalDto);
    Task<bool> ApprovalLevel3Async(string businessId, FormApprovalDto formApprovalDto);
    Task<bool> RejectLevel3Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> RejectLevel2Async(string businessId, FormRejectDto formRejectDto);
    Task<bool> ApprovalLevel2Async(string businessId, FormApprovalDto formApprovalDto);
    Task<bool> RejectLevel1Async(string businessId, SendNumberDto input);
    Task<bool> ApprovalLevel1Async(string businessId, SendNumberDto input);

    Task<bool> InsertProjectAndHistoryAsync(string formCode, string businessId, string version);

    Task<ProjectPageDto> GetProjectPageAsync([FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria);

    Task<ProjectPageDto> GetNewestProjectPageAsync([FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria);

    Task<Dictionary<string, List<OptionDto>?>?> GetSelectOptionAsync();
    Task<ProjectsDto?> GetProjectInfoAsync(long key);

    Task<bool> EditApplyAsync(string businessId, EditApplyProjectOperationDto input);
    Task<bool> EditConfirmedAsync(string businessId);
    Task<bool> EditRejectedAsync(string businessId, EditProjectRejectOperationDto input);

    Task<bool> RecallAsync(string businessId);
    
    Task<List<DashboardStatisticsDto>> GetDashboardAsync();
    
    Task<long?> GetProjectHistoryNewestPidAsync(string businessId);
    Task<int> GetSummarizedDataAsync(long userId, string statisticsCode, string positionCode);
}

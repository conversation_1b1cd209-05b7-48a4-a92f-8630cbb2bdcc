using Microsoft.AspNetCore.Mvc;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.Interfaces;

public interface IProjectHistoryService
{

    Task<ProjectsDto?> GetProjectHistoryInfoAsync(long key);
}

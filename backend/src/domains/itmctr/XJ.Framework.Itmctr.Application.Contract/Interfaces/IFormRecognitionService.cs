using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.Interfaces;

public interface IFormRecognitionService
{
    /// <summary>
    /// 对附件进行预检查
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="formRecognitionContext"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task<PreCheckResult> PreCheckAsync(long userId,FormRecognitionContext formRecognitionContext,
        CancellationToken cancellationToken = default);


    /// <summary>
    /// 启动提取表单服务
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    Task<bool> ExtractAsync(string taskId);

    /// <summary>
    /// 填充表单定义
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="language"></param>
    /// <param name="taskCode"></param>
    /// <param name="annotationKey"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    Task FillFormAsync(string taskId, FillFormLanguage language, string taskCode, string annotationKey,
        FormDefinitionDto formDefinitionDto);

    /// <summary>
    /// 查询提取表单服务的进度
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    Task<AttachmentParseDto?> QueryAttachmentParseCompletedProgressAsync(string taskId);

    /// <summary>
    /// 查询内容对比服务的进度
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    Task<ContentCompareDto?> QueryContentCompareCompletedProgressAsync(string taskId);

    /// <summary>
    /// 查询全文翻译服务的进度
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    Task<ContentTranslateDto?> QueryContentTranslateCompletedProgressAsync(string taskId);

    /// <summary>
    /// 启动全文翻译服务
    /// </summary>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    Task<string> ContentTranslateAsync(FormDefinitionDto formDefinitionDto);

    /// <summary>
    /// 启动内容对比服务
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="userId"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    Task<string> ContentCompareAsync(long userId, string taskId, FormDefinitionDto formDefinitionDto);

    /// <summary>
    /// 进行实时翻译
    /// </summary>
    /// <param name="content"></param>
    /// <returns></returns>
    Task<string> RealtimeContentTranslateAsync(string content);

    /// <summary>
    /// 获取表单对比结果
    /// </summary>
    /// <param name="formId"></param>
    /// <param name="taskId"></param>
    /// <returns></returns>
    Task<FormCompareDto> GetFormCompareAsync(string formId, string taskId);


    /// <summary>
    /// 完成提取表单服务
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CompletedExtractAsync(string taskId, AttachmentParseDto input);

    /// <summary>
    /// 完成内容对比服务
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CompletedContentCompareAsync(string taskId, ContentCompareDto input);

    /// <summary>
    /// 完成全文翻译服务
    /// </summary>
    /// <param name="requestId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    Task CompletedContentTranslateAsync(string requestId, ContentTranslateDto input);
}

namespace XJ.Framework.Itmctr.Application.Contract.OperationDtos;

/// <summary>
/// 项目附件 操作 DTO
/// </summary>
public class ProjectAttachOperationDto : BaseOperationDto
{
    /// <summary>
    /// 项目ID
    /// </summary>
    public long ProjectId { get; set; }

    /// <summary>
    /// 关联业务id
    /// </summary>
    public string BusinessId { get; set; } = null!;

    /// <summary>
    /// 版本
    /// </summary>
    public string Version { get; set; } = null!;

    /// <summary>
    /// 文件原始名称
    /// </summary>
    public string FileName { get; set; } = null!;

    /// <summary>
    /// 文件总大小（字节）
    /// </summary>
    public long FileSize { get; set; }

    /// <summary>
    /// 文件类型code
    /// </summary>
    public string FileTypeCode { get; set; } = null!;
} 
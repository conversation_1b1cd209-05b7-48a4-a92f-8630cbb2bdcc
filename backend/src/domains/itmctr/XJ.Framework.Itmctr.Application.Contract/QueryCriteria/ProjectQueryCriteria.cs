using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;

namespace XJ.Framework.Itmctr.Application.Contract.QueryCriteria;

/// <summary>
/// 项目 查询条件
/// </summary>
public class ProjectQueryCriteria : BaseQueryCriteria
{
    [Equal("BusinessId")] public string? BusinessId { get; set; }
    public Dictionary<string, string> DynamicQueries { get; set; } = new();
}

namespace XJ.Framework.Itmctr.Application.Contract.QueryCriteria;

/// <summary>
/// ��Ŀ���� ��ѯ����
/// </summary>
public class ProjectAttachQueryCriteria : BaseQueryCriteria
{
    /// <summary>
    /// ��ĿID
    /// </summary>
    [Equal]
    public long? ProjectId { get; set; }

    /// <summary>
    /// ����ҵ��id
    /// </summary>
    [Equal]
    public string? BusinessId { get; set; }

    /// <summary>
    /// �汾
    /// </summary>
    [Equal]
    public string? Version { get; set; }

    /// <summary>
    /// �ļ�ԭʼ����
    /// </summary>
    [Contains]
    public string? FileName { get; set; }

    /// <summary>
    /// �ļ�����code
    /// </summary>
    [Equal]
    public string? FileTypeCode { get; set; }
}

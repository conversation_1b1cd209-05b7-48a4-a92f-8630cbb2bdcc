namespace XJ.Framework.Itmctr.Application.Services;

public static class FieldMapToFileSource
{
    public readonly static Dictionary<string, List<string>> FieldMap = new()
    {
        {
            "ethic_committee_approved_file", [
                "ethic_committee_approved",
                "ethic_committee_approved_no",
                "ethic_committee_name",
                "ethic_committee_approved_date",
                "ethic_committee_contact",
                "ethic_committee_address",
                "ethic_committee_phone",
                "ethic_committee_email"
            ]
        },
        { "informed_consent_file", [] },
        {
            "study_protocol", [
                "registration_status",
                "public_title",
                "english_acronym",
                "scientific_title",
                "scientific_title_acronym",
                "study_subject_id",
                "partner_registry_number",
                "applicant",
                "study_leader",
                "applicant_telephone",
                "study_leader_telephone",
                "applicant_fax",
                "study_leader_fax",
                "applicant_email",
                "study_leader_email",
                "applicant_website",
                "study_leader_website",
                "applicant_address",
                "study_leader_address",
                "applicant_postcode",
                "study_leader_postcode",
                "applicant_affiliation",
                "study_leader_affiliation",
                "mpa_approved_no",
                "mpa_approved_date",
                "primary_sponsor",
                "primary_sponsor_address",
                "trial_sponsor_area",
                "funding_source",
                "target_disease",
                "target_disease_code",
                "study_type",
                "study_design",
                "study_phase",
                "study_objectives",
                "treatment_description",
                "inclusion_criteria",
                "exclusion_criteria",
                "study_execute_time",
                "recruiting_time",
                "gold_standard",
                "index_test",
                "target_condition",
                "target_sample_size",
                "confounding_condition",
                "confounding_sample_size",
                "research_site_area",
                "intervention_area",
                "intervention_total_sample_size",
                "measurement_index_area",
                "human_sample_collection_area",
                "recruiting_status",
                "age_range",
                "gender",
                "randomization_procedure",
                "sign_informed_consent",
                "follow_up_length",
                "allocation_concealment",
                "blinding",
                "unblinding_rules",
                "statistical_methods",
                "calculated_results",
                "calculated_results_public",
                "utn",
                "ipd_sharing",
                "ipd_sharing_way",
                "data_collection",
                "safety_committee",
                "publication_info"
            ]
        }
    };
}
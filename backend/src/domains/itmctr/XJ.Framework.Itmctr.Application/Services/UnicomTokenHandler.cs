using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Net;
using System.Net.Http.Headers;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Itmctr.Application.Services;

public class UnicomTokenHandler : DelegatingHandler
{
    private readonly ITokenProvider _tokenProvider;
    private readonly ILogger<UnicomTokenHandler> _logger;

    public UnicomTokenHandler(ITokenProvider tokenProvider, ILogger<UnicomTokenHandler> logger)
    {
        _tokenProvider = tokenProvider;
        _logger = logger;
    }

    protected async override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        _logger.LoggingDebug("unicom", "Sending request to {Url}", request.RequestUri!);
        // 获取有效token
        var token = await _tokenProvider.GetTokenAsync();

        if (string.IsNullOrEmpty(token))
        {
            _logger.LoggingWarning("unicom", "Token is null or empty, request will not include Authorization header.");
        }

        // 加入Authorization头
        request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

        if (request.Content is not null && request.Content is not MultipartFormDataContent)
        {
            _logger.LoggingInformation("unicom", "Requesting To {Url} with JSON data:{Data}",
                request.RequestUri!, await request.Content?.ReadAsStringAsync(cancellationToken)!);
        }

        HttpResponseMessage? result;
        try
        {
            // 继续请求
            result = await base.SendAsync(request, cancellationToken);

            _logger.LoggingInformation("unicom",
                "Received response from {Url} with status code {StatusCode}  data:{Data}",
                request.RequestUri!, result.StatusCode, await result.Content.ReadAsStringAsync(cancellationToken));

            result.EnsureSuccessStatusCode();
        }
        catch (HttpRequestException exception)
        {
            if (exception.StatusCode?.Equals(HttpStatusCode.Unauthorized) ?? false)
            {
                _logger.LoggingWarning("unicom", "Token is unauthorized,request will retry.");

                // 重新获取token
                token = await _tokenProvider.GetTokenAsync(true);

                // 重新设置Authorization头
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);

                // 重新请求
                result = await base.SendAsync(request, cancellationToken);

                _logger.LoggingInformation("unicom",
                    "Received retry response from {Url} with status code {StatusCode}  data:{Data}",
                    request.RequestUri!, result.StatusCode, await result.Content.ReadAsStringAsync(cancellationToken));
            }
            else
            {
                result = new HttpResponseMessage(exception.StatusCode ?? HttpStatusCode.InternalServerError);
            }
        }

        return result;
    }
}

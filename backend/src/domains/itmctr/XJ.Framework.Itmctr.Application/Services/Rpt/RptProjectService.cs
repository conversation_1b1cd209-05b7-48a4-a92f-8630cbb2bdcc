using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.EntityFrameworkCore.Extensions;

namespace XJ.Framework.Itmctr.Application.Services;

public abstract class RptProjectService<T, TCondition>
    where T : BaseEntity<long>, IHasProjectKey
    where TCondition : RptProjectQueryCriteria
{
    private readonly IRptProjectRepository _rptProjectRepository;
    private readonly IRptProjectMeasurementRepository _rptProjectMeasurementRepository;
    private readonly IRptProjectResearchSiteRepository _rptProjectResearchSiteRepository;
    private readonly IRptProjectSponsorRepository _rptProjectSponsorRepository;
    private readonly IRptProjectInterventionRepository _rptProjectInterventionRepository;
    private readonly IRptProjectHumanSampleRepository _rptProjectHumanSampleRepository;
    private readonly IRptProjectAttachRepository _rptProjectAttachRepository;
    private readonly IMapper _mapper;
    private readonly IKeyGenerator<long> _keyGenerator;
    private readonly IKeyGenerator<Guid> _guidKeyGenerator;
    private readonly IProjectStatisticsBatchItemRepository _projectStatisticsBatchItemRepository;

    protected RptProjectService(IServiceProvider serviceProvider)
    {
        _rptProjectRepository = serviceProvider.GetRequiredService<IRptProjectRepository>();
        _rptProjectMeasurementRepository = serviceProvider.GetRequiredService<IRptProjectMeasurementRepository>();
        _rptProjectResearchSiteRepository = serviceProvider.GetRequiredService<IRptProjectResearchSiteRepository>();
        _rptProjectSponsorRepository = serviceProvider.GetRequiredService<IRptProjectSponsorRepository>();
        _rptProjectInterventionRepository = serviceProvider.GetRequiredService<IRptProjectInterventionRepository>();
        _rptProjectHumanSampleRepository = serviceProvider.GetRequiredService<IRptProjectHumanSampleRepository>();
        _rptProjectAttachRepository = serviceProvider.GetRequiredService<IRptProjectAttachRepository>();
        _mapper = serviceProvider.GetRequiredService<IMapper>();
        _keyGenerator = serviceProvider.GetRequiredService<IKeyGenerator<long>>();
        _guidKeyGenerator = serviceProvider.GetRequiredService<IKeyGenerator<Guid>>();
        _projectStatisticsBatchItemRepository =
            serviceProvider.GetRequiredService<IProjectStatisticsBatchItemRepository>();
    }


    public abstract Task<IOrderedEnumerable<ProjectInfoView>>
        OrderProjectInfosAsync(List<ProjectInfoView> projectInfos);

    public abstract Task<Dictionary<ProjectInfoKey, ProjectInfoView>> GetProjectInfosAsync(
        params ProjectInfoKey[] keys);

    public abstract Task<IQueryable<T>> GetQueryableAsync();

    public abstract Task<IQueryable<T>> AppendFilterAsync(IQueryable<T> queryable, DateTimeOffset start,
        DateTimeOffset end);

    public async Task<List<ProjectInfoKey>> GetProjectKeysAsync(DateTimeOffset start, DateTimeOffset end)
    {
        return (await AppendFilterAsync(await GetQueryableAsync(), start, end))
            .Select(q => new ProjectInfoKey(q.GetBusinessId(), q.GetVersion()))
            .ToList();
    }

    public async Task<PageData<long, RptProjectEntity>> GetPageAsync(
        PagedQueryCriteria<RptProjectQueryCriteria> criteria)
    {
        var tempCriteria = new PagedQueryCriteria<TCondition>()
        {
            Condition = _mapper.Map<TCondition>(criteria.Condition),
            OrderBy = criteria.OrderBy,
            PageParams = criteria.PageParams
        };

        var whereExpr =
            tempCriteria.Condition
                .BuildExpression<long, T, TCondition>();

        var orderbyExpr = tempCriteria
            .BuildOrderExpression<long, T, TCondition>();

        return await GetPageAsync(
            whereExpr,
            criteria.PageParams.ToRowIndex(),
            criteria.PageParams.PageSize,
            orderbyExpr
        );
    }

    public async Task<PageData<long, RptProjectEntity>> GetPageAsync(
        Expression<Func<T, bool>> whereLambda,
        int rowIndex, int pageSize,
        List<OrderbyDirection<T>> orderBy,
        bool isNoTracking = true)
    {
        var queryable = await GetQueryableAsync();

        var data = isNoTracking ? queryable.Where(whereLambda).AsNoTracking() : queryable.Where(whereLambda);

        data = data.Orderby<long, T>(orderBy);

        var rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync();

        var pageData = new PageData<long, RptProjectEntity>
        {
            Totals = await data.CountAsync(),
            Rows = _mapper.Map<List<RptProjectEntity>>(rows)
        };
        return pageData;
    }

    public async Task<List<ProjectInfoView>> GetRptProjectInfosAsync(
        long batchId)
    {
        var items = await _projectStatisticsBatchItemRepository.GetListAsync(q => q.BatchId == batchId);

        var itemIds = items.Select(q => q.Key).ToList();

        var projects = (await _rptProjectRepository.GetListAsync(q => itemIds.Contains(q.ProjectStatisticsBatchItemId)))
            .ToList();

        var projectIds = projects.Select(p => p.Key).ToList();

        var projectAttaches = (await _rptProjectAttachRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectHumanSamples = (await _rptProjectHumanSampleRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectInterventions = (await _rptProjectInterventionRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectMeasurements = (await _rptProjectMeasurementRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectResearchSites = (await _rptProjectResearchSiteRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();

        var projectSponsors = (await _rptProjectSponsorRepository.GetListAsync(q =>
            projectIds.Contains(q.ProjectId)
        )).ToList();


        return projects.Select(project =>
        {
            return new ProjectInfoView(
                _mapper.Map<RptProjectEntity>(project),
                projectAttaches.Where(p => p.ProjectId == project.Key)
                    .ToList(),
                projectHumanSamples
                    .Where(p => p.ProjectId == project.Key).OrderBy(q => q.RowIndex).ToList(),
                projectInterventions
                    .Where(p => p.ProjectId == project.Key).OrderBy(q => q.RowIndex).ToList(),
                projectMeasurements
                    .Where(p => p.ProjectId == project.Key).OrderBy(q => q.RowIndex).ToList(),
                projectResearchSites
                    .Where(p => p.ProjectId == project.Key).OrderBy(q => q.RowIndex).ToList(),
                projectSponsors.Where(p => p.ProjectId == project.Key)
                    .ToList()
            );
        }).ToList();
    }

    public async Task<bool> ClearAsync(long projectStatisticsBatchItemId)
    {
        var project =
            await _rptProjectRepository.GetAsync(q => q.ProjectStatisticsBatchItemId == projectStatisticsBatchItemId);

        if (project == null)
            return true;

        var interventions =
            (await _rptProjectInterventionRepository.GetListAsync(q => q.ProjectId == project.Key)).ToList();

        var measurements =
            (await _rptProjectMeasurementRepository.GetListAsync(q => q.ProjectId == project.Key)).ToList();

        var researchSites =
            (await _rptProjectResearchSiteRepository.GetListAsync(q => q.ProjectId == project.Key)).ToList();

        var sponsors =
            (await _rptProjectSponsorRepository.GetListAsync(q => q.ProjectId == project.Key)).ToList();

        var humanSamples =
            (await _rptProjectHumanSampleRepository.GetListAsync(q => q.ProjectId == project.Key)).ToList();

        var attaches =
            (await _rptProjectAttachRepository.GetListAsync(q => q.ProjectId == project.Key)).ToList();


        await _rptProjectRepository.DeleteAsync(project);
        await _rptProjectInterventionRepository.DeleteAsync(interventions);
        await _rptProjectMeasurementRepository.DeleteAsync(measurements);
        await _rptProjectResearchSiteRepository.DeleteAsync(researchSites);
        await _rptProjectSponsorRepository.DeleteAsync(sponsors);
        await _rptProjectHumanSampleRepository.DeleteAsync(humanSamples);
        await _rptProjectAttachRepository.DeleteAsync(attaches);

        return true;
    }

    public async Task<bool> InsertAsync(long projectStatisticsBatchItemId, ProjectInfoView projectInfo)
    {
        var projectId = _keyGenerator.GenerateKey();

        projectInfo.Project.Key = projectId;
        projectInfo.Project.ProjectStatisticsBatchItemId = projectStatisticsBatchItemId;
        await _rptProjectRepository.InsertAsync(projectInfo.Project);

        projectInfo.Interventions.ForEach(item =>
        {
            item.ProjectId = projectId;
            item.Key = _keyGenerator.GenerateKey();
        });
        await _rptProjectInterventionRepository.InsertAsync(projectInfo.Interventions);

        projectInfo.Measurements.ForEach(item =>
        {
            item.ProjectId = projectId;
            item.Key = _keyGenerator.GenerateKey();
        });
        await _rptProjectMeasurementRepository.InsertAsync(projectInfo.Measurements);

        projectInfo.ResearchSites.ForEach(item =>
        {
            item.ProjectId = projectId;
            item.Key = _keyGenerator.GenerateKey();
        });
        await _rptProjectResearchSiteRepository.InsertAsync(projectInfo.ResearchSites);

        projectInfo.Sponsors.ForEach(item =>
        {
            item.ProjectId = projectId;
            item.Key = _keyGenerator.GenerateKey();
        });
        await _rptProjectSponsorRepository.InsertAsync(projectInfo.Sponsors);

        projectInfo.HumanSamples.ForEach(item =>
        {
            item.ProjectId = projectId;
            item.Key = _keyGenerator.GenerateKey();
        });
        await _rptProjectHumanSampleRepository.InsertAsync(projectInfo.HumanSamples);

        projectInfo.Attaches.ForEach(item =>
        {
            item.ProjectId = projectId;
            item.Key = _guidKeyGenerator.GenerateKey();
        });
        await _rptProjectAttachRepository.InsertAsync(projectInfo.Attaches);

        return true;
    }
}

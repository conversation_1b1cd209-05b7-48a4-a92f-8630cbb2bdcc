using System.Web;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Extensions;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ExportXmlService
{
    private static string HtmlDecode(string str)
    {
        return HttpUtility.HtmlDecode(str);
    }

    private static string RestoreSql(string sql)
    {
        var text = sql;
        if (string.IsNullOrEmpty(text))
        {
            return text;
        }

        if (text.Length > 20)
        {
            text = text.Replace("&#115;cript", "script");
            text = text.Replace("ma&#115;ter", "master");
            text = text.Replace("&#32;", " ");
            text = text.Replace("ins&#101;&#114;t", "insert");
            text = text.Replace("de&#108;&#101;te", "delete");
            text = text.Replace("u&#112;&#100;ate", "update");
            text = text.Replace("&#101;xec", "exec");
            text = text.Replace("&#39;", "'");
        }

        return text;
    }

    public async Task<TrialInfo> ConvertAsync(
        ProjectInfoView projectInfo,
        Dictionary<string, List<OptionDto>?> options
    )
    {
        var triallcs = new TrialInfo();
        triallcs.Main.TrialId = projectInfo.Project.registration_number!.CleanInvalidCharsForXml().ConvertToEn();
        triallcs.Main.Utrn = projectInfo.Project.utn?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        if (!string.IsNullOrEmpty(projectInfo.Project.partner_registry_number))
            triallcs.SecondaryIds.SecondaryId.Add(new SecondaryIdStr()
                { SecId = projectInfo.Project.partner_registry_number.CleanInvalidCharsForXml().ConvertToEn() });
        if (!string.IsNullOrEmpty(projectInfo.Project.chi_reg_number))
            triallcs.SecondaryIds.SecondaryId.Add(new SecondaryIdStr()
                { SecId = projectInfo.Project.chi_reg_number.CleanInvalidCharsForXml().ConvertToEn() });
        triallcs.Main.RegName = "ITMCTR";
        if (projectInfo.Project.send_number_time.HasValue)
            triallcs.Main.DateRegistration =
                projectInfo.Project.send_number_time.Value.ToLocalTime().ToString("yyyy-MM-dd");
        triallcs.Main.PrimarySponsor =
            projectInfo.Project.primary_sponsor_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        triallcs.Main.PublicTitle = projectInfo.Project.publictitle_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        triallcs.Main.ScientificTitle =
            projectInfo.Project.scientific_title_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        triallcs.Main.Acronym = projectInfo.Project.english_acronym_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        triallcs.Main.ScientificAcronym =
            projectInfo.Project.scientific_title_acronym_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        if (projectInfo.Project.recruiting_time_start.HasValue)
            triallcs.Main.DateEnrolment = projectInfo.Project.recruiting_time_start.Value.ToString("yyyy-MM-dd");

        var txtsize = "";
        var txtcode = "";
        var txtfreetext = "";


        if (projectInfo.Project.study_type == "1001003")
        {
            txtsize =
                $"Target condition:{projectInfo.Project.target_sample_size};Difficult condition:{projectInfo.Project.confounding_sample_size}";
            txtcode = "";
            txtfreetext =
                $"Gold Standard:{RestoreSql(projectInfo.Project.gold_standard_en ?? "")};Index test:{projectInfo.Project.index_test_en};";
        }
        else
        {
            foreach (var info in projectInfo.Interventions)
            {
                var parsed = int.TryParse(info.intervention_sample_size, out var num4);
                var num3 = parsed ? num4 : 0;
                txtsize += $"{HtmlDecode(info.intervention_group_en ?? "")}:{num3};";
                if (!string.IsNullOrEmpty(info.intervention_code))
                {
                    txtcode = txtcode + info.intervention_code + ";";
                }

                txtfreetext += string.Format("{0}:{1};", HtmlDecode(info.intervention_group_en ?? ""),
                    HtmlDecode(info.intervention_name_en ?? ""));
            }
        }

        triallcs.InterventionCode.ICode = txtcode.CleanInvalidCharsForXml().ConvertToEn();
        triallcs.Main.TargetSize = txtsize.CleanInvalidCharsForXml().ConvertToEn();
        triallcs.Main.IFreetext = txtfreetext.CleanInvalidCharsForXml().ConvertToEn();
        var recruitingStatus = projectInfo.Project.recruiting_status?.Trim() ?? "";
        triallcs.Main.RecruitmentStatus = "Pending";
        if (recruitingStatus == "1004001")
        {
            triallcs.Main.RecruitmentStatus = "Pending";
        }

        if (recruitingStatus == "1004002")
        {
            triallcs.Main.RecruitmentStatus = "Recruiting";
        }

        if (recruitingStatus == "1004003")
        {
            triallcs.Main.RecruitmentStatus = "Temporary halt";
        }

        if (recruitingStatus == "1004004")
        {
            triallcs.Main.RecruitmentStatus = "Completed";
        }

        triallcs.Main.Url =
            $"http://itmctr.ccebtcm.org.cn/en-US/Home/ProjectView?pid={projectInfo.Project.BusinessId.ToLower()}";

        triallcs.Main.StudyType =
            options["study_type"]?.FirstOrDefault(x => x.Value == projectInfo.Project.study_type)?.LabelEn ?? "";

        triallcs.Main.StudyType = triallcs.Main.StudyType.CleanInvalidCharsForXml().ConvertToEn();

        triallcs.Main.StudyDesign =
            options["study_design"]?.FirstOrDefault(x => x.Value == projectInfo.Project.study_design)?.LabelEn ?? "";

        triallcs.Main.StudyDesign = triallcs.Main.StudyDesign.CleanInvalidCharsForXml().ConvertToEn();

        //1003005 随机平行对照 Parallel
        //10030010 随机交叉对照 Cross-over
        switch (projectInfo.Project.study_design)
        {
            case "1003005":
                triallcs.Main.StudyDesign = "Parallel".CleanInvalidCharsForXml().ConvertToEn();
                break;
            case "10030010":
                triallcs.Main.StudyDesign = "Cross-over".CleanInvalidCharsForXml().ConvertToEn();
                break;
        }

        triallcs.Main.Phase =
            options["study_phase"]?.FirstOrDefault(x => x.Value == projectInfo.Project.study_phase)?.LabelEn ?? "";

        triallcs.Main.Phase = triallcs.Main.Phase.CleanInvalidCharsForXml().ConvertToEn();


        triallcs.Main.HcFreetext =
            HtmlDecode(projectInfo.Project.target_disease_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");

        var array3 = new ContactStr[2];
        var contactStr = new ContactStr();
        contactStr.Type = "Scientific";
        contactStr.Firstname =
            HtmlDecode(projectInfo.Project.applicant_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr.Address =
            HtmlDecode(projectInfo.Project.applicant_address_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr.Affiliation =
            HtmlDecode(projectInfo.Project.applicant_affiliation_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr.Zip =
            HtmlDecode(projectInfo.Project.applicant_postcode?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr.Telephone =
            HtmlDecode(projectInfo.Project.applicant_telephone?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr.Email =
            HtmlDecode(projectInfo.Project.applicant_email?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        array3.SetValue(contactStr, 0);

        var contactStr2 = new ContactStr();
        contactStr2.Type = "Public";
        contactStr2.Firstname =
            HtmlDecode(projectInfo.Project.study_leader_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr2.Address =
            HtmlDecode(projectInfo.Project.study_leader_address_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr2.Zip =
            HtmlDecode(projectInfo.Project.study_leader_postcode?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr2.Telephone =
            HtmlDecode(projectInfo.Project.study_leader_telephone?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr2.Email =
            HtmlDecode(projectInfo.Project.study_leader_email?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        contactStr2.Affiliation =
            HtmlDecode(projectInfo.Project.study_leader_affiliation_en?.CleanInvalidCharsForXml().ConvertToEn() ??
                       "");
        array3.SetValue(contactStr2, 1);

        triallcs.Contacts.Contact = array3;

        triallcs.HealthConditionCode.HcCode =
            projectInfo.Project.target_disease_code?.CleanInvalidCharsForXml().ConvertToEn() ?? "";

        foreach (var info in projectInfo.ResearchSites)
        {
            triallcs.Countries.Country2 =
                HtmlDecode(info.site_country_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        }

        triallcs.Criteria.InclusionCriteria =
            HtmlDecode(projectInfo.Project.inclusion_criteria_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        triallcs.Criteria.Agemin = projectInfo.Project.age_range_min?.CleanInvalidCharsForXml().ConvertToEn() ?? "";
        triallcs.Criteria.Agemax = projectInfo.Project.age_range_max?.CleanInvalidCharsForXml().ConvertToEn() ?? "";

        var txtsex = projectInfo.Project.gender?.Trim() ?? "";
        if (txtsex == "1006001")
        {
            triallcs.Criteria.Gender = "Male";
        }

        if (txtsex == "1006002")
        {
            triallcs.Criteria.Gender = "Female";
        }

        if (txtsex == "1006003")
        {
            triallcs.Criteria.Gender = "Both";
        }

        triallcs.Criteria.ExclusionCriteria =
            HtmlDecode(projectInfo.Project.exclusion_criteria_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        var txtPrim = "";
        var txtSec = "";


        foreach (var info in projectInfo.Measurements)
        {
            if (info.outcome_type == "4002001")
            {
                txtPrim += $"{HtmlDecode(info.outcome_name_en ?? "")};";
            }

            if (info.outcome_type == "4002002")
            {
                txtSec += $"{HtmlDecode(info.outcome_name_en ?? "")};";
            }
        }

        triallcs.PrimaryOutcome.PrimOutcome = txtPrim.CleanInvalidCharsForXml().ConvertToEn();
        triallcs.SecondaryOutcome.SecOutcome = txtSec.CleanInvalidCharsForXml().ConvertToEn();
        triallcs.SourceSupport.SourceName =
            HtmlDecode(projectInfo.Project.funding_source_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");


        if (projectInfo.Project.ethic_committee_approved == "1")
        {
            triallcs.EthicsReviews.EthicsReview.Status = "Approved";
        }
        else if (projectInfo.Project.ethic_committee_approved == "0")
        {
            triallcs.EthicsReviews.EthicsReview.Status = "Not approved";
        }
        else
        {
            triallcs.EthicsReviews.EthicsReview.Status = "NA";
        }

        if (projectInfo.Project.ethic_committee_approved_date.HasValue)
            triallcs.EthicsReviews.EthicsReview.ApprovalDate =
                projectInfo.Project.ethic_committee_approved_date.Value.ToString("yyyy-MM-dd");
        triallcs.EthicsReviews.EthicsReview.ContactName =
            HtmlDecode(projectInfo.Project.ethic_committee_contact_en?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        triallcs.EthicsReviews.EthicsReview.ContactAddress =
            HtmlDecode(projectInfo.Project.ethic_committee_address_en?.CleanInvalidCharsForXml().ConvertToEn() ??
                       "");
        triallcs.EthicsReviews.EthicsReview.ContactPhone =
            HtmlDecode(projectInfo.Project.ethic_committee_phone?.CleanInvalidCharsForXml().ConvertToEn() ?? "");
        triallcs.EthicsReviews.EthicsReview.ContactEmail =
            HtmlDecode(projectInfo.Project.ethic_committee_email?.CleanInvalidCharsForXml().ConvertToEn() ?? "");

        return await Task.FromResult(triallcs);
    }
}

using XJ.Framework.Files.ApiClient;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Services;

public sealed class ProjectStatisticsService
    : BaseEditableAppService<long, ProjectStatisticsBatchEntity, ProjectStatisticsBatchDto,
            ProjectStatisticsBatchOperationDto,
            IProjectStatisticsBatchRepository,
            ProjectStatisticsQueryCriteria>,
        IProjectStatisticsService
{
    private readonly IProjectStatisticsBatchItemRepository _projectStatisticsBatchItemRepository;
    private readonly IProjectStatisticsBatchItemViewRepository _projectStatisticsBatchItemViewRepository;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IProjectStatisticsBatchRepository _projectStatisticsBatchRepository;
    private readonly IKeyGenerator<long> _keyGenerator;
    private readonly ICurrentUserContext _currentUserContext;

    private readonly RptProjectServiceFactory _rptProjectServiceFactory;

    private readonly FilesApplicationApiClient _filesApplicationApiClient;


    public ProjectStatisticsService(IProjectStatisticsBatchRepository repository, IMapper mapper,
        IUnitOfWork unitOfWork, IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext,
        IProjectStatisticsBatchItemRepository projectStatisticsBatchItemRepository,
        IProjectStatisticsBatchItemViewRepository projectStatisticsBatchItemViewRepository,
        IAsyncTaskService asyncTaskService, IProjectStatisticsBatchRepository projectStatisticsBatchRepository,
        RptProjectServiceFactory rptProjectServiceFactory, FilesApplicationApiClient filesApplicationApiClient) : base(
        repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _keyGenerator = keyGenerator;
        _currentUserContext = currentUserContext;
        _projectStatisticsBatchItemRepository = projectStatisticsBatchItemRepository;
        _projectStatisticsBatchItemViewRepository = projectStatisticsBatchItemViewRepository;
        _asyncTaskService = asyncTaskService;
        _projectStatisticsBatchRepository = projectStatisticsBatchRepository;
        _rptProjectServiceFactory = rptProjectServiceFactory;
        _filesApplicationApiClient = filesApplicationApiClient;
    }

    public async Task<PageDtoData<long, ProjectStatisticsBatchItemViewDto>> GetPageItemsAsync(
        long batchId,
        PagedQueryCriteria<ProjectStatisticsBatchItemViewQueryCriteria> criteria)
    {
        var whereExpr =
            criteria.Condition
                .BuildExpression<long, ProjectStatisticsBatchItemViewEntity,
                    ProjectStatisticsBatchItemViewQueryCriteria>();

        whereExpr = whereExpr.And(q => q.Item.BatchId == batchId);

        var orderbyExpr = criteria
            .BuildOrderExpression<long, ProjectStatisticsBatchItemViewEntity,
                ProjectStatisticsBatchItemViewQueryCriteria>();

        var data = await _projectStatisticsBatchItemViewRepository.GetPageAsync(
            whereExpr,
            criteria.PageParams.ToRowIndex(),
            criteria.PageParams.PageSize,
            orderbyExpr
        );

        return new PageDtoData<long, ProjectStatisticsBatchItemViewDto>
        {
            Totals = data.Totals,
            Rows = Mapper.Map<List<ProjectStatisticsBatchItemViewDto>>(data.Rows)
        };
    }

    public async Task<long> CreateBatchAsync(
        CreateProjectStatisticsBatchRequest request
    )
    {
        var batch = new ProjectStatisticsBatchEntity()
        {
            Key = _keyGenerator.GenerateKey(),
            StartTime = request.Start,
            EndTime = request.End,
            CreatedTime = DateTimeOffset.UtcNow,
            Category = request.Category
        };

        var rptProjectService = _rptProjectServiceFactory.GetService(request.Category);

        var projectInfoKeys = request.ProjectInfoKeys ??
                              (await rptProjectService.GetProjectKeysAsync(request.Start, request.End));

        var items = projectInfoKeys.Select(q =>
        {
            var item = new ProjectStatisticsBatchItemEntity()
            {
                Key = _keyGenerator.GenerateKey(),
                BusinessId = q.BusinessId,
                Version = q.Version,
                BatchId = batch.Key,
                Status = 1
            };
            return item;
        }).ToList();

        await _projectStatisticsBatchRepository.InsertAsync(batch);

        await _projectStatisticsBatchItemRepository.InsertAsync(items);

        await CreateExtractProjectTaskAsync(batch.Key);

        return batch.Key;
    }

    public async Task<bool> ReStatisticsAsync(long batchId)
    {
        var batch = await _projectStatisticsBatchRepository.GetAsync(batchId);

        var rptProjectService = _rptProjectServiceFactory.GetService(batch!.Category);

        var projectInfoKeys = await rptProjectService.GetProjectKeysAsync(batch.StartTime, batch.EndTime);

        var originalKeys = (await _projectStatisticsBatchItemRepository.GetListAsync(q => q.BatchId == batchId))
            .Select(q => new ProjectInfoKey(q.BusinessId, q.Version))
            .ToList();

        var items = projectInfoKeys.Select(q =>
        {
            var item = new ProjectStatisticsBatchItemEntity()
            {
                Key = _keyGenerator.GenerateKey(),
                BusinessId = q.BusinessId,
                Version = q.Version,
                BatchId = batch.Key,
                Status = 1
            };
            return item;
        }).ToList();

        var newProjects = items
            .Where(q => !originalKeys.Any(p => p.BusinessId == q.BusinessId && p.Version == q.Version)).ToList();

        await _projectStatisticsBatchItemRepository.InsertAsync(newProjects);

        await CreateExtractProjectTaskAsync(batchId, false, newProjects.Select(q => q.Key).ToList());

        return true;
    }

    public async Task<FileDto> ExportAsync(long batchId)
    {
        var batch = await _projectStatisticsBatchRepository.GetAsync(batchId);
        var rptProjectService = _rptProjectServiceFactory.GetService(batch!.Category);
        var file = await rptProjectService.ExportAsync(batchId);

        var fileInfoDto = await _filesApplicationApiClient.UploadAsync("project-statistics", file.fileName,
            _currentUserContext.GetCurrentUserId()!.Value, file.bytes);

        return new FileDto()
        {
            FileId = fileInfoDto.Key.ToString()!,
            FileName = fileInfoDto.FileName!,
            FileType = fileInfoDto.FileTypeCode!,
            FileSize = fileInfoDto.FileSize
        };
    }

    public async Task<PageDtoData<long, ProjectsDto>> GetProjectStatisticsQueryAsync(long batchId,
        PagedQueryCriteria<RptProjectQueryCriteria> criteria)
    {
        var batch = await _projectStatisticsBatchRepository.GetAsync(batchId);
        var rptProjectService = _rptProjectServiceFactory.GetService(batch!.Category);

        var result = await rptProjectService.GetPageAsync(criteria);

        return new PageDtoData<long, ProjectsDto>()
        {
            Totals = result.Totals,
            Rows = Mapper.Map<List<ProjectsDto>>(result.Rows)
        };
    }

    public async Task<bool> RemoveItemAsync(long batchId, long itemId)
    {
        var batch = await _projectStatisticsBatchRepository.GetAsync(batchId);
        if (batch == null)
            throw new ValidationException("批次不存在");
        var item = await _projectStatisticsBatchItemRepository.GetAsync(q =>
            q.BatchId == batchId && q.Key == itemId
        );
        if (item == null)
        {
            throw new ValidationException("项目不存在");
        }

        await _projectStatisticsBatchItemRepository.DeleteAsync(item);

        return true;
    }

    public async Task<bool> AddItemAsync(long batchId, List<ProjectInfoKey> projectInfoKeys)
    {
        var batch = await _projectStatisticsBatchRepository.GetAsync(batchId);

        if (batch == null)
        {
            throw new ValidationException("批次不存在");
        }

        if (projectInfoKeys == null || !projectInfoKeys.Any())
        {
            throw new ValidationException("项目数量为空");
        }

        var expr = DynamicLinqExpressions.False<ProjectStatisticsBatchItemEntity>();
        projectInfoKeys.ForEach(p =>
        {
            expr = expr.Or(q => q.BusinessId == p.BusinessId && q.Version == p.Version);
        });
        expr = expr.And(q => q.BatchId == batchId);

        var finders = (await _projectStatisticsBatchItemRepository.GetListAsync(expr)).ToList();

        var newProjects = projectInfoKeys
            .Where(p => !finders.Any(f => f.BusinessId == p.BusinessId && f.Version == p.Version))
            .ToList();

        var items = newProjects.Select(n =>
        {
            var item = new ProjectStatisticsBatchItemEntity()
            {
                Key = _keyGenerator.GenerateKey(),
                BusinessId = n.BusinessId,
                Version = n.Version,
                BatchId = batchId,
                Status = 1
            };
            return item;
        }).ToList();

        await _projectStatisticsBatchItemRepository.InsertAsync(items);

        await CreateExtractProjectTaskAsync(batchId, false, items.Select(q => q.Key).ToList());

        return true;
    }

    public async Task<bool> CreateExtractProjectTaskAsync(long batchId, bool force = true, List<long>? itemIds = null)
    {
        var batch = await _projectStatisticsBatchRepository.GetAsync(batchId);
        if (batch == null)
        {
            throw new ValidationException("批次不存在");
        }

        var items = await _projectStatisticsBatchItemRepository.GetListAsync(q => q.BatchId == batchId);

        if (itemIds != null && itemIds.Any())
        {
            items = items.Where(q => itemIds.Contains(q.Key)).ToList();
        }

        if (!force)
        {
            items = items.Where(q => q.Status != 3).ToList();
        }

        await _asyncTaskService.CreateTaskAsync(batchId.ToString(), "ExtractProjectTask",
            new Dictionary<string, object?>()
            {
                { "BatchId", batchId },
                { "ItemIds", items.Select(q => q.Key).ToList() }
            }
        );
        return true;
    }
}

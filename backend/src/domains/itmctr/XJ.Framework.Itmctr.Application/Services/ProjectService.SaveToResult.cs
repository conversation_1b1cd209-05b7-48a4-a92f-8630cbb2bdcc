using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.Extensions;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Files.ApiClient;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ProjectService
{
    public async Task<bool> InsertProjectAndHistoryAsync(string formCode, string businessId, string version)
    {
        var dynamicFormApplicationApiClient = _serviceProvider.GetRequiredService<DynamicFormApplicationApiClient>();

        var formDefinitionDto =
            await dynamicFormApplicationApiClient.GetNamedVersionFormInstanceAsync(businessId, version);

        var dataValue = await FormDefinitionHelper.GetFieldValuesAsync(formDefinitionDto);

        var formData = formDefinitionDto.FormData;

        return await SaveToResultDataAsync(formCode, businessId, version, formDefinitionDto.Language!, dataValue,
            formData);
    }

    public async Task<bool> SaveToResultDataAsync(string formCode, string businessId, string version, string language,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        await _unitOfWork.BeginTransactionAsync();

        try
        {
            var currentVersion =
                await _projectRepository.GetAsync(q => q.BusinessId == businessId && q.Version == version);


            var attachEntities =
                currentVersion == null
                    ? []
                    : (await _projectAttachRepository.GetListAsync(q => q.BusinessId == currentVersion.BusinessId))
                    .ToList();


            var sponsorEntities =
                currentVersion == null
                    ? []
                    : (await _projectSponsorRepository.GetListAsync(q => q.BusinessId == currentVersion.BusinessId))
                    .ToList();


            var researchSiteEntities =
                currentVersion == null
                    ? []
                    : (await _projectResearchSiteRepository.GetListAsync(q =>
                        q.BusinessId == currentVersion.BusinessId))
                    .ToList();


            var interventionEntities =
                currentVersion == null
                    ? []
                    : (await _projectInterventionRepository.GetListAsync(q =>
                        q.BusinessId == currentVersion.BusinessId))
                    .ToList();


            var measurementEntities =
                currentVersion == null
                    ? []
                    : (await _projectMeasurementRepository.GetListAsync(q => q.BusinessId == currentVersion.BusinessId))
                    .ToList();


            var humanSampleEntities =
                currentVersion == null
                    ? []
                    : (await _projectHumanSampleRepository.GetListAsync(q => q.BusinessId == currentVersion.BusinessId))
                    .ToList();

            //有版本 并且当前版本不是传入的版本 此种情况需要归档
            // var archive = currentVersion != null && currentVersion.Version != version;

            var clear = currentVersion != null;

            // if (archive)
            //     await ArchiveProjectInfoAsync(currentVersion!,
            //         attachEntities,
            //         sponsorEntities,
            //         researchSiteEntities,
            //         interventionEntities,
            //         measurementEntities,
            //         humanSampleEntities);

            if (clear)
                await ClearProjectInfoAsync(currentVersion!,
                    attachEntities,
                    sponsorEntities,
                    researchSiteEntities,
                    interventionEntities,
                    measurementEntities,
                    humanSampleEntities);

            await SaveProjectInfoAsync(businessId, version, language, dataValue, formData);

            await _unitOfWork.CommitAsync();
        }
        catch (Exception)
        {
            await _unitOfWork.RollbackAsync();
            throw;
        }

        return true;
    }

    private async Task SaveProjectHumanSampleAsync(ProjectEntity project,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var rowIndex = 0;
        var humanSampleList =
            dataValue?["human_sample_collection_area"]?.ObjectArray?.Select(row => new ProjectHumanSampleEntity()
            {
                BusinessId = project.BusinessId,
                Key = IdGenerator.NextId(),
                ProjectId = project.Key,
                Version = project.Version,
                RowIndex = rowIndex++,
                sample_name_zh = GetFormValueFromDataValue(row, "sample_name", "zh"),
                sample_name_en = GetFormValueFromDataValue(row, "sample_name", "en"),
                tissue_zh = GetFormValueFromDataValue(row, "tissue", "zh"),
                tissue_en = GetFormValueFromDataValue(row, "tissue", "en"),
                fate_of_sample = GetFormValueFromDataValue(row, "fate_of_sample"),
                sample_note_zh = GetFormValueFromDataValue(row, "sample_note", "zh"),
                sample_note_en = GetFormValueFromDataValue(row, "sample_note", "en"),
            }).ToList();

        if (humanSampleList != null)
            await _projectHumanSampleRepository.DetachAndInsertAsync(humanSampleList);
    }

    private async Task SaveProjectMeasurementAsync(ProjectEntity project,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var rowIndex = 0;
        var measurementList = dataValue?["measurement_index_area"]?.ObjectArray?.Select(row =>
            new ProjectMeasurementEntity()
            {
                BusinessId = project.BusinessId,
                Key = IdGenerator.NextId(),
                ProjectId = project.Key,
                Version = project.Version,
                RowIndex = rowIndex++,
                outcome_name_zh = GetFormValueFromDataValue(row, "outcome_name", "zh"),
                outcome_name_en = GetFormValueFromDataValue(row, "outcome_name", "en"),
                outcome_type = GetFormValueFromDataValue(row, "outcome_type"),
                measure_time_point_zh = GetFormValueFromDataValue(row, "measure_time_point", "zh"),
                measure_time_point_en = GetFormValueFromDataValue(row, "measure_time_point", "en"),
                measure_method_zh = GetFormValueFromDataValue(row, "measure_method", "zh"),
                measure_method_en = GetFormValueFromDataValue(row, "measure_method", "en"),
            }).ToList();
        if (measurementList != null)
            await _projectMeasurementRepository.DetachAndInsertAsync(measurementList);
    }

    private async Task SaveProjectInterventionAsync(ProjectEntity project,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var rowIndex = 0;
        var interventionList = dataValue?["intervention_area"]?.ObjectArray?.Select(row =>
            new ProjectInterventionEntity()
            {
                BusinessId = project.BusinessId,
                Key = IdGenerator.NextId(),
                ProjectId = project.Key,
                Version = project.Version,
                RowIndex = rowIndex++,
                intervention_group_zh = GetFormValueFromDataValue(row, "intervention_group", "zh"),
                intervention_group_en = GetFormValueFromDataValue(row, "intervention_group", "en"),
                intervention_sample_size = GetFormValueFromDataValue(row, "intervention_sample_size"),
                intervention_name_zh = GetFormValueFromDataValue(row, "intervention_name", "zh"),
                intervention_name_en = GetFormValueFromDataValue(row, "intervention_name", "en"),
                intervention_code = GetFormValueFromDataValue(row, "intervention_code"),
            }).ToList();
        if (interventionList != null)
            await _projectInterventionRepository.DetachAndInsertAsync(interventionList);
    }

    private async Task SaveProjectResearchSiteAsync(ProjectEntity project,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var rowIndex = 0;
        var researchSiteList = dataValue?["research_site_area"]?.ObjectArray?.Select(row =>
            new ProjectResearchSiteEntity()
            {
                BusinessId = project.BusinessId,
                Key = IdGenerator.NextId(),
                ProjectId = project.Key,
                Version = project.Version,
                RowIndex = rowIndex++,
                site_country_zh = GetFormValueFromDataValue(row, "site_country", "zh"),
                site_country_en = GetFormValueFromDataValue(row, "site_country", "en"),
                site_province_zh = GetFormValueFromDataValue(row, "site_province", "zh"),
                site_province_en = GetFormValueFromDataValue(row, "site_province", "en"),
                site_city_zh = GetFormValueFromDataValue(row, "site_city", "zh"),
                site_city_en = GetFormValueFromDataValue(row, "site_city", "en"),
                site_institution_zh = GetFormValueFromDataValue(row, "site_institution", "zh"),
                site_institution_en = GetFormValueFromDataValue(row, "site_institution", "en"),
                site_level_zh = GetFormValueFromDataValue(row, "site_level", "zh"),
                site_level_en = GetFormValueFromDataValue(row, "site_level", "en"),
            }).ToList();

        if (researchSiteList != null)
            await _projectResearchSiteRepository.DetachAndInsertAsync(researchSiteList);
    }

    private async Task SaveProjectSponsorAsync(ProjectEntity project,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var rowIndex = 0;
        var sponsorList = dataValue?["trial_sponsor_area"]?.ObjectArray?.Select(row => new ProjectSponsorEntity()
        {
            BusinessId = project.BusinessId,
            Key = IdGenerator.NextId(),
            ProjectId = project.Key,
            Version = project.Version,
            RowIndex = rowIndex++,
            sponsor_country_zh = GetFormValueFromDataValue(row, "sponsor_country", "zh"),
            sponsor_country_en = GetFormValueFromDataValue(row, "sponsor_country", "en"),
            sponsor_province_zh = GetFormValueFromDataValue(row, "sponsor_province", "zh"),
            sponsor_province_en = GetFormValueFromDataValue(row, "sponsor_province", "en"),
            sponsor_city_zh = GetFormValueFromDataValue(row, "sponsor_city", "zh"),
            sponsor_city_en = GetFormValueFromDataValue(row, "sponsor_city", "en"),
            sponsor_institution_zh = GetFormValueFromDataValue(row, "sponsor_institution", "zh"),
            sponsor_institution_en = GetFormValueFromDataValue(row, "sponsor_institution", "en"),
            sponsor_address_zh = GetFormValueFromDataValue(row, "sponsor_address", "zh"),
            sponsor_address_en = GetFormValueFromDataValue(row, "sponsor_address", "en"),
        }).ToList();

        if (sponsorList != null)
            await _projectSponsorRepository.DetachAndInsertAsync(sponsorList);
    }

    private async Task SaveProjectAttachAsync(ProjectEntity project,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        //伦理委员会批件附件
        var ethicCommitteeApprovedFile =
            dataValue?["ethic_committee_approved_file"]?.KeyValue != null
            && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "ethic_committee_approved_file", "fileId"))
            && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "ethic_committee_approved_file", "fileName"))
                ? new ProjectAttachEntity()
                {
                    Key = Guid.NewGuid(),
                    FileId = Guid.Parse(dataValue?["ethic_committee_approved_file"]?.KeyValue?["fileId"]?.ToString()!),
                    BusinessId = project.BusinessId,
                    Version = project.Version,
                    FileTypeCode = "ethic_committee_approved_file",
                    ProjectId = project.Key,
                    FileName = dataValue?["ethic_committee_approved_file"]?.KeyValue?["fileName"]?.ToString() ??
                               string.Empty,
                    FileSize = Convert.ToInt64(
                        GetStringFromFieldValue(dataValue, "ethic_committee_approved_file", "fileSize", "-1"))
                }
                : null;

        //国家药监局批准附件
        var mpaApprovedFile = dataValue?["mpa_approved_file"]?.KeyValue != null
                              && !string.IsNullOrEmpty(
                                  GetStringFromFieldValue(dataValue, "mpa_approved_file", "fileId"))
                              && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "mpa_approved_file",
                                  "fileName"))
            ? new ProjectAttachEntity()
            {
                Key = Guid.NewGuid(),
                FileId = Guid.Parse(dataValue?["mpa_approved_file"]?.KeyValue?["fileId"]?.ToString()!),
                BusinessId = project.BusinessId,
                Version = project.Version,
                FileTypeCode = "mpa_approved_file",
                ProjectId = project.Key,
                FileName = dataValue?["mpa_approved_file"]?.KeyValue?["fileName"]?.ToString() ??
                           string.Empty,
                FileSize = Convert.ToInt64(
                    GetStringFromFieldValue(dataValue, "mpa_approved_file", "fileSize", "-1")
                )
            }
            : null;
        //研究方案文件
        var studyProtocol = dataValue?["study_protocol"]?.KeyValue != null
                            && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "study_protocol", "fileId"))
                            && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "study_protocol", "fileName"))
            ? new ProjectAttachEntity()
            {
                Key = Guid.NewGuid(),
                FileId = Guid.Parse(dataValue?["study_protocol"]?.KeyValue?["fileId"]?.ToString()!),
                BusinessId = project.BusinessId,
                Version = project.Version,
                FileTypeCode = "study_protocol",
                ProjectId = project.Key,
                FileName = dataValue?["study_protocol"]?.KeyValue?["fileName"]?.ToString() ??
                           string.Empty,
                FileSize = Convert.ToInt64(
                    GetStringFromFieldValue(dataValue, "study_protocol", "fileSize", "-1")
                )
            }
            : null;
        //知情同意书
        var informedConsentFile = dataValue?["informed_consent_file"]?.KeyValue != null
                                  && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "informed_consent_file",
                                      "fileId"))
                                  && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue, "informed_consent_file",
                                      "fileName"))
            ? new ProjectAttachEntity()
            {
                Key = Guid.NewGuid(),
                FileId = Guid.Parse(dataValue?["informed_consent_file"]?.KeyValue?["fileId"]?.ToString()!),
                BusinessId = project.BusinessId,
                Version = project.Version,
                FileTypeCode = "informed_consent_file",
                ProjectId = project.Key,
                FileName = dataValue?["informed_consent_file"]?.KeyValue?["fileName"]?.ToString() ??
                           string.Empty,
                FileSize = Convert.ToInt64(
                    GetStringFromFieldValue(dataValue, "informed_consent_file", "fileSize", "-1")
                )
            }
            : null;
        // 上传试验完成后的统计结果
        var statisticalResultsFile = dataValue?["statistical_results_file"]?.KeyValue != null
                                     && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue,
                                         "statistical_results_file", "fileId"))
                                     && !string.IsNullOrEmpty(GetStringFromFieldValue(dataValue,
                                         "statistical_results_file", "fileName"))
            ? new ProjectAttachEntity()
            {
                Key = Guid.NewGuid(),
                FileId = Guid.Parse(dataValue?["statistical_results_file"]?.KeyValue?["fileId"]?.ToString()!),
                BusinessId = project.BusinessId,
                Version = project.Version,
                FileTypeCode = "statistical_results_file",
                ProjectId = project.Key,
                FileName = dataValue?["statistical_results_file"]?.KeyValue?["fileName"]?.ToString() ??
                           string.Empty,
                FileSize = Convert.ToInt64(
                    GetStringFromFieldValue(dataValue, "statistical_results_file", "fileSize", "-1")
                )
            }
            : null;

        var attachEntities = new List<ProjectAttachEntity>();
        if (ethicCommitteeApprovedFile != null)
        {
            attachEntities.Add(ethicCommitteeApprovedFile);
        }

        if (mpaApprovedFile != null)
        {
            attachEntities.Add(mpaApprovedFile);
        }

        if (studyProtocol != null)
        {
            attachEntities.Add(studyProtocol);
        }

        if (informedConsentFile != null)
        {
            attachEntities.Add(informedConsentFile);
        }

        if (statisticalResultsFile != null)
        {
            attachEntities.Add(statisticalResultsFile);
        }

        await _projectAttachRepository.DetachAndInsertAsync(attachEntities);
    }

    private async Task<ProjectEntity> SaveProjectAsync(string businessId, string version, string language,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var project = new ProjectEntity()
        {
            BusinessId = businessId,
            FormCode = FormCode,
            Key = IdGenerator.NextId(),
            language = language,
            Version = version,
        };

        #region project

        project.registration_number = dataValue?["registration_number"]?.StringValue;
        project.registration_status = dataValue?["registration_status"]?.StringValue;
        project.publictitle_zh = GetStringFromFieldValue(dataValue, "public_title", "zh");
        project.publictitle_en = GetStringFromFieldValue(dataValue, "public_title", "en");
        project.english_acronym_zh = GetStringFromFieldValue(dataValue, "english_acronym", "zh");
        project.english_acronym_en = GetStringFromFieldValue(dataValue, "english_acronym", "en");
        project.scientific_title_zh = GetStringFromFieldValue(dataValue, "scientific_title", "zh");
        project.scientific_title_en = GetStringFromFieldValue(dataValue, "scientific_title", "en");
        project.scientific_title_acronym_zh = GetStringFromFieldValue(dataValue, "scientific_title_acronym", "zh");
        project.scientific_title_acronym_en = GetStringFromFieldValue(dataValue, "scientific_title_acronym", "en");
        project.study_subject_id = dataValue?["study_subject_id"]?.StringValue;
        project.partner_registry_number = dataValue?["partner_registry_number"]?.StringValue;
        project.applicant_zh = GetStringFromFieldValue(dataValue, "applicant", "zh");
        project.applicant_en = GetStringFromFieldValue(dataValue, "applicant", "en");
        project.study_leader_zh = GetStringFromFieldValue(dataValue, "study_leader", "zh");
        project.study_leader_en = GetStringFromFieldValue(dataValue, "study_leader", "en");
        project.applicant_telephone = dataValue?["applicant_telephone"]?.StringValue;
        project.study_leader_telephone = dataValue?["study_leader_telephone"]?.StringValue;
        project.applicant_fax = dataValue?["applicant_fax"]?.StringValue;
        project.study_leader_fax = dataValue?["study_leader_fax"]?.StringValue;
        project.applicant_email = dataValue?["applicant_email"]?.StringValue;
        project.study_leader_email = dataValue?["study_leader_email"]?.StringValue;
        project.applicant_website = dataValue?["applicant_website"]?.StringValue;
        project.study_leader_website = dataValue?["study_leader_website"]?.StringValue;
        project.applicant_address_zh = GetStringFromFieldValue(dataValue, "applicant_address", "zh");
        project.applicant_address_en = GetStringFromFieldValue(dataValue, "applicant_address", "en");
        project.study_leader_address_zh = GetStringFromFieldValue(dataValue, "study_leader_address", "zh");
        project.study_leader_address_en = GetStringFromFieldValue(dataValue, "study_leader_address", "en");
        project.applicant_postcode = dataValue?["applicant_postcode"]?.StringValue;
        project.study_leader_postcode = dataValue?["study_leader_postcode"]?.StringValue;
        project.applicant_affiliation_zh = GetStringFromFieldValue(dataValue, "applicant_affiliation", "zh");
        project.applicant_affiliation_en = GetStringFromFieldValue(dataValue, "applicant_affiliation", "en");
        project.study_leader_affiliation_zh = GetStringFromFieldValue(dataValue, "study_leader_affiliation", "zh");
        project.study_leader_affiliation_en = GetStringFromFieldValue(dataValue, "study_leader_affiliation", "en");
        project.ethic_committee_approved = dataValue?["ethic_committee_approved"]?.StringValue;
        project.ethic_committee_approved_no = dataValue?["ethic_committee_approved_no"]?.StringValue;
        project.ethic_committee_name_zh = GetStringFromFieldValue(dataValue, "ethic_committee_name", "zh");
        project.ethic_committee_name_en = GetStringFromFieldValue(dataValue, "ethic_committee_name", "en");
        if (!string.IsNullOrWhiteSpace(dataValue?["ethic_committee_approved_date"]?.StringValue))
            project.ethic_committee_approved_date =
                Convert.ToDateTime(dataValue?["ethic_committee_approved_date"]?.StringValue);
        project.ethic_committee_contact_zh = GetStringFromFieldValue(dataValue, "ethic_committee_contact", "zh");
        project.ethic_committee_contact_en = GetStringFromFieldValue(dataValue, "ethic_committee_contact", "en");
        project.ethic_committee_address_zh = GetStringFromFieldValue(dataValue, "ethic_committee_address", "zh");
        project.ethic_committee_address_en = GetStringFromFieldValue(dataValue, "ethic_committee_address", "en");
        project.ethic_committee_phone = dataValue?["ethic_committee_phone"]?.StringValue;
        project.ethic_committee_email = dataValue?["ethic_committee_email"]?.StringValue;
        project.mpa_approved_no = dataValue?["mpa_approved_no"]?.StringValue;
        if (!string.IsNullOrWhiteSpace(dataValue?["mpa_approved_date"]?.StringValue))
            project.mpa_approved_date = Convert.ToDateTime(dataValue?["mpa_approved_date"]?.StringValue);
        project.primary_sponsor_zh = GetStringFromFieldValue(dataValue, "primary_sponsor", "zh");
        project.primary_sponsor_en = GetStringFromFieldValue(dataValue, "primary_sponsor", "en");
        project.primary_sponsor_address_zh = GetStringFromFieldValue(dataValue, "primary_sponsor_address", "zh");
        project.primary_sponsor_address_en = GetStringFromFieldValue(dataValue, "primary_sponsor_address", "en");
        project.funding_source_zh = GetStringFromFieldValue(dataValue, "funding_source", "zh");
        project.funding_source_en = GetStringFromFieldValue(dataValue, "funding_source", "en");
        project.target_disease_zh = GetStringFromFieldValue(dataValue, "target_disease", "zh");
        project.target_disease_en = GetStringFromFieldValue(dataValue, "target_disease", "en");
        project.target_disease_code = dataValue?["target_disease_code"]?.StringValue;
        project.study_type = dataValue?["study_type"]?.StringValue;
        project.study_design = dataValue?["study_design"]?.StringValue;
        project.study_phase = dataValue?["study_phase"]?.StringValue;
        project.study_objectives_zh = GetStringFromFieldValue(dataValue, "study_objectives", "zh");
        project.study_objectives_en = GetStringFromFieldValue(dataValue, "study_objectives", "en");
        project.treatment_description_zh = GetStringFromFieldValue(dataValue, "treatment_description", "zh");
        project.treatment_description_en = GetStringFromFieldValue(dataValue, "treatment_description", "en");
        project.inclusion_criteria_zh = GetStringFromFieldValue(dataValue, "inclusion_criteria", "zh");
        project.inclusion_criteria_en = GetStringFromFieldValue(dataValue, "inclusion_criteria", "en");
        project.exclusion_criteria_zh = GetStringFromFieldValue(dataValue, "exclusion_criteria", "zh");
        project.exclusion_criteria_en = GetStringFromFieldValue(dataValue, "exclusion_criteria", "en");
        if (!string.IsNullOrWhiteSpace(GetStringFromFieldValue(dataValue, "study_execute_time", "start")))
            project.study_time_start =
                Convert.ToDateTime(GetStringFromFieldValue(dataValue, "study_execute_time", "start"));
        if (!string.IsNullOrWhiteSpace(GetStringFromFieldValue(dataValue, "study_execute_time", "end")))
            project.study_time_end =
                Convert.ToDateTime(GetStringFromFieldValue(dataValue, "study_execute_time", "end"));

        if (!string.IsNullOrWhiteSpace(GetStringFromFieldValue(dataValue, "recruiting_time", "start")))
            project.recruiting_time_start =
                Convert.ToDateTime(GetStringFromFieldValue(dataValue, "recruiting_time", "start"));
        if (!string.IsNullOrWhiteSpace(GetStringFromFieldValue(dataValue, "recruiting_time", "end")))
            project.recruiting_time_end =
                Convert.ToDateTime(GetStringFromFieldValue(dataValue, "recruiting_time", "end"));
        project.gold_standard_zh = GetStringFromFieldValue(dataValue, "gold_standard", "zh");
        project.gold_standard_en = GetStringFromFieldValue(dataValue, "gold_standard", "en");
        project.index_test_zh = GetStringFromFieldValue(dataValue, "index_test", "zh");
        project.index_test_en = GetStringFromFieldValue(dataValue, "index_test", "en");
        project.target_condition_zh = GetStringFromFieldValue(dataValue, "target_condition", "zh");
        project.target_condition_en = GetStringFromFieldValue(dataValue, "target_condition", "en");
        project.target_sample_size = dataValue?["target_sample_size"]?.StringValue;
        project.confounding_condition_zh = GetStringFromFieldValue(dataValue, "confounding_condition", "zh");
        project.confounding_condition_en = GetStringFromFieldValue(dataValue, "confounding_condition", "en");
        project.confounding_sample_size = dataValue?["confounding_sample_size"]?.StringValue;
        project.intervention_total_sample_size = dataValue?["intervention_total_sample_size"]?.StringValue;
        project.recruiting_status = dataValue?["recruiting_status"]?.StringValue;
        project.age_range_min = GetStringFromFieldValue(dataValue, "age_range", "min");
        project.age_range_max = GetStringFromFieldValue(dataValue, "age_range", "max");
        project.gender = dataValue?["gender"]?.StringValue;
        project.randomization_procedure_zh = GetStringFromFieldValue(dataValue, "randomization_procedure", "zh");
        project.randomization_procedure_en = GetStringFromFieldValue(dataValue, "randomization_procedure", "en");
        project.sign_informed_consent = dataValue?["sign_informed_consent"]?.StringValue;
        project.follow_up_length = GetStringFromFieldValue(dataValue, "follow_up_length", "value");
        project.follow_up_unit = GetStringFromFieldValue(dataValue, "follow_up_length", "unit");
        project.allocation_concealment_zh = GetStringFromFieldValue(dataValue, "allocation_concealment", "zh");
        project.allocation_concealment_en = GetStringFromFieldValue(dataValue, "allocation_concealment", "en");
        project.blinding_zh = GetStringFromFieldValue(dataValue, "blinding", "zh");
        project.blinding_en = GetStringFromFieldValue(dataValue, "blinding", "en");
        project.unblinding_rules_zh = GetStringFromFieldValue(dataValue, "unblinding_rules", "zh");
        project.unblinding_rules_en = GetStringFromFieldValue(dataValue, "unblinding_rules", "en");
        project.statistical_methods_zh = GetStringFromFieldValue(dataValue, "statistical_methods", "zh");
        project.statistical_methods_en = GetStringFromFieldValue(dataValue, "statistical_methods", "en");
        project.calculated_results_zh = GetStringFromFieldValue(dataValue, "calculated_results", "zh");
        project.calculated_results_en = GetStringFromFieldValue(dataValue, "calculated_results", "en");
        project.calculated_results_public = dataValue?["calculated_results_public"]?.StringValue;
        project.utn = dataValue?["utn"]?.StringValue;
        project.ipd_sharing = dataValue?["ipd_sharing"]?.StringValue;
        project.ipd_sharing_way_zh = GetStringFromFieldValue(dataValue, "ipd_sharing_way", "zh");
        project.ipd_sharing_way_en = GetStringFromFieldValue(dataValue, "ipd_sharing_way", "en");
        project.data_collection_zh = GetStringFromFieldValue(dataValue, "data_collection", "zh");
        project.data_collection_en = GetStringFromFieldValue(dataValue, "data_collection", "en");
        project.safety_committee = dataValue?["safety_committee"]?.StringValue;
        project.publication_info_zh = GetStringFromFieldValue(dataValue, "publication_info", "zh");
        project.publication_info_en = GetStringFromFieldValue(dataValue, "publication_info", "en");

        #endregion


        if (formData?.TryGetValue("SendNumberTime", out var sendNumberTimeString) != null &&
            !string.IsNullOrEmpty(sendNumberTimeString))
        {
            project.send_number_time =
                DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(sendNumberTimeString));
        }

        if (formData?.TryGetValue("FirstSubmitTime", out var firstSubmitTimeString) != null &&
            !string.IsNullOrEmpty(firstSubmitTimeString))
        {
            project.first_submit_time =
                DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(firstSubmitTimeString));
        }

        if (formData?.TryGetValue("secondaryID", out var secondaryId) != null &&
            !string.IsNullOrEmpty(secondaryId))
        {
            project.chi_reg_number = secondaryId;
        }

        if (formData?.TryGetValue("FinalApprovalTime", out var finalApprovalTimeString) != null &&
            !string.IsNullOrEmpty(finalApprovalTimeString))
        {
            project.release_time =
                DateTimeOffset.FromUnixTimeMilliseconds(Convert.ToInt64(finalApprovalTimeString));
        }
        else
        {
            var historyString = "[]";
            formData?.TryGetValue("ApprovalHistory", out historyString);

            var histories = JsonSerializer.Deserialize<List<ApprovalHistoryDto>>(historyString ?? "[]")!;

            if (histories.Any())
            {
                project.release_time = (histories
                                            .OrderByDescending(q => q.OperateTime)
                                            .FirstOrDefault(q => q.Action == "Approval") ??
                                        new ApprovalHistoryDto()
                                        {
                                            OperateTime = DateTimeOffset.Now
                                        }).OperateTime;
            }
        }

        if (project.release_time == DateTimeOffset.MinValue)
        {
            project.release_time = DateTimeOffset.Now;
        }

        await _projectRepository.DetachAndInsertAsync(project);

        return project;
    }

    private async Task SaveProjectInfoAsync(string businessId, string version, string language,
        Dictionary<string, MultiTypeValue?>? dataValue,
        Dictionary<string, string?>? formData)
    {
        var project = await SaveProjectAsync(businessId, version, language, dataValue, formData);

        await SaveProjectAttachAsync(project, dataValue, formData);

        await SaveProjectSponsorAsync(project, dataValue, formData);

        await SaveProjectResearchSiteAsync(project, dataValue, formData);

        await SaveProjectInterventionAsync(project, dataValue, formData);

        await SaveProjectMeasurementAsync(project, dataValue, formData);

        await SaveProjectHumanSampleAsync(project, dataValue, formData);
    }

    private async Task ClearProjectHumanSampleAsync(List<ProjectHumanSampleEntity> humanSampleEntities)
    {
        await _projectHumanSampleRepository.DetachAndDeleteAsync(humanSampleEntities);
    }

    private async Task ClearProjectMeasurementAsync(List<ProjectMeasurementEntity> measurementEntities)
    {
        await _projectMeasurementRepository.DetachAndDeleteAsync(measurementEntities);
    }

    private async Task ClearProjectInterventionAsync(List<ProjectInterventionEntity> interventionEntities)
    {
        await _projectInterventionRepository.DetachAndDeleteAsync(interventionEntities);
    }

    private async Task ClearProjectResearchSiteAsync(List<ProjectResearchSiteEntity> researchSiteEntities)
    {
        await _projectResearchSiteRepository.DetachAndDeleteAsync(researchSiteEntities);
    }

    private async Task ClearProjectSponsorAsync(List<ProjectSponsorEntity> sponsorEntities)
    {
        await _projectSponsorRepository.DetachAndDeleteAsync(sponsorEntities);
    }

    private async Task ClearProjectAttachAsync(List<ProjectAttachEntity> attachEntities)
    {
        await _projectAttachRepository.DetachAndDeleteAsync(attachEntities);
    }

    private async Task ClearProjectAsync(ProjectEntity currentVersion)
    {
        await _projectRepository.DetachAndDeleteAsync(currentVersion);
    }

    private async Task ClearProjectInfoAsync(ProjectEntity currentVersion,
        List<ProjectAttachEntity> attachEntities,
        List<ProjectSponsorEntity> sponsorEntities,
        List<ProjectResearchSiteEntity> researchSiteEntities,
        List<ProjectInterventionEntity> interventionEntities,
        List<ProjectMeasurementEntity> measurementEntities,
        List<ProjectHumanSampleEntity> humanSampleEntities
    )
    {
        await ClearProjectAsync(currentVersion);

        await ClearProjectAttachAsync(attachEntities);

        await ClearProjectSponsorAsync(sponsorEntities);

        await ClearProjectResearchSiteAsync(researchSiteEntities);

        await ClearProjectInterventionAsync(interventionEntities);

        await ClearProjectMeasurementAsync(measurementEntities);

        await ClearProjectHumanSampleAsync(humanSampleEntities);
    }

    private string GetFormValueFromDataValue(Dictionary<string, object?>? itemValue, string fieldCode,
        string key)
    {
        if (!(itemValue?.ContainsKey(fieldCode) ?? false))
        {
            return string.Empty;
        }

        var value = itemValue?[fieldCode];

        var fieldValue = FieldDtoExtensions.RefactMultiTypeValue(value);

        if (!(fieldValue?.KeyValue!.ContainsKey(key) ?? false))
        {
            return string.Empty;
        }

        return fieldValue?.KeyValue?[key]?.ToString() ?? "";
    }

    private string GetFormValueFromDataValue(Dictionary<string, object?>? itemValue, string fieldCode)
    {
        var value = itemValue?[fieldCode];

        var fieldValue = FieldDtoExtensions.RefactMultiTypeValue(value);

        return fieldValue?.StringValue ?? "";
    }

    private string GetStringFromFieldValue(Dictionary<string, MultiTypeValue?>? value, string propertyName, string key,
        string defaultValue = "")
    {
        var keyValue = value?[propertyName]?.KeyValue;
        object? result = null;
        var finder = keyValue?.TryGetValue(key, out result) ?? false;

        // if (!finder)
        // {
        //     Console.WriteLine(@"{0} not found in {1}", key, propertyName);
        // }

        return result?.ToString() ?? defaultValue;
    }


    #region 归档项目信息(已废弃)

    /*
     private async Task ArchiveProjectHumanSampleAsync(List<ProjectHumanSampleEntity> humanSampleEntities)
       {
           var humanSampleHistoryEntities = humanSampleEntities.Select(entity =>
           {
               var result = new ProjectHumanSampleHistoryEntity()
               {
                   BusinessId = entity.BusinessId,
                   Key = IdGenerator.NextId(),
                   ProjectId = entity.ProjectId,
                   Version = entity.Version,
                   RowIndex = entity.RowIndex,
               };
               _mapper.Map(entity, result);
               result.Key = IdGenerator.NextId();
               return result;
           }).ToList();

           await _projectHumanSampleHistoryRepository.InsertAsync(humanSampleHistoryEntities);
       }

       private async Task ArchiveProjectMeasurementAsync(List<ProjectMeasurementEntity> measurementEntities)
       {
           var measurementHistoryEntities = measurementEntities.Select(entity =>
           {
               var result = new ProjectMeasurementHistoryEntity()
               {
                   BusinessId = entity.BusinessId,
                   Key = IdGenerator.NextId(),
                   ProjectId = entity.ProjectId,
                   Version = entity.Version,
                   RowIndex = entity.RowIndex,
               };
               _mapper.Map(entity, result);
               result.Key = IdGenerator.NextId();
               return result;
           }).ToList();

           await _projectMeasurementHistoryRepository.InsertAsync(measurementHistoryEntities);
       }

       private async Task ArchiveProjectInterventionAsync(List<ProjectInterventionEntity> interventionEntities)
       {
           var interventionHistoryEntities = interventionEntities.Select(entity =>
           {
               var result = new ProjectInterventionHistoryEntity()
               {
                   BusinessId = entity.BusinessId,
                   Key = IdGenerator.NextId(),
                   ProjectId = entity.ProjectId,
                   Version = entity.Version,
                   RowIndex = entity.RowIndex,
               };
               _mapper.Map(entity, result);
               result.Key = IdGenerator.NextId();
               return result;
           }).ToList();

           await _projectInterventionHistoryRepository.InsertAsync(interventionHistoryEntities);
       }

       private async Task ArchiveProjectResearchSiteAsync(List<ProjectResearchSiteEntity> researchSiteEntities)
       {
           var researchSiteHistoryEntities = researchSiteEntities.Select(entity =>
           {
               var result = new ProjectResearchSiteHistoryEntity()
               {
                   BusinessId = entity.BusinessId,
                   Key = IdGenerator.NextId(),
                   ProjectId = entity.ProjectId,
                   Version = entity.Version,
                   RowIndex = entity.RowIndex,
               };
               _mapper.Map(entity, result);
               result.Key = IdGenerator.NextId();
               return result;
           }).ToList();

           await _projectResearchSiteHistoryRepository.InsertAsync(researchSiteHistoryEntities);
       }

       private async Task ArchiveProjectSponsorAsync(List<ProjectSponsorEntity> sponsorEntities)
       {
           var sponsorHistoryEntities = sponsorEntities.Select(entity =>
           {
               var result = new ProjectSponsorHistoryEntity()
               {
                   BusinessId = entity.BusinessId,
                   Key = IdGenerator.NextId(),
                   ProjectId = entity.ProjectId,
                   Version = entity.Version,
                   RowIndex = entity.RowIndex,
               };
               _mapper.Map(entity, result);
               result.Key = IdGenerator.NextId();
               return result;
           }).ToList();

           await _projectSponsorHistoryRepository.InsertAsync(sponsorHistoryEntities);
       }

       private async Task ArchiveProjectAttachAsync(List<ProjectAttachEntity> attachEntities)
       {
           var attachHistoryEntities = attachEntities.Select(entity =>
           {
               var result = new ProjectAttachHistoryEntity()
               {
                   BusinessId = entity.BusinessId,
                   Key = Guid.NewGuid(),
                   FileId = entity.FileId,
                   FileName = entity.FileName,
                   FileSize = entity.FileSize,
                   FileTypeCode = entity.FileTypeCode,
                   ProjectId = entity.ProjectId,
                   Version = entity.Version,
               };
               _mapper.Map(entity, result);
               result.Key = Guid.NewGuid();
               return result;
           }).ToList();

           await _projectAttachHistoryRepository.InsertAsync(attachHistoryEntities);
       }

       private async Task ArchiveProjectAsync(ProjectEntity currentVersion)
       {
           var projectHistory = new ProjectHistoryEntity()
           {
               Key = IdGenerator.NextId(),
               BusinessId = currentVersion.BusinessId,
               FormCode = currentVersion.FormCode,
               language = currentVersion.language,
               Version = currentVersion.Version,
           };
           _mapper.Map(currentVersion, projectHistory);


           projectHistory.Key = IdGenerator.NextId();

           await _projectHistoryRepository.InsertAsync(projectHistory);
       }

       private async Task ArchiveProjectInfoAsync(ProjectEntity currentVersion,
           List<ProjectAttachEntity> attachEntities,
           List<ProjectSponsorEntity> sponsorEntities,
           List<ProjectResearchSiteEntity> researchSiteEntities,
           List<ProjectInterventionEntity> interventionEntities,
           List<ProjectMeasurementEntity> measurementEntities,
           List<ProjectHumanSampleEntity> humanSampleEntities
       )
       {
           // 归档主表数据
           await ArchiveProjectAsync(currentVersion);

           // 归档文件
           await ArchiveProjectAttachAsync(attachEntities);

           // 归档试验主办单位
           await ArchiveProjectSponsorAsync(sponsorEntities);

           // 归档归档研究实施地点
           await ArchiveProjectResearchSiteAsync(researchSiteEntities);

           // 归档归档干预措施
           await ArchiveProjectInterventionAsync(interventionEntities);

           // 归档测量指标
           await ArchiveProjectMeasurementAsync(measurementEntities);

           // 归档采集人体标本
           await ArchiveProjectHumanSampleAsync(humanSampleEntities);
       }
     */

    #endregion
}

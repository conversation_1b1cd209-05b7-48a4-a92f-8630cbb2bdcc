using AutoMapper;
using System.Linq.Expressions;
using System.Net.Http.Headers;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.Extensions;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Services;

public class FormRecognitionService : IFormRecognitionService
{
    private readonly FilesApiClient _filesApiClient;
    private readonly FilesApplicationApiClient _filesApplicationApiClient;
    private readonly UnicomApiClient _unicomApiClient;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IMapper _mapper;
    private readonly DynamicFormApiClient _dynamicFormApiClient;
    private readonly DynamicFormMgtApiClient _dynamicFormMgtApiClient;

    public FormRecognitionService(FilesApiClient filesApiClient, UnicomApiClient unicomApiClient,
        IAsyncTaskService asyncTaskService, IMapper mapper, DynamicFormApiClient dynamicFormApiClient,
        DynamicFormMgtApiClient dynamicFormMgtApiClient, FilesApplicationApiClient filesApplicationApiClient)
    {
        _filesApiClient = filesApiClient;
        _unicomApiClient = unicomApiClient;
        _asyncTaskService = asyncTaskService;
        _mapper = mapper;
        _dynamicFormApiClient = dynamicFormApiClient;
        _dynamicFormMgtApiClient = dynamicFormMgtApiClient;
        _filesApplicationApiClient = filesApplicationApiClient;
    }


    public async Task<PreCheckResult> PreCheckAsync(long userId, FormRecognitionContext formRecognitionContext,
        CancellationToken cancellationToken = default)
    {
        if (formRecognitionContext.Consent == null ||
            formRecognitionContext.Ethics == null ||
            formRecognitionContext.Protocol == null)
        {
            throw new ValidationException("关键文件不能为空/ Key files cannot be null.");
        }

        var ethicsBytes = await _filesApplicationApiClient.DownloadFileAsync(formRecognitionContext.Ethics!.FileId,
            formRecognitionContext.Ethics.FileName, cancellationToken);

        var protocolBytes =
            await _filesApplicationApiClient.DownloadFileAsync(formRecognitionContext.Protocol!.FileId,
                formRecognitionContext.Protocol.FileName, cancellationToken);

        var consentBytes = await _filesApplicationApiClient.DownloadFileAsync(formRecognitionContext.Consent!.FileId,
            formRecognitionContext.Consent.FileName, cancellationToken);

        var result = await _unicomApiClient.AttachmentChecksCheckAsync(
            formRecognitionContext.Protocol.FileName, protocolBytes,
            formRecognitionContext.Ethics.FileName, ethicsBytes,
            formRecognitionContext.Consent.FileName, consentBytes
        );

        await _asyncTaskService.CreateTaskAsync(result.TaskId, "PreCheck", formRecognitionContext, result,
            applyUserId: userId);

        return new PreCheckResult
        {
            TaskId = result.TaskId,
            Protocol = result.Data.FirstOrDefault(q => q.FileType == "研究方案")?.IfMatch ?? false,
            Ethics = result.Data.FirstOrDefault(q => q.FileType == "伦理批件")?.IfMatch ?? false,
            Consent = result.Data.FirstOrDefault(q => q.FileType == "知情同意书")?.IfMatch ?? false
        };
    }

    private async Task<string> PreFillAsync(string taskId)
    {
        var result = await _unicomApiClient.PrefillFormAsync(taskId);
        return result.TaskId;
    }

    public async Task<string> RealtimeContentTranslateAsync(string content)
    {
        return content.IsNullOrEmpty()
            ? string.Empty
            : (await _unicomApiClient.RealtimeContentTranslateAsync(content)).ContentEn;
    }

    public async Task FillFormAsync(string taskId,
        FillFormLanguage language, string taskCode, string annotationKey, FormDefinitionDto formDefinitionDto)
    {
        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, taskCode);
        if (asyncTask is not { TaskStatus: AsyncTaskStatus.Completed })
        {
            throw new ValidationException("任务未完成/Task is not completed");
        }

        var data = JsonSerializer.Deserialize<AttachmentParseDto>(asyncTask.TaskResult!);

        await FillFormAsync(data!.Data, language, annotationKey, formDefinitionDto);
    }

    public async Task CompletedExtractAsync(string taskId, AttachmentParseDto input)
    {
        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "Extract");
        if (asyncTask == null || asyncTask.TaskStatus == AsyncTaskStatus.Completed)
        {
            return;
        }

        var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);
        asyncTaskOperationDto.TaskStatus = input.Succeed ? AsyncTaskStatus.Completed : AsyncTaskStatus.Failed;
        asyncTaskOperationDto.TaskResult = input.ToJson();


        asyncTask.TaskStatus = AsyncTaskStatus.Completed;
        await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
    }

    public async Task CompletedContentCompareAsync(string taskId, ContentCompareDto input)
    {
        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "ContentCompare");
        if (asyncTask == null || asyncTask.TaskStatus == AsyncTaskStatus.Completed)
        {
            return;
        }

        var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);
        asyncTaskOperationDto.TaskStatus = input.Succeed ? AsyncTaskStatus.Completed : AsyncTaskStatus.Failed;
        asyncTaskOperationDto.TaskResult = input.ToJson();

        asyncTask.TaskStatus = AsyncTaskStatus.Completed;
        await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
    }

    public async Task CompletedContentTranslateAsync(string requestId, ContentTranslateDto input)
    {
        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(requestId, "ContentTranslate");
        if (asyncTask == null || asyncTask.TaskStatus == AsyncTaskStatus.Completed)
        {
            return;
        }

        var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);
        asyncTaskOperationDto.TaskStatus = input.Succeed ? AsyncTaskStatus.Completed : AsyncTaskStatus.Failed;
        asyncTaskOperationDto.TaskResult = input.ToJson();

        asyncTask.TaskStatus = AsyncTaskStatus.Completed;
        await _asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
    }

    private async Task FillFormAsync(List<ParseItemDto> input,
        FillFormLanguage language, string annotationKey, FormDefinitionDto formDefinitionDto)
    {
        //填充对应的值以及批注
        formDefinitionDto.Groups.ForEach(group =>
        {
            group.Fields.ForEach(field =>
            {
                var finder = input.FirstOrDefault(q => q.ItemCode.ToLower().Equals(field.Code));
                if (finder != null)
                {
                    if (finder.ItemType == "form")
                    {
                        var texts = finder.Contents.Select(row =>
                        {
                            var rowIndex = row.Index;
                            var slotValues = row.Slots?.ToDictionary(q => q.SlotCode, q => q.Value!)!;
                            return new Tuple<int, Dictionary<string, string>>(rowIndex ?? 0, slotValues);
                        }).ToList();

                        var annotations = finder.Contents.Select(row =>
                        {
                            var rowIndex = row.Index;
                            var slotValues = row.Slots?.ToDictionary(q => q.SlotCode, q => q.Value!)!;
                            return new Tuple<int, string?, Dictionary<string, string>>(rowIndex ?? 0, row.Text,
                                slotValues);
                        }).ToList();

                        field.ConvertToSubformValue(texts, language.GetName());

                        field.ConvertToSubformAnnotation(annotations, annotationKey);
                    }
                    else
                    {
                        var text = finder.Contents.FirstOrDefault()?.Text;

                        field.ConvertToValue(text, language.GetName());

                        field.ConvertToAnnotation(text, annotationKey);
                    }
                }
            });
        });
        await Task.CompletedTask;
    }

    public async Task<FormCompareDto> GetFormCompareAsync(string formId, string taskId)
    {
        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "ContentCompare");
        if (asyncTask is not { TaskStatus: AsyncTaskStatus.Completed })
        {
            throw new ValidationException("任务未完成/Task is not completed");
        }

        var extractAsyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "PreCheck");

        var data = JsonSerializer.Deserialize<ContentCompareDto>(asyncTask.TaskResult!)!;

        var extractTaskData = JsonSerializer.Deserialize<FormRecognitionContext>(extractAsyncTask!.TaskData)!;

        var result = await ConvertToFormCompareAsync(data.Data, extractTaskData.Ethics!, extractTaskData.Protocol!,
            extractTaskData.Consent!);

        var formDefinition = await _dynamicFormApiClient.GetNewestFormInstanceAsync(formId);

        return await ConvertToFormCompareAsync(formDefinition, result);
    }

    private async Task<FormCompareDto> ConvertToFormCompareAsync(FormDefinitionDto formDefinition,
        FormCompareDto formCompareDto)
    {
        var result = new FormCompareDto();
        formDefinition.Groups.SelectMany(q => q.Fields)
            .ForEach(field =>
            {
                var fieldTypeFinder = FieldMapToFileSource.FieldMap.FirstOrDefault(q => q.Value.Contains(field.Code));

                if (fieldTypeFinder.Key.IsNullOrEmpty())
                {
                    return;
                }

                var fieldItem = new FormCompareFieldDto();

                fieldItem.FieldCode = field.Code;
                fieldItem.FieldName = field.LabelZh;
                fieldItem.Nested = field.Type is "multiSubForm" or "subForm";
                fieldItem.Items = new List<FormCompareItemDto>();

                var finder = formCompareDto.FirstOrDefault(q => q.FieldCode.ToLower().Equals(field.Code.ToLower()));

                if (fieldItem.Nested)
                {
                    var values = field.GetSubformDisplayValue();

                    field.Value?.ObjectArray?.ForEach((idx, row) =>
                    {
                        var rowFinder = finder?.Items.FirstOrDefault(q => q.RowIndex == idx);
                        if (rowFinder != null)
                        {
                            rowFinder.UserInput = values[idx];
                            fieldItem.Items.Add(rowFinder);
                        }
                        else
                        {
                            fieldItem.Items.Add(new FormCompareItemDto()
                            {
                                UserInput = values[idx],
                                RowIndex = idx,
                                Match = null,
                                Score = 0,
                                Summary = null,
                                ReferenceCount = 0,
                                References = new List<FormCompareReferenceDto>()
                            });
                        }
                    });
                }
                else
                {
                    if (finder is { Items.Count: > 0 })
                    {
                        finder.Items.FirstOrDefault()!.UserInput = field.GetDisplayValue();
                        fieldItem.Items.Add(finder.Items.FirstOrDefault()!);
                    }
                    else
                    {
                        fieldItem.Items.Add(new FormCompareItemDto
                        {
                            UserInput = field.GetDisplayValue(),
                            RowIndex = null,
                            Match = null,
                            Score = 0,
                            Summary = null,
                            ReferenceCount = 0,
                            References = new List<FormCompareReferenceDto>()
                        });
                    }
                }

                result.Add(fieldItem);
            });

        return await Task.FromResult(result);
    }


    private async Task<FormCompareDto> ConvertToFormCompareAsync(List<CompareItemDto> input, FileDto ethics,
        FileDto protocol, FileDto consent)
    {
        var result = new FormCompareDto();

        input.ForEach(item =>
        {
            var field = new FormCompareFieldDto();
            field.FieldCode = item.ItemCode;
            field.FieldName = item.ItemName;
            field.Nested = item.ItemType == "form";
            field.Items = new List<FormCompareItemDto>();

            item.CompareResults.ForEach(compareResult =>
            {
                var compareItem = new FormCompareItemDto();
                compareItem.RowIndex = compareResult.Index;
                compareItem.UserInput = compareResult.Input;
                compareItem.Match = compareResult.IfMatch;
                compareItem.Score = compareResult.Score;
                compareItem.Summary = compareResult.Comment;
                compareItem.ReferenceCount = compareResult.ReferencesCount;
                compareItem.References = new List<FormCompareReferenceDto>();

                compareResult.References.ForEach(reference =>
                {
                    var referenceItem = new FormCompareReferenceDto();

                    if (reference.FileName == ethics.FileName)
                    {
                        referenceItem.FileCode = "ethic_committee_approved_file";
                    }
                    else if (reference.FileName == protocol.FileName)
                    {
                        referenceItem.FileCode = "study_protocol";
                    }
                    else if (reference.FileName == consent.FileName)
                    {
                        referenceItem.FileCode = "informed_consent_file";
                    }

                    if (referenceItem.FileCode.IsNullOrEmpty())
                    {
                        if (reference.FileName == "伦理审核批件.pdf")
                        {
                            referenceItem.FileCode = "ethic_committee_approved_file";
                        }
                        else if (reference.FileName == "研究方案.pdf")
                        {
                            referenceItem.FileCode = "study_protocol";
                        }
                        else if (reference.FileName == "知情同意书.pdf")
                        {
                            referenceItem.FileCode = "informed_consent_file";
                        }
                    }

                    referenceItem.ReferenceContent = reference.Text;
                    referenceItem.Areas = new List<FormCompareReferenceAreaDto>();
                    reference.Chunks.ForEach(chunk =>
                    {
                        var area = new FormCompareReferenceAreaDto();
                        area.PageNumber = chunk.PageNum;
                        area.X0 = chunk.Coordinate.X0;
                        area.X1 = chunk.Coordinate.X1;
                        area.Y0 = chunk.Coordinate.Y0;
                        area.Y1 = chunk.Coordinate.Y1;

                        referenceItem.Areas.Add(area);
                    });
                    compareItem.References.Add(referenceItem);
                });
                field.Items.Add(compareItem);
            });
            result.Add(field);
        });

        return await Task.FromResult(result);
    }

    private async Task<List<ParseItemDto>> PrepareFormDtoAsync(FormDefinitionDto formDefinitionDto,
        Func<FieldDto, bool>? predicate = null)
    {
        predicate ??= q => true;

        var result = new List<ParseItemDto>();
        formDefinitionDto.Groups.ForEach(group =>
        {
            group.Fields.Where(q => q.Type == "subForm" || q.Type == "multiSubForm" || predicate(q)).ForEach(field =>
            {
                var itemType = ConvertItemType(field.Type);
                if (itemType != null)
                {
                    var item = new ParseItemDto()
                    {
                        ItemCode = field.Code,
                        ItemName = field.LabelZh,
                        ItemType = itemType,
                    };
                    var contents = new List<ParseItemContentDto>();
                    if (itemType == "form")
                    {
                        var rowDisplayLines = field.GetSubformDisplayValue();
                        field.Value?.ObjectArray?.ForEach((idx, row) =>
                        {
                            var text = rowDisplayLines[idx];

                            var slots = field.Fields
                                .Where(q => predicate(q))
                                .Select(child => new ParseItemContentSlotDto()
                                {
                                    SlotCode = child.Code,
                                    SlotName = child.LabelZh,
                                    Value = child.GetDisplayValue(
                                        FieldDtoExtensions.RefactMultiTypeValue(row[child.Code]))
                                }).ToList();

                            contents.Add(new ParseItemContentDto()
                            {
                                Text = text,
                                Slots = slots,
                                Index = idx
                            });
                        });
                    }
                    else
                    {
                        contents.Add(new ParseItemContentDto()
                        {
                            Text = field.GetDisplayValue(),
                            Slots = null,
                            Index = null
                        });
                    }

                    item.Contents = contents;

                    result.Add(item);
                }
            });
        });
        return await Task.FromResult(result);
    }


    public async Task<AttachmentParseDto?> QueryAttachmentParseCompletedProgressAsync(string taskId)
    {
        var data = await _unicomApiClient.ProgressQueryAsync(taskId);

        var history =
            data.Data.FirstOrDefault(q =>
                    q.ApiName == ApiName.AttachmentParse.GetName() && q.Status == "completed") as
                AttachmentParseApiHistoryDto;

        return history?.History;
    }


    public async Task<ContentCompareDto?> QueryContentCompareCompletedProgressAsync(string taskId)
    {
        var data = await _unicomApiClient.ProgressQueryAsync(taskId);

        var history =
            data.Data.FirstOrDefault(q =>
                    q.ApiName == ApiName.ContentCompare.GetName() && q.Status == "completed") as
                ContentCompareApiHistoryDto;

        return history?.History;
    }

    public async Task<ContentTranslateDto?> QueryContentTranslateCompletedProgressAsync(string requestId)
    {
        var data = await _unicomApiClient.TranslateProgressQueryAsync(requestId);

        var history =
            data.Data.FirstOrDefault(q =>
                    q.ApiName == ApiName.ContentTranslate.GetName() && q.Status == "completed") as
                ContentTranslateApiHistoryDto;

        return history?.History;
    }

    public async Task<string> ContentTranslateAsync(FormDefinitionDto formDefinitionDto)
    {
        var items = await PrepareFormDtoAsync(formDefinitionDto,
            q => q.Type == "textarea_multilang" || q.Type == "text_multilang");

        items = items.Where(q => !q.Contents.FirstOrDefault()?.Text.IsNullOrEmpty() ?? false).ToList();

        var request = new FullContentTranslateRequestDto()
        {
            Data = items
        };

        var result = await _unicomApiClient.FullContentTranslateAsync(request);

        var asyncTaskId =
            await _asyncTaskService.CreateProcessingTaskAsync(result.RequestId, "ContentTranslate",
                JsonSerializer.Serialize(request));

        return result.RequestId;
    }


    public async Task<string> ContentCompareAsync(long userId, string taskId, FormDefinitionDto formDefinitionDto)
    {
        var parseItemDtos = await PrepareFormDtoAsync(formDefinitionDto);

        var result = await _unicomApiClient.ContentCompareAsync(new ContentCompareRequestDto()
        {
            TaskId = taskId,
            Data = parseItemDtos
        });

        var asyncTaskId =
            await _asyncTaskService.CreateTaskAsync(result.TaskId, "ContentCompare", parseItemDtos,
                applyUserId: userId);

        // await _asyncTaskService.ProcessTaskAsync(asyncTaskId);

        return result.TaskId;
    }

    private string? ConvertItemType(string fieldType)
    {
        switch (fieldType)
        {
            case "multiSubForm":
                return "form";
            case "subForm":
                return "form";
            case "textarea_multilang":
                return "text";
            case "text_multilang":
                return "text";
            case "input":
                return "text";
            case "textarea":
                return "text";
            case "date":
                return "text";
            default:
                return null;
        }
    }


    public async Task<bool> ExtractAsync(string taskId)
    {
        var preCheckAsyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "PreCheck");
        if (preCheckAsyncTask == null)
        {
            return false;
        }

        var taskData = JsonSerializer.Deserialize<FormRecognitionContext>(preCheckAsyncTask.TaskData);

        await _asyncTaskService.CreateProcessingTaskAsync(taskId, "Extract", taskData!);

        await PreFillAsync(taskId);

        return true;
    }
}

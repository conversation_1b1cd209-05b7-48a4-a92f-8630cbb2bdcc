using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.QueryCriteria;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ProjectService
{
    public async Task<int> GetCountAsync(ProjectStatisticsEnum category)
    {
        return await GetCountAsync(category, _currentUserContext.GetCurrentUser()!.Key);
    }

    public async Task<int> GetCountAsync(ProjectStatisticsEnum category, long userId)
    {
        var define = ProjectStatisticsDefineExtensions.Map[category];

        var baseQuery = define.FormDataQueries;

        var query = baseQuery.Select(q => new FormDataDynamicQuery()
        {
            Key = q.Key,
            Operator = q.Operator,
            Value = q.Value
        }).ToList();


        if (define.Mgt)
        {
            if (!string.IsNullOrEmpty(define.CurrentUserIdFieldName))
            {
                query.Add(new FormDataDynamicQuery()
                {
                    Key = define.CurrentUserIdFieldName,
                    Operator = FormDataQueryOperator.Equal,
                    Value = userId.ToString()
                });
            }

            if (define.UsingNewest)
            {
                return await _dynamicFormMgtApiClient.GetNewestCountAsync(FormCode, define.Status, query);
            }

            return await _dynamicFormMgtApiClient.GetCountAsync(FormCode, define.Status, query);
        }
        else
        {
            if (define.UsingNewest)
            {
                return await _dynamicFormApiClient.GetNewestCountAsync(FormCode, define.Status, define.FormDataQueries);
            }

            return await _dynamicFormApiClient.GetCountAsync(FormCode, define.Status, define.FormDataQueries);
        }
    }

    public async Task<FormInstancePageDto> GetPageAsync(ProjectStatisticsEnum category,
        PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria)
    {
        return await GetPageAsync(category, criteria, _currentUserContext.GetCurrentUser()!.Key);
    }

    public async Task<FormInstancePageDto> GetPageAsync(ProjectStatisticsEnum category,
        PagedQueryCriteria<InnerFormInstanceQueryCriteria> criteria, long userId)
    {
        var define = ProjectStatisticsDefineExtensions.Map[category];

        var baseQuery = define.FormDataQueries;

        var query = baseQuery.Select(q => new FormDataDynamicQuery()
        {
            Key = q.Key,
            Operator = q.Operator,
            Value = q.Value
        }).ToList();

        if (criteria.Condition.FormDataDynamicQueries.Any())
        {
            query.AddRange(criteria.Condition.FormDataDynamicQueries);
        }

        var queryCriteria = new PagedQueryCriteria<FormInstanceQueryCriteria>
        {
            OrderBy = criteria.OrderBy,
            Condition = new FormInstanceQueryCriteria()
            {
                FormDataDynamicQueries = query,
                Columns = criteria.Condition.Columns,
                FormCode = FormCode,
                Status = define.Status,
                IsObsoleted = false,
                DynamicQueries = criteria.Condition.DynamicQueries
            },
            PageParams = criteria.PageParams
        };


        if (define.Mgt)
        {
            if (!string.IsNullOrEmpty(define.CurrentUserIdFieldName))
            {
                queryCriteria.Condition.FormDataDynamicQueries.Add(new FormDataDynamicQuery()
                {
                    Key = define.CurrentUserIdFieldName,
                    Operator = FormDataQueryOperator.Equal,
                    Value = userId.ToString()
                });
            }

            if (define.UsingNewest)
            {
                return await _dynamicFormMgtApiClient.GetNewestPageAsync(queryCriteria);
            }

            return await _dynamicFormMgtApiClient.GetPageAsync(queryCriteria);
        }
        else
        {
            if (define.UsingNewest)
            {
                return await _dynamicFormApiClient.GetNewestPageAsync(queryCriteria);
            }

            return await _dynamicFormApiClient.GetPageAsync(queryCriteria);
        }
    }

    private async Task<DashboardStatisticsDto> GetDashboardStatisticsItemAsync(ProjectStatisticsEnum category)
    {
        return new DashboardStatisticsDto
        {
            Define = ProjectStatisticsDefineExtensions.Map[category],
            Count = await GetCountAsync(category)
        };
    }

    public async Task<List<DashboardStatisticsDto>> GetDashboardAsync()
    {
        var userPositions = (await _userApiClient.GetUserPositionsAsync()).Select(q => q.Code).ToList();

        var userRoles = (await _userApiClient.GetUserRolesAsync()).ToList();

        var data = new List<DashboardStatisticsDto>();

        if (userRoles.Exists(q => q.ToUpper() == "RESEARCHER"))
        {
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.UserAll));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.UserPendingSubmit));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.UserPendingApproval));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.UserApproved));
        }

        if (userPositions.Exists(q => q == "CHECKER_LEVEL_1"))
        {
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1PendingJudge));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1PendingSendNumber));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1ApplyEditPendingConfirmation));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1ApplyEditPendingApproval));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1ApplyEditReturned));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1SentNumber));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1NonTraditional));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level1AllSubmitted));
        }

        if (userPositions.Exists(q => q == "CHECKER_LEVEL_2"))
        {
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2PendingAssign));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2PendingReview));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2ReviewReturned));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2PendingApproval));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2Approved));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2ReAssign));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level2AllSubmitted));
        }

        if (userPositions.Exists(q => q == "CHECKER_LEVEL_3"))
        {
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3PendingAssignReview));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3PendingReview));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3PendingApproval));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3ReviewReturned));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3Approved));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3ReAssign));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level3AllSubmitted));
        }

        if (userPositions.Exists(q => q == "CHECKER_LEVEL_4"))
        {
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level4PendingReview));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level4PendingApproval));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level4ReviewReturned));
            data.Add(await GetDashboardStatisticsItemAsync(ProjectStatisticsEnum.Level4Approved));
        }

        return data;
    }
}

using AutoMapper;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Services;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Domain.UOW;

namespace XJ.Framework.Itmctr.Application.Services;

public class AsyncTaskService :
    BaseEditableAppService<long, AsyncTaskEntity, AsyncTaskDto, AsyncTaskOperationDto,
        IAsyncTaskRepository, AsyncTaskQueryCriteria>,
    IAsyncTaskService
{
    private readonly ICurrentUserContext _currentUserContext;

    public AsyncTaskService(IAsyncTaskRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork,
        keyGenerator, currentUserContext)
    {
        _currentUserContext = currentUserContext;
    }

    public async Task<long> CreateTaskAsync(string businessId, string taskCode, object taskData,
        long? applyUserId = null)
    {
        var asyncTask = new AsyncTaskOperationDto()
        {
            TaskCode = taskCode,
            BusinessId = businessId,
            TaskData = taskData.ToJson(),
            TaskStatus = Domain.Shared.Enums.AsyncTaskStatus.Pending,
            ApplyUserId = applyUserId ?? _currentUserContext.GetCurrentUserId() ?? 0
        };
        var key = KeyGenerator.GenerateKey();
        await CreateAsync(key, asyncTask);
        return key;
    }

    public async Task<long> CreateTaskAsync(string businessId, string taskCode, object taskData, object? taskResult,
        AsyncTaskStatus? taskStatus = null, long? applyUserId = null)
    {
        var asyncTask = new AsyncTaskOperationDto()
        {
            TaskCode = taskCode,
            BusinessId = businessId,
            TaskData = taskData.ToJson(),
            TaskResult = taskResult.ToJson(),
            TaskStatus = taskStatus ?? Domain.Shared.Enums.AsyncTaskStatus.Pending,
            ApplyUserId = applyUserId ?? _currentUserContext.GetCurrentUserId() ?? 0
        };
        var key = KeyGenerator.GenerateKey();
        await CreateAsync(key, asyncTask);
        return key;
    }

    public async Task<long> CreateProcessingTaskAsync(string businessId, string taskCode, object taskData)
    {
        var asyncTask = new AsyncTaskOperationDto()
        {
            TaskCode = taskCode,
            BusinessId = businessId,
            TaskData = taskData.ToJson(),
            TaskStatus = Domain.Shared.Enums.AsyncTaskStatus.Processing,
            ApplyUserId = _currentUserContext.GetCurrentUserId()!.Value
        };
        var key = KeyGenerator.GenerateKey();
        await CreateAsync(key, asyncTask);
        return key;
    }

    public async Task<bool> ProcessTaskAsync(long id)
    {
        var asyncTask = await Repository.GetAsync(id);
        if (asyncTask == null)
        {
            return false;
        }

        asyncTask.TaskStatus = Domain.Shared.Enums.AsyncTaskStatus.Processing;
        await Repository.UpdateAsync(asyncTask);
        return true;
    }

    public async Task<bool> ProcessTaskAsync(string businessId, string taskCode)
    {
        var taskDto = await GetTaskByBusinessIdAsync(businessId, taskCode);
        if (taskDto == null)
            return false;
        return await ProcessTaskAsync(taskDto.Key);
    }

    public async Task<bool> CompletedTaskByBusinessIdAsync(string businessId, string taskCode, object taskResult)
    {
        var taskDto = await GetTaskByBusinessIdAsync(businessId, taskCode);
        if (taskDto == null)
            return false;
        return await CompletedTaskAsync(taskDto.Key, taskResult);
    }

    public async Task<bool> CompletedTaskAsync(long id, object taskResult)
    {
        var asyncTask = await Repository.GetAsync(id);
        if (asyncTask == null)
        {
            return false;
        }

        asyncTask.TaskStatus = Domain.Shared.Enums.AsyncTaskStatus.Completed;
        asyncTask.TaskResult = taskResult.ToJson();
        await Repository.UpdateAsync(asyncTask);
        return true;
    }

    public async Task<AsyncTaskDto?> GetTaskByBusinessIdAsync(string businessId, string taskCode)
    {
        var entity = await Repository.GetAsync(q =>
            q.BusinessId.ToLower() == businessId.ToLower() && q.TaskCode.ToLower().Equals(taskCode.ToLower()));
        return await GetDtoAsync(entity);
    }


    public async Task<AsyncTaskDto?> GetLastPendingTaskByBusinessIdAsync(string businessId, string taskCode)
    {
        var entities = await Repository.GetListAsync(q =>
            q.BusinessId.ToLower() == businessId.ToLower() && q.TaskCode.ToLower().Equals(taskCode.ToLower()) &&
            q.TaskStatus == Domain.Shared.Enums.AsyncTaskStatus.Pending);
        var entity = entities.OrderByDescending(q => q.CreatedTime).FirstOrDefault();
        return await GetDtoAsync(entity);
    }
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Options;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.Extensions;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Rbac.ApiClient;

namespace XJ.Framework.Itmctr.Application.Services;

public partial class ProjectService : IProjectService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly DynamicFormApiClient _dynamicFormApiClient;
    private readonly DynamicFormMgtApiClient _dynamicFormMgtApiClient;
    private readonly DynamicFormMgtApplicationApiClient _dynamicFormMgtApplicationApiClient;
    private readonly DynamicFormApplicationApiClient _dynamicFormApplicationApiClient;
    private const string FormCode = "PROJECT";
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IFormRecognitionService _formRecognitionService;
    private readonly IMapper _mapper;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly UserMgtApiClient _userMgtApiClient;
    private readonly JsonSerializerOptions _jsonSerializerOptions;

    private readonly IOptions<UnicomOption> _unicomOption;
    private readonly UserApiClient _userApiClient;
    private readonly IProjectRepository _projectRepository;
    private readonly IProjectAttachRepository _projectAttachRepository;
    private readonly IProjectAttachHistoryRepository _projectAttachHistoryRepository;
    private readonly IProjectSponsorRepository _projectSponsorRepository;
    private readonly IProjectSponsorHistoryRepository _projectSponsorHistoryRepository;
    private readonly IProjectResearchSiteRepository _projectResearchSiteRepository;
    private readonly IProjectResearchSiteHistoryRepository _projectResearchSiteHistoryRepository;
    private readonly IProjectInterventionRepository _projectInterventionRepository;
    private readonly IProjectInterventionHistoryRepository _projectInterventionHistoryRepository;
    private readonly IProjectMeasurementRepository _projectMeasurementRepository;
    private readonly IProjectMeasurementHistoryRepository _projectMeasurementHistoryRepository;
    private readonly IProjectHumanSampleRepository _projectHumanSampleRepository;
    private readonly IProjectHumanSampleHistoryRepository _projectHumanSampleHistoryRepository;
    private readonly IProjectHistoryRepository _projectHistoryRepository;

    private readonly IUnitOfWork _unitOfWork;
    private readonly ILogger<ProjectService> _logger;

    public ProjectService(DynamicFormApiClient dynamicFormApiClient, DynamicFormMgtApiClient dynamicFormMgtApiClient,
        IAsyncTaskService asyncTaskService, IFormRecognitionService formRecognitionService, IMapper mapper,
        IKeyGenerator<long> keyGenerator,
        ICurrentUserContext currentUserContext, UserMgtApiClient userMgtApiClient, IOptions<JsonOptions> jsonOptions,
        IOptions<UnicomOption> unicomOption, UserApiClient userApiClient, IProjectRepository projectRepository,
        IProjectAttachRepository projectAttachRepository, IProjectSponsorRepository projectSponsorRepository,
        IProjectResearchSiteRepository projectResearchSiteRepository,
        IProjectInterventionRepository projectInterventionRepository,
        IProjectMeasurementRepository projectMeasurementRepository,
        IProjectHumanSampleRepository projectHumanSampleRepository,
        IUnitOfWork unitOfWork, IProjectHistoryRepository projectHistoryRepository,
        IProjectAttachHistoryRepository projectAttachHistoryRepository,
        IProjectSponsorHistoryRepository projectSponsorHistoryRepository,
        IProjectResearchSiteHistoryRepository projectResearchSiteHistoryRepository,
        IProjectInterventionHistoryRepository projectInterventionHistoryRepository,
        IProjectMeasurementHistoryRepository projectMeasurementHistoryRepository,
        IProjectHumanSampleHistoryRepository projectHumanSampleHistoryRepository, IServiceProvider serviceProvider,
        ILogger<ProjectService> logger, DynamicFormMgtApplicationApiClient dynamicFormMgtApplicationApiClient,
        DynamicFormApplicationApiClient dynamicFormApplicationApiClient)
    {
        _dynamicFormApiClient = dynamicFormApiClient;
        _dynamicFormMgtApiClient = dynamicFormMgtApiClient;
        _asyncTaskService = asyncTaskService;
        _formRecognitionService = formRecognitionService;
        _mapper = mapper;
        _currentUserContext = currentUserContext;
        _userMgtApiClient = userMgtApiClient;
        _unicomOption = unicomOption;
        _userApiClient = userApiClient;
        this._jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
        _projectRepository = projectRepository;
        _projectAttachRepository = projectAttachRepository;
        _projectSponsorRepository = projectSponsorRepository;
        _projectResearchSiteRepository = projectResearchSiteRepository;
        _projectInterventionRepository = projectInterventionRepository;
        _projectMeasurementRepository = projectMeasurementRepository;
        _projectHumanSampleRepository = projectHumanSampleRepository;
        _projectHistoryRepository = projectHistoryRepository;
        _projectAttachHistoryRepository = projectAttachHistoryRepository;
        _projectSponsorHistoryRepository = projectSponsorHistoryRepository;
        _projectResearchSiteHistoryRepository = projectResearchSiteHistoryRepository;
        _projectInterventionHistoryRepository = projectInterventionHistoryRepository;
        _projectMeasurementHistoryRepository = projectMeasurementHistoryRepository;
        _projectHumanSampleHistoryRepository = projectHumanSampleHistoryRepository;
        _serviceProvider = serviceProvider;
        _logger = logger;
        _dynamicFormMgtApplicationApiClient = dynamicFormMgtApplicationApiClient;
        _dynamicFormApplicationApiClient = dynamicFormApplicationApiClient;
        _unitOfWork = unitOfWork;
    }


    public async Task<bool> SubmitToContentCompareAsync(long userId, string businessId, string version)
    {
        // var formInstanceDto = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId, version);

        //调用获取表单定义
        var formDefinitionDto =
            await _dynamicFormApplicationApiClient.GetDifferenceFormInstanceAsync(businessId, version);

        //获取上一版本差异值
        var previousValues = await FormDefinitionHelper.GetFieldPreviousValuesAsync(formDefinitionDto);

        //获取当前值
        var values = await FormDefinitionHelper.GetFieldValuesAsync(formDefinitionDto);

        //获取上一版本的关键文件
        var previousFormRecognitionContext = await GetCriticalFilesAsync(previousValues);

        //获取当前版本的关键文件
        var formRecognitionContext = await GetCriticalFilesAsync(values);

        //获取当前表单中保存的TaskId

        var taskId = formDefinitionDto.FormData.TryGetValue("TaskId", out var value) ? value : null;

        if (_unicomOption.Value.Enable && formRecognitionContext is
                                           { Ethics: not null, Consent: not null, Protocol: not null }
                                       && await CheckCriticalFilesAllowExtensionsAsync(formRecognitionContext)
           )
        {
            // 如果之前没做过预检查，或者有上一版本并且关键文件有变更，则需要重新做预检查
            if (taskId.IsNullOrEmpty() ||
                (!formDefinitionDto.PreviousVersion.IsNullOrEmpty()
                 &&
                 await CheckCriticalFilesChangeAsync(previousFormRecognitionContext, formRecognitionContext)))
            {
                //调用同步预检查
                var preCheckResult =
                    await _formRecognitionService.PreCheckAsync(userId, formRecognitionContext);

                taskId = preCheckResult.TaskId;

                //重新赋值TaskId
                formDefinitionDto.FormData["TaskId"] = taskId;

                await _dynamicFormMgtApplicationApiClient.SetFormDataAsync(businessId, version,
                    new Dictionary<string, string?>()
                    {
                        {
                            "TaskId", taskId
                        }
                    });
            }
        }


        if (_unicomOption.Value.Enable)
        {
            //发起内容对比服务
            await _formRecognitionService.ContentCompareAsync(userId, taskId!, formDefinitionDto);
        }

        return true;
    }


    public async Task<string?> FixFormRecognitionAsync(string businessId)
    {
        //调用获取表单定义
        var formDefinitionDto = await _dynamicFormApiClient.GetDifferenceFormInstanceAsync(businessId);

        //获取当前值
        var values = await FormDefinitionHelper.GetFieldValuesAsync(formDefinitionDto);

        //获取当前版本的关键文件
        var formRecognitionContext = await GetCriticalFilesAsync(values);

        if (!_unicomOption.Value.Enable)
        {
            throw new ValidationException("相关功能未启用");
        }

        if (formRecognitionContext is
            not { Ethics: not null, Consent: not null, Protocol: not null })
        {
            throw new ValidationException("伦理批件、知情同意书、研究方案三者必须均具备方可使用");
        }

        if (!await CheckCriticalFilesAllowExtensionsAsync(formRecognitionContext))
        {
            throw new ValidationException("只支持PDF格式的附件进行抽取和比较");
        }

        //调用同步预检查
        var result =
            await _formRecognitionService.PreCheckAsync(_currentUserContext.GetCurrentUserId()!.Value,
                formRecognitionContext);

        formDefinitionDto.FormData["TaskId"] = result.TaskId;

        await _dynamicFormApiClient.SetFormDataAsync(businessId, formDefinitionDto.FormData);

        //发起内容对比服务
        return await _formRecognitionService.ContentCompareAsync(_currentUserContext.GetCurrentUserId()!.Value,
            result.TaskId, formDefinitionDto);
    }

    private async Task<FormDefinitionDto> FilterFormDefinitionAsync(FormDefinitionDto formDefinition)
    {
        formDefinition.Groups.ForEach(group =>
        {
            //过滤掉注册号字段 如果只取不是注册号或者是注册号但值不为空的
            group.Fields = group.Fields.Where(field =>
                field.Code != "registration_number" || (field.Code == "registration_number" &&
                                                        !(field.Value?.StringValue?.IsNullOrEmpty() ?? true))).ToList();
            var registrationNumberField = group.Fields.FirstOrDefault(q => q.Code == "registration_number");
            if (registrationNumberField != null)
            {
                group.Fields.FirstOrDefault(q => q.Code == "registration_number")!.InitReadonly = true;
            }
        });
        return await Task.FromResult(formDefinition);
    }

    public async Task<FormDefinitionDto> GetFormDefinitionAsync()
    {
        var formDefinitionDto = await _dynamicFormApiClient.GetDefinitionAsync(FormCode);
        return await FilterFormDefinitionAsync(formDefinitionDto);
    }


    public async Task<string> SaveAsync(string businessId, FormDefinitionDto formDefinitionDto)
    {
        var originalFormDefinition = await _dynamicFormApiClient.GetNewestFormInstanceAsync(businessId);


        //如果已经审批通过
        if (originalFormDefinition.FormData.TryGetValue("RegistrationNumber", out var registrationNumber)
            && !string.IsNullOrEmpty(registrationNumber)
           )
        {
            if (!originalFormDefinition.FormData.TryGetValue("EditProcessStatus", out var editProcessStatus) ||
                editProcessStatus != nameof(ProjectEditProcessStatusEnum.Approved))
            {
                throw new ValidationException(
                    "您还未发起再修改申请或申请未通过/You have not initiated a modification request or the request has not been approved");
            }
        }

        originalFormDefinition.FormData.TryGetValue("ProjectTag", out var projectTag);
        originalFormDefinition.FormData.TryGetValue("ProcessStatus", out var processStatus);

        if ((projectTag ?? string.Empty) is nameof(ProjectTagEnum.Termination) or nameof(ProjectTagEnum.Hide))
        {
            throw new ValidationException("该项目已被终止/This project has been terminated");
        }

        if ((processStatus ?? string.Empty) != string.Empty &&
            (processStatus ?? string.Empty) != nameof(ProjectProcessStatusEnum.RejectToApply) &&
            (processStatus ?? string.Empty) != nameof(ProjectProcessStatusEnum.Approved))
        {
            throw new ValidationException("不允许修改/Modification is not allowed");
        }


        formDefinitionDto.FormData = originalFormDefinition.FormData;

        await _dynamicFormApiClient.SaveInstanceAsync(businessId, formDefinitionDto);

        return businessId;
    }

    public async Task<bool> MaintenanceSaveAsync(string businessId, string version,
        FormDefinitionDto formDefinitionDto)
    {
        var originalFormDefinition = await _dynamicFormApiClient.GetFormInstanceAsync(businessId, version);

        originalFormDefinition.FormData = formDefinitionDto.FormData;

        var dataValue = await FormDefinitionHelper.GetFieldValuesAsync(formDefinitionDto);

        await FormDefinitionHelper.SetFieldValueAsync(originalFormDefinition, dataValue);

        if (formDefinitionDto.Language == "both")
        {
            originalFormDefinition.FormData["PublicTitle"] =
                formDefinitionDto.GetFieldValue("public_title")?.KeyValue?["zh"]?.ToString();
        }

        originalFormDefinition.FormData["PublicTitleEN"] =
            formDefinitionDto.GetFieldValue("public_title")?.KeyValue?["en"]?.ToString();


        originalFormDefinition.FormData["RegistrationNumber"] =
            formDefinitionDto.GetFieldValue("registration_number")?.StringValue ?? string.Empty;

        await _dynamicFormMgtApiClient.SaveInstanceAsync(businessId, version, originalFormDefinition);


        //根据是否已经有了注册号以及审批状态是否为已审批完成决定是否保存到结果数据
        if (!string.IsNullOrEmpty(originalFormDefinition.FormData["RegistrationNumber"]) &&
            originalFormDefinition.FormData.TryGetValue("ProcessStatus", out var processStatus) &&
            processStatus == "Approved")
        {
            await SaveToResultDataAsync(formDefinitionDto.Code, businessId, version, formDefinitionDto.Language!,
                dataValue, originalFormDefinition.FormData);
        }

        return true;
    }

    private async Task<FormDefinitionDto> GetAsync(FormInstanceDto formInstanceDto,
        FormDefinitionDto formDefinitionDto, bool validateUser = true, bool filterFormDefinition = true)
    {
        if (validateUser)
        {
            await ValidateUserAsync(formInstanceDto, formDefinitionDto);
        }

        await FilterFormDataAsync(formInstanceDto, formDefinitionDto);

        return filterFormDefinition ? await FilterFormDefinitionAsync(formDefinitionDto) : formDefinitionDto;
    }

    public async Task<FormDefinitionDto> GetAsync(string businessId, string version, bool validateUser = true,
        bool filterFormDefinition = true)
    {
        var formInstanceDto = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId, version);
        var formDefinitionDto = await _dynamicFormApiClient.GetFormInstanceAsync(businessId, version);
        return await GetAsync(formInstanceDto, formDefinitionDto, validateUser, filterFormDefinition);
    }

    public async Task<FormDefinitionDto> GetAsync(string businessId, bool validateUser = true,
        bool filterFormDefinition = true)
    {
        var formInstanceDto = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId);
        var formDefinitionDto = await _dynamicFormApiClient.GetNewestFormInstanceAsync(businessId);
        return await GetAsync(formInstanceDto, formDefinitionDto, validateUser, filterFormDefinition);
    }

    public async Task<FormDefinitionDto> GetWithPreviousCompareAsync(string businessId, bool validateUser = true,
        bool filterFormDefinition = true)
    {
        var formInstanceDto = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId);
        var formDefinitionDto = await _dynamicFormApiClient.GetDifferenceFormInstanceAsync(businessId);

        if (validateUser)
        {
            await ValidateUserAsync(formInstanceDto, formDefinitionDto);
        }

        await FilterFormDataAsync(formInstanceDto, formDefinitionDto);

        return filterFormDefinition ? await FilterFormDefinitionAsync(formDefinitionDto) : formDefinitionDto;
    }

    private async Task FilterFormDataAsync(FormInstanceDto formInstance, FormDefinitionDto formDefinitionDto)
    {
        formDefinitionDto.FormData.TryGetValue("ApprovalHistory", out var approvalHistoryString);

        var approvalHistory = approvalHistoryString ?? "[]";

        var approvalHistoryList = JsonSerializer.Deserialize<List<ApprovalHistoryDto>>(approvalHistory!);

        var userPositions = await _userApiClient.GetUserPositionsAsync();

        var skipThirdAssignment =
            formDefinitionDto.FormData.TryGetValue("SkipThirdAssignment", out var skipThirdAssignmentString)
            && skipThirdAssignmentString == "True";

        var hasLevel4Position = userPositions.Any(q => q.Code == "CHECKER_LEVEL_4");
        var hasLevel1Position = userPositions.Any(q => q.Code == "CHECKER_LEVEL_1");

        var minLevel = !userPositions.Any()
            ? 4
            : userPositions.Select(q =>
            {
                switch (q.Code)
                {
                    case "CHECKER_LEVEL_1":
                        return 0;
                    case "CHECKER_LEVEL_2":
                        return 1;
                    case "CHECKER_LEVEL_3":
                        return 2;
                    case "CHECKER_LEVEL_4":
                        return 3;
                    default:
                        return 4;
                }
            }).Min();

        if (_currentUserContext.GetCurrentUserId()!.Value == 1L)
        {
            minLevel = 0;
        }

        //如果跳过了三级审核 并且当前人最高级别是四级 则临时提升级别
        if (skipThirdAssignment && minLevel == 3)
        {
            minLevel = 2;
        }


        var filteredApprovalHistory = approvalHistoryList!.Where(h =>
        {
            var level = 0;
            switch (h.NodeType)
            {
                case "User":
                    level = 5;
                    break;
                case "FirstApproval":
                    level = 1;
                    break;
                case "SecondApproval":
                    level = 2;
                    break;
                case "ThirdApproval":
                    level = 3;
                    break;
                case "FourthApproval":
                    level = 4;
                    break;
            }

            var downLevelActions = new string[] { "Recall", "Return" };

            if (downLevelActions.Contains(h.Action))
            {
                level = level - 1;
            }

            var result = minLevel <= level;
            if (result)
            {
                return result;
            }

            if ((h.NodeType is "OldFirstApproval" or "OldSecondApproval") &&
                (hasLevel1Position || hasLevel4Position))
            {
                result = true;
            }

            if ((h.NodeType is "OldSecondApproval"))
            {
                result = true;
            }

            if (h is { NodeType: "FirstApproval", Action: "Reject" })
            {
                result = true;
            }

            return result;
        }).ToList();

        formDefinitionDto.FormData["ApprovalHistory"] = JsonSerializer.Serialize(filteredApprovalHistory);

        await Task.CompletedTask;
    }

    private async Task ValidateUserAsync(FormInstanceDto formInstance, FormDefinitionDto formDefinitionDto)
    {
        //提交人 一审 二审 三审 四审 系统管理员
        var applyUserId = formInstance.ApplyUserId.ToString();


        formDefinitionDto.FormData.TryGetValue("FirstApprovalUserId", out var firstApprovalUserId);
        formDefinitionDto.FormData.TryGetValue("SecondApprovalUserId", out var secondApprovalUserId);
        formDefinitionDto.FormData.TryGetValue("ThirdApprovalUserId", out var thirdApprovalUserId);
        formDefinitionDto.FormData.TryGetValue("FourthApprovalUserId", out var fourthApprovalUserId);

        var tempUsers =
                new List<string?>()
                {
                    applyUserId,
                    firstApprovalUserId,
                    secondApprovalUserId,
                    thirdApprovalUserId,
                    fourthApprovalUserId,
                    1L.ToString() // 系统管理员
                }
            ;

        var userPositions = await _userApiClient.GetUserPositionsAsync();
        if (userPositions.Exists(q => q.Code == "CHECKER_LEVEL_1"))
        {
            tempUsers.Add(_currentUserContext.GetCurrentUserId()!.Value.ToString());
        }

        var allowUsers = tempUsers.Where(q => !string.IsNullOrEmpty(q))
            .Select(q => Convert.ToInt64(q))
            .Distinct()
            .ToList();

        if (!allowUsers.Contains(_currentUserContext.GetCurrentUserId()!.Value))
        {
            throw new ValidationException("您没有权限查看该表单/You do not have permission to view this form");
        }

        await Task.CompletedTask;
    }


    public async Task<bool> SubmitAsync(string businessId)
    {
        var formInstanceDto = await _dynamicFormApiClient.GetFormInstanceDtoAsync(businessId);
        if (formInstanceDto.Status != FormInstanceStatus.Draft ||
            formInstanceDto.ApplyUserId != _currentUserContext.GetCurrentUserId())
        {
            throw new ValidationException("表单状态不正确/Form status is incorrect");
        }


        var result = await _dynamicFormApiClient.CheckFormInstanceAsync(businessId);
        if (result == null || result.Any())
        {
            throw new ValidationException(string.Join("\n", result!));
        }

        //调用获取表单定义
        var formDefinitionDto = await _dynamicFormApiClient.GetDifferenceFormInstanceAsync(businessId);

        formDefinitionDto.FormData.TryGetValue("ProjectTag", out var projectTag);

        if ((projectTag ?? string.Empty) is nameof(ProjectTagEnum.Termination) or nameof(ProjectTagEnum.Hide))
        {
            throw new ValidationException("该项目已被终止/This project has been terminated");
        }


        //如果已经审批通过
        if (formDefinitionDto.FormData.TryGetValue("RegistrationNumber", out var registrationNumber)
            && !string.IsNullOrEmpty(registrationNumber)
           )
        {
            if (!formDefinitionDto.FormData.TryGetValue("EditProcessStatus", out var editProcessStatus) ||
                editProcessStatus != nameof(ProjectEditProcessStatusEnum.Approved))
            {
                throw new ValidationException(
                    "您还未发起再修改申请或申请未通过/You have not initiated a modification request or the request has not been approved");
            }
        }


        var publicTitle = formDefinitionDto.GetFieldValue("public_title")?.KeyValue?["zh"]?.ToString() ?? string.Empty;
        var publicTitleEn = formDefinitionDto.GetFieldValue("public_title")?.KeyValue?["en"]?.ToString() ??
                            string.Empty;

        if (publicTitle.IsNullOrEmpty() || publicTitleEn.IsNullOrEmpty())
        {
            throw new ValidationException("注册题目不能为空/Public title is required");
        }

        // var existPublicTitle =
        //     await _dynamicFormApiClient.ExistFormDataValueAsync(FormCode, businessId, "PublicTitle", publicTitle);
        //
        // var existPublicTitleEn =
        //     await _dynamicFormApiClient.ExistFormDataValueAsync(FormCode, businessId, "PublicTitleEN", publicTitle);
        //
        // if (existPublicTitle || existPublicTitleEn)
        // {
        //     throw new ValidationException("注册题目已存在，请重新输入/Public title already exists, please re-enter.");
        // }

        //获取批注
        var annotationValues = await FormDefinitionHelper.GetAnnotationsAsync(formDefinitionDto);

        formDefinitionDto.FormData["PublicTitle"] = publicTitle;
        formDefinitionDto.FormData["PublicTitleEN"] = publicTitleEn;

        //更新提交时间
        formDefinitionDto.FormData["SubmitTime"] =
            DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();

        //如果没有首次提交时间 则赋值
        if (!formDefinitionDto.FormData.ContainsKey("FirstSubmitTime"))
        {
            formDefinitionDto.FormData["FirstSubmitTime"] = formDefinitionDto.FormData["SubmitTime"];
        }

        formDefinitionDto.FormData.TryGetValue("ProcessStatus", out var processStatus);
        //先重置流程状态
        formDefinitionDto.FormData["ProcessStatus"] = string.Empty;
        formDefinitionDto.FormData["FourthRejectReason"] = string.Empty;

        //如果原来有审批状态并且状态时驳回到发起人 则直接修改为等待四级审核
        if (!string.IsNullOrEmpty(processStatus))
        {
            //说明是再修改
            if (!string.IsNullOrEmpty(registrationNumber))
            {
                formDefinitionDto.FormData["ProcessStatus"] = nameof(ProjectProcessStatusEnum.PendingFourthApproval);
            }
            else if (processStatus == "RejectToApply")
            {
                //如何项目性质没确定 则需要继续判断
                if (string.IsNullOrEmpty(projectTag))
                {
                    formDefinitionDto.FormData["ProcessStatus"] = string.Empty;
                }
                else
                {
                    //说明是驳回的
                    formDefinitionDto.FormData["ProcessStatus"] =
                        nameof(ProjectProcessStatusEnum.PendingFourthApproval);
                }
            }
        }

        if (!formDefinitionDto.FormData.ContainsKey("ProjectTag"))
        {
            formDefinitionDto.FormData["ProjectTag"] = string.Empty;
        }

        if (!formDefinitionDto.FormData.ContainsKey("RegistrationNumber"))
        {
            formDefinitionDto.FormData["RegistrationNumber"] = string.Empty;
        }

        //移除指定的批注值（翻译和填充）
        await FormDefinitionHelper.RemoveNamedAnnotationsAsync(annotationValues, "fill", "translate");

        //调用保存批注
        await _dynamicFormApiClient.SaveAnnotationAsync(businessId, annotationValues);

        //保存表单数据
        AddApprovalHistory("User", "Submit", string.Empty, formDefinitionDto.FormData);

        await _dynamicFormApiClient.SetFormDataAsync(businessId, formDefinitionDto.FormData);

        //调用提交
        await _dynamicFormApiClient.SubmitInstanceAsync(businessId);

        await _asyncTaskService.CreateTaskAsync(businessId, "SubmitToContentCompare", new Dictionary<string, object?>()
        {
            {
                "BusinessId", businessId
            },
            {
                "Version", formDefinitionDto.Version
            }
        });

        return true;
    }

    private async Task<bool> CheckCriticalFilesAllowExtensionsAsync(FormRecognitionContext formRecognitionContext)
    {
        return await Task.FromResult(Path.GetExtension(formRecognitionContext.Ethics!.FileName)
                                         .Equals(".pdf", StringComparison.CurrentCultureIgnoreCase) &&
                                     Path.GetExtension(formRecognitionContext.Consent!.FileName)
                                         .Equals(".pdf", StringComparison.CurrentCultureIgnoreCase) &&
                                     Path.GetExtension(formRecognitionContext.Protocol!.FileName)
                                         .Equals(".pdf", StringComparison.CurrentCultureIgnoreCase));
    }

    private async Task<bool> CheckCriticalFilesChangeAsync(FormRecognitionContext previousFormRecognitionContext,
        FormRecognitionContext formRecognitionContext)
    {
        var ethicsChanged = previousFormRecognitionContext.Ethics?.FileId != formRecognitionContext.Ethics?.FileId;
        var protocolChanged =
            previousFormRecognitionContext.Protocol?.FileId != formRecognitionContext.Protocol?.FileId;
        var consentChanged =
            previousFormRecognitionContext.Consent?.FileId != formRecognitionContext.Consent?.FileId;

        return await Task.FromResult(ethicsChanged || protocolChanged || consentChanged);
    }

    private async Task<FormRecognitionContext> GetCriticalFilesAsync(Dictionary<string, MultiTypeValue?>? dataValue)
    {
        var ethicsValue = dataValue?.TryGetValue("ethic_committee_approved_file", out var ethicsKeyValue) is true
            ? ethicsKeyValue?.KeyValue
            : null;

        var protocolValue = dataValue?.TryGetValue("study_protocol", out var protocolKeyValue) is true
            ? protocolKeyValue?.KeyValue
            : null;

        var consentValue = dataValue?.TryGetValue("informed_consent_file", out var consentKeyValue) is true
            ? consentKeyValue?.KeyValue
            : null;

        var ethics = ethicsValue != null
                     && ethicsValue.TryGetValue("fileId", out var ethicsFileId)
                     && ethicsValue.TryGetValue("fileName", out var ethicsFileName)
            ? new FileDto()
            {
                FileId = ethicsFileId!.ToString()!,
                FileName = ethicsFileName!.ToString()!,
            }
            : null;
        var protocol = protocolValue != null
                       && protocolValue.TryGetValue("fileId", out var protocolFileId)
                       && protocolValue.TryGetValue("fileName", out var protocolFileName)
            ? new FileDto()
            {
                FileId = protocolFileId!.ToString()!,
                FileName = protocolFileName!.ToString()!,
            }
            : null;
        var consent = consentValue != null
                      && consentValue.TryGetValue("fileId", out var consentFileId)
                      && consentValue.TryGetValue("fileName", out var consentFileName)
            ? new FileDto()
            {
                FileId = consentFileId!.ToString()!,
                FileName = consentFileName!.ToString()!,
            }
            : null;
        return await Task.FromResult(new FormRecognitionContext()
        {
            Ethics = ethics,
            Protocol = protocol,
            Consent = consent
        });
    }

    public async Task<string> CreateAsync(FormDefinitionDto formDefinitionDto)
    {
        // 创建实例
        var result = await _dynamicFormApiClient.SaveWithoutInstanceAsync(FormCode, formDefinitionDto);
        return result.BusinessId;
    }


    public async Task<string> TranslateAsync(string businessId)
    {
        var formDefinitionDto = await GetAsync(businessId, false);
        var requestId = await _formRecognitionService.ContentTranslateAsync(formDefinitionDto);

        var formData = await _dynamicFormApiClient.GetFormDataAsync(businessId);
        formData["TranslateRequestId"] = requestId;
        await _dynamicFormApiClient.SetFormDataAsync(businessId, formData);

        return requestId;
    }

    public async Task<string> CreateAsync(string taskId)
    {
        //获取表单定义
        var formDefinition = await _dynamicFormApiClient.GetDefinitionAsync(FormCode);

        await _formRecognitionService.FillFormAsync(taskId, FillFormLanguage.Chinese, "Extract", "fill",
            formDefinition);

        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "Extract");

        if (asyncTask != null)
        {
            if (asyncTask.ApplyUserId != _currentUserContext.GetCurrentUserId())
            {
                throw new ValidationException("这不是您发起的任务申请/This is not your task application");
            }

            var context = JsonSerializer.Deserialize<FormRecognitionContext>(asyncTask.TaskData);

            if (context != null)
            {
                formDefinition.Groups.ForEach(group =>
                {
                    group.Fields.ForEach(field =>
                    {
                        if (field.Code == "ethic_committee_approved_file")
                        {
                            field.Value = context.Ethics != null
                                ? new MultiTypeValue()
                                {
                                    KeyValue = new Dictionary<string, object?>()
                                    {
                                        { "fileId", context.Ethics.FileId },
                                        { "fileName", context.Ethics.FileName },
                                        { "fileSize", context.Ethics.FileSize }
                                    }
                                }
                                : null;
                        }
                        else if (field.Code == "study_protocol")
                        {
                            field.Value = context.Protocol != null
                                ? new MultiTypeValue()
                                {
                                    KeyValue = new Dictionary<string, object?>()
                                    {
                                        { "fileId", context.Protocol.FileId },
                                        { "fileName", context.Protocol.FileName },
                                        { "fileSize", context.Protocol.FileSize }
                                    }
                                }
                                : null;
                        }
                        else if (field.Code == "informed_consent_file")
                        {
                            field.Value = context.Consent != null
                                ? new MultiTypeValue()
                                {
                                    KeyValue = new Dictionary<string, object?>()
                                    {
                                        { "fileId", context.Consent.FileId },
                                        { "fileName", context.Consent.FileName },
                                        { "fileSize", context.Consent.FileSize }
                                    }
                                }
                                : null;
                        }
                        else if (field.Code == "ethic_committee_approved")
                        {
                            field.Value = new MultiTypeValue()
                            {
                                StringValue = "1"
                            };
                        }
                    });
                });
            }
        }

        // 设置formData
        var formData = new Dictionary<string, string?>()
        {
            {
                "TaskId", taskId
            }
        };

        formDefinition.FormData = formData;

        return await CreateAsync(formDefinition);
    }


    public async Task<ProjectPageDto> GetProjectPageAsync(
        [FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria)
    {
        var whereExpr = criteria.Condition.BuildExpression<long, ProjectEntity, ProjectQueryCriteria>();

        var orderbyExpr = criteria.BuildOrderExpression<long, ProjectEntity, ProjectQueryCriteria>();
        var data = await _projectRepository.GetProjectPageAsync(
            whereExpr,
            criteria.Condition.DynamicQueries!,
            0,
            100,
            orderbyExpr
        );

        var versionNumbers = new Dictionary<string, string>();

        data.Rows.ForEach(r =>
        {
            var currentVersion = string.Join(".", r.Version.Replace("v", "").Split('.').Select(q => q.PadLeft(3, '0')));

            var previousCount = data.Rows.Count(q =>
            {
                var previousVersion =
                    string.Join(".", q.Version.Replace("v", "").Split('.').Select(v => v.PadLeft(3, '0')));

                return q.BusinessId == r.BusinessId && q.Key != r.Key &&
                       string.Compare(previousVersion, currentVersion, StringComparison.Ordinal) < 0;
            });
            versionNumbers.Add(r.Version, $"v1.0.{previousCount}");
        });

        versionNumbers.ForEach(v =>
        {
            var finder = data.Rows.FirstOrDefault(q => q.Version == v.Key);
            if (finder != null)
            {
                finder.Version = v.Value;
            }
        });

        data.Rows = data.Rows.Skip(criteria.PageParams.ToRowIndex()).Take(criteria.PageParams.PageSize)
            .OrderByDescending(q => q.Version).ToList();

        return await ConvertProjectPageDataAsync(data);
    }

    public async Task<ProjectPageDto> GetNewestProjectPageAsync(
        [FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria)
    {
        var whereExpr = criteria.Condition.BuildExpression<long, ProjectEntity, ProjectQueryCriteria>();

        var orderbyExpr = criteria.BuildOrderExpression<long, ProjectEntity, ProjectQueryCriteria>();
        var data = await _projectRepository.GetNewestProjectPageAsync(
            whereExpr,
            criteria.Condition.DynamicQueries!,
            criteria.PageParams.ToRowIndex(),
            criteria.PageParams.PageSize,
            orderbyExpr
        );
        return await ConvertProjectPageDataAsync(data);
    }

    public async Task<Dictionary<string, List<OptionDto>?>?> GetSelectOptionAsync()
    {
        var options = new Dictionary<string, List<OptionDto>?>();
        var formDefinitionDto = await _dynamicFormApiClient.GetDefinitionAsync(FormCode);
        foreach (var group in formDefinitionDto.Groups)
        {
            // 遍历Group下所有Field，筛选类型为select的
            foreach (var field in group.Fields)
            {
                if (field.Type == "select" || field.Type == "radio" || field.Type == "unit_select" ||
                    field.Type == "checkbox")
                {
                    options[field.Code] = field?.Options;
                }

                if (field?.Fields.Any() ?? false)
                {
                    foreach (var childField in field.Fields)
                    {
                        if (childField.Type == "select" || childField.Type == "radio" ||
                            childField.Type == "unit_select" || childField.Type == "checkbox")
                        {
                            options[childField.Code] = childField?.Options;
                        }
                    }
                }
            }
        }

        return options;
    }

    private async Task<ProjectPageDto> ConvertProjectPageDataAsync(PageData<long, ProjectEntity> data)
    {
        // // 获取所有业务ID
        // var businessIds = data.Rows.Select(x => x.BusinessId).Distinct().ToList();
        // // 批量查询所有相关历史记录
        // var historyList = await _projectHistoryRepository.GetListAsync(q => businessIds.Contains(q.BusinessId));
        // // 映射并赋值 first_submit_time
        // var dtoList = data.Rows.Select(project =>
        // {
        //     var dto = _mapper.Map<ProjectsDto>(project);
        //     var firstHistory = historyList
        //         .Where(h => h.BusinessId == project.BusinessId)
        //         .OrderBy(h => h.CreatedTime)
        //         .FirstOrDefault();
        //     dto.first_submit_time = firstHistory?.CreatedTime ?? project.CreatedTime;
        //     return dto;
        // }).ToList();

        // 构造 ProjectPageDto
        var result = new ProjectPageDto
        {
            Totals = data.Totals,
            Rows = _mapper.Map<List<ProjectsDto>>(data.Rows)
        };
        return await Task.FromResult(result);
    }

    public async Task<ProjectsDto?> GetProjectInfoAsync(long key)
    {
        var project = await _projectRepository.GetAsync(key);
        if (project == null)
        {
            throw new ValidationException("记录不存在/Record does not exist");
        }

        ProjectsDto dto = new ProjectsDto() { Key = 0 };
        _mapper.Map(project, dto);
        //files
        //var files = await _projectAttachRepository.GetListAsync(q => q.ProjectId == project.Key);
        //试验主办单位
        var existSponsorList =
            await _projectSponsorRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.sponsor = _mapper.Map<List<ProjectSponsorDto>>(existSponsorList);
        // 研究实施地点
        var existSiteList =
            await _projectResearchSiteRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.researchSite = _mapper.Map<List<ProjectResearchSiteDto>>(existSiteList);
        // 干预措施
        var existInterventionList =
            await _projectInterventionRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.intervention = _mapper.Map<List<ProjectInterventionDto>>(existInterventionList);
        // 测量指标
        var existMeasurementList =
            await _projectMeasurementRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.measurement = _mapper.Map<List<ProjectMeasurementDto>>(existMeasurementList);
        // 采集人体标本
        var existHumanSampleList =
            await _projectHumanSampleRepository.GetListAsync(q =>
                q.ProjectId == project.Key && q.BusinessId == project.BusinessId);
        dto.humanSample = _mapper.Map<List<ProjectHumanSampleDto>>(existHumanSampleList);
        return dto;
    }


    public async Task<long?> GetProjectHistoryNewestPidAsync(string businessId)
    {
        var result = await _projectRepository.GetListAsync(q => q.BusinessId == businessId);
        return result.OrderByDescending(q => q.release_time).FirstOrDefault()?.Key;
    }

    public async Task<int> GetSummarizedDataAsync(long userId, string statisticsCode, string positionCode)
    {
        var auditors = (await _userMgtApiClient.GetManagedPositionUsersAsync(positionCode))
            .SelectMany(u => u.Users).ToList();
        if (auditors.All(u => u.Key != userId))
        {
            throw new ValidationException("用户不在该岗位下/User is not in the position");
        }

        //字符串转为枚举 ProjectStatisticsEnum 如果不存在则抛出异常
        if (!Enum.TryParse<ProjectStatisticsEnum>(statisticsCode, out var category))
        {
            throw new ValidationException("统计类型不存在/Statistics type does not exist");
        }

        return await GetCountAsync(category, userId);
    }
}

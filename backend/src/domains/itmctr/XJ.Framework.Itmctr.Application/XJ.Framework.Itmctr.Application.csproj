<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj"/>
        <ProjectReference Include="..\..\dynamic-form\XJ.Framework.DynamicForm.ApiClient\XJ.Framework.DynamicForm.ApiClient.csproj" />
        <ProjectReference Include="..\..\dynamic-form\XJ.Framework.DynamicForm.Application.Contract\XJ.Framework.DynamicForm.Application.Contract.csproj" />
        <ProjectReference Include="..\..\files\XJ.Framework.Files.ApiClient\XJ.Framework.Files.ApiClient.csproj" />
        <ProjectReference Include="..\XJ.Framework.Itmctr.Application.Contract\XJ.Framework.Itmctr.Application.Contract.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Itmctr.EntityFrameworkCore\XJ.Framework.Itmctr.EntityFrameworkCore.csproj"/>
        <ProjectReference Include="..\..\..\core\abstractions\XJ.Framework.Library.DistributedLock\XJ.Framework.Library.DistributedLock.csproj"/>
        <ProjectReference Include="..\..\..\core\impls\XJ.Framework.Library.DistributedLock.Cache\XJ.Framework.Library.DistributedLock.Cache.csproj"/>
    </ItemGroup>

</Project> 
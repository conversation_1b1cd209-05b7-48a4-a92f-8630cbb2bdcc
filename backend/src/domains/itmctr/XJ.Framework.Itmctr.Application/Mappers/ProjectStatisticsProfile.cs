namespace XJ.Framework.Rbac.Application.Mappers;

public class ProjectStatisticsProfile : Profile
{
    public ProjectStatisticsProfile()
    {
        CreateMap<ProjectStatisticsBatchEntity, ProjectStatisticsBatchDto>();
        CreateMap<ProjectStatisticsBatchOperationDto, ProjectStatisticsBatchEntity>()
            .ForMember(dest => dest.CreatedTime,
                opt =>
                    opt.MapFrom(src => DateTimeOffset.UtcNow)
            );

        CreateMap<RptProjectEntity, ProjectsDto>();

        CreateMap<ProjectStatisticsBatchItemEntity, ProjectStatisticsBatchItemDto>();

        CreateMap<ProjectStatisticsBatchItemViewEntity, ProjectStatisticsBatchItemViewDto>();

        CreateMap<ProjectEntity, RptProjectEntity>();
        CreateMap<ProjectAttachEntity, RptProjectAttachEntity>();
        CreateMap<ProjectHumanSampleEntity, RptProjectHumanSampleEntity>();
        CreateMap<ProjectInterventionEntity, RptProjectInterventionEntity>();
        CreateMap<ProjectMeasurementEntity, RptProjectMeasurementEntity>();
        CreateMap<ProjectResearchSiteEntity, RptProjectResearchSiteEntity>();
        CreateMap<ProjectSponsorEntity, RptProjectSponsorEntity>();

        CreateMap<RptProjectQueryCriteria, ExportXmlProjectQueryCriteria>();
    }
}

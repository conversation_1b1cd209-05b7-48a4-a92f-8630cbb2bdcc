using AutoMapper;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;

namespace XJ.Framework.Rbac.Application.Mappers;

/// <summary>
/// AsyncTask Profile
/// </summary>
public class AsyncTaskProfile : Profile
{
    public AsyncTaskProfile()
    {
        CreateMap<AsyncTaskEntity, AsyncTaskDto>();
        CreateMap<AsyncTaskOperationDto, AsyncTaskEntity>();
        CreateMap<AsyncTaskDto, AsyncTaskOperationDto>();
    }
}
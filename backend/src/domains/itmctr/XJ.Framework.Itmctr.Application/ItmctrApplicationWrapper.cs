using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Options;
using XJ.Framework.Itmctr.Application.Services;
using XJ.Framework.Rbac.ApiClient;
using XJ.Framework.Library.DistributedLock.Cache;

namespace XJ.Framework.Itmctr.Application;

public class ItmctrApplicationWrapper : ApplicationWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        // services.AddSingleton<YourService>();

        services.AddTransient<DynamicFormApiClientHelper>();

        services.AddHttpClient<UserApiClient>();
        services.AddHttpClient<FilesApiClient>();
        services.AddHttpClient<DynamicFormApiClient>();
        services.AddHttpClient<DynamicFormMgtApiClient>();
        services.AddHttpClient<DynamicFormApplicationApiClient>();
        services.AddHttpClient<DynamicFormMgtApplicationApiClient>();
        services.AddHttpClient<UserMgtApiClient>();
        services.AddHttpClient<UserMgtApplicationApiClient>();
        services.AddHttpClient<FilesApplicationApiClient>();

        services.AddHttpClient("UnicomApi")
            .AddHttpMessageHandler<UnicomTokenHandler>();

        services.AddSingleton<ITokenProvider, TokenProvider>();
        services.AddTransient<UnicomTokenHandler>();
        services.AddTransient<UnicomApiClient>();

        services.AddTransient<IFormRecognitionService, FormRecognitionService>();

        services.AddTransient<ExportXmlService>();

        services.AddKeyedScoped<IRptProjectService, RptExportXmlService>("ExportXml");

        services.AddScoped<RptProjectServiceFactory>();

        services.Configure<UnicomOption>(configuration.GetSection("UnicomOptions"));
    }
}

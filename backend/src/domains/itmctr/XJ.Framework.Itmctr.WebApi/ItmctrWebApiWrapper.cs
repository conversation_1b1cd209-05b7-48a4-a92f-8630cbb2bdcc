using XJ.Framework.Files.ApiClient;

namespace XJ.Framework.Itmctr.WebApi;

public class ItmctrWebApiWrapper : WebApiWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<ItmctrApplicationWrapper, ItmctrInfrastructureWrapper>(configuration);
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}

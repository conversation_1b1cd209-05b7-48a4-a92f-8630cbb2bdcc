using Microsoft.AspNetCore.Authorization;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.Services;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Logging.Abstraction.DI;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Itmctr.WebApi.Controllers;

[ApiController]
[Route("")]
public class CallbackController : ControllerBase
{
    private readonly ILogger<CallbackController> _logger;
    private readonly IFormRecognitionService _formRecognitionService;
    private readonly IProjectService _projectService;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly ICurrentUserContext _currentUserContext;

    public CallbackController(ILogger<CallbackController> logger, IFormRecognitionService formRecognitionService,
        IProjectService projectService, IAsyncTaskService asyncTaskService, ICurrentUserContext currentUserContext)
    {
        _logger = logger;
        _formRecognitionService = formRecognitionService;
        _projectService = projectService;
        _asyncTaskService = asyncTaskService;
        _currentUserContext = currentUserContext;
    }

    /// <summary>
    /// 文档填充检查
    /// </summary>
    /// <param name="formRecognitionContext"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("precheck")]
    [PublicPermission]
    // [UnitOfWork]
    public async Task<PreCheckResult> PreCheckAsync(FormRecognitionContext formRecognitionContext)
    {
        return await _formRecognitionService.PreCheckAsync(_currentUserContext.GetCurrentUserId()!.Value,
            formRecognitionContext);
    }

    /// <summary>
    /// 发起提取
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("extract/{taskId}")]
    [PublicPermission]
    [UnitOfWork]
    public async Task<bool> ExtractFormAsync(string taskId)
    {
        return await _formRecognitionService.ExtractAsync(taskId);
    }

    /// <summary>
    /// 发起内容对比
    /// </summary>
    /// <param name="formId"></param>
    /// <returns></returns>
    [HttpPost]
    [Route("content-compare/{formId}")]
    [PublicPermission]
    [UnitOfWork]
    public async Task<bool> ContentCompareAsync(string formId)
    {
        var newestForm = await _projectService.GetAsync(formId);
        var taskId = newestForm.FormData["TaskId"]!;
        await _formRecognitionService.ContentCompareAsync(_currentUserContext.GetCurrentUserId()!.Value, taskId,
            newestForm);
        return true;
    }

    /// <summary>
    /// 发起全文翻译
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost]
    [PublicPermission]
    [Route("content-translate/{businessId}")]
    [UnitOfWork]
    public async Task<string> ContentTranslateAsync(string businessId)
    {
        return await _projectService.TranslateAsync(businessId);
    }


    /// <summary>
    /// 获取全文翻译结果
    /// </summary>
    /// <param name="formId"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission]
    [Route("content-translate/{formId}")]
    [UnitOfWork]
    public async Task<Dictionary<string, AnnotationValue?>?> GetContentTranslateAsync(string formId)
    {
        var formDefinitionDto = await _projectService.GetAsync(formId);
        var requestId = formDefinitionDto.FormData["TranslateRequestId"];
        if (requestId.IsNullOrEmpty())
            return null;
        await _formRecognitionService.FillFormAsync(requestId!, FillFormLanguage.English, "ContentTranslate",
            "translate", formDefinitionDto);

        return await FormDefinitionHelper.GetAnnotationsAsync(formDefinitionDto);
    }

    /// <summary>
    /// 获取表单对比差异
    /// </summary>
    /// <param name="formId"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission]
    [Route("content-compare/{formId}")]
    public async Task<FormCompareDto> GetContentCompareAsync(string formId)
    {
        var newestForm = await _projectService.GetAsync(formId);
        var taskId = newestForm.FormData["TaskId"]!;
        return await _formRecognitionService.GetFormCompareAsync(formId, taskId);
    }

    /// <summary>
    /// 获取任务是否完成
    /// </summary>
    /// <param name="taskId"></param>
    /// <param name="taskCode"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission]
    [Route("progress/{taskId}/{taskCode}")]
    public async Task<bool> CheckTaskCompletedAsync(string taskId, string taskCode)
    {
        var asyncTask = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, taskCode);

        return asyncTask?.TaskStatus == AsyncTaskStatus.Completed;
    }


    /// <summary>
    /// 尝试完成提取任务
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    [HttpPost]
    [PublicPermission]
    [UnitOfWork]
    [IgnoreLogging]
    [Route("extract/progress/{taskId}")]
    public async Task<int> FinishExtractWhenTaskCompletedAsync(string taskId)
    {
        var task = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "Extract");
        if (task != null && task.TaskStatus == AsyncTaskStatus.Completed)
        {
            var dtos = JsonSerializer.Deserialize<AttachmentParseDto>(task.TaskResult!)!;
            return dtos.Data.Count;
        }

        var result = await _formRecognitionService.QueryAttachmentParseCompletedProgressAsync(taskId);
        if (result == null)
        {
            return -1;
        }

        await _formRecognitionService.CompletedExtractAsync(taskId, result);

        return result.Data.Count;
    }


    /// <summary>
    /// 尝试完成内容对比任务
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    [HttpPost]
    [PublicPermission]
    [IgnoreLogging]
    [UnitOfWork]
    [Route("content-compare/progress/{taskId}")]
    public async Task<bool> FinishContentCompareWhenTaskCompletedAsync(string taskId)
    {
        var task = await _asyncTaskService.GetTaskByBusinessIdAsync(taskId, "ContentCompare");
        if (task != null && task.TaskStatus == AsyncTaskStatus.Completed)
        {
            return true;
        }


        var result = await _formRecognitionService.QueryContentCompareCompletedProgressAsync(taskId);
        if (result == null)
        {
            return false;
        }

        await _formRecognitionService.CompletedContentCompareAsync(taskId, result);
        return true;
    }


    /// <summary>
    /// 尝试完成全文翻译任务
    /// </summary>
    /// <param name="requestId"></param>
    /// <returns></returns>
    [HttpPost]
    [PublicPermission]
    [UnitOfWork]
    [IgnoreLogging]
    [Route("content-translate/progress/{requestId}")]
    public async Task<bool> FinishContentTranslateWhenTaskCompletedAsync(string requestId)
    {
        var task = await _asyncTaskService.GetTaskByBusinessIdAsync(requestId, "ContentTranslate");
        if (task != null && task.TaskStatus == AsyncTaskStatus.Completed)
        {
            return true;
        }

        var result = await _formRecognitionService.QueryContentTranslateCompletedProgressAsync(requestId);
        if (result == null)
        {
            return false;
        }

        await _formRecognitionService.CompletedContentTranslateAsync(requestId, result);
        return true;
    }

    /// <summary>
    /// 实时翻译
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [PublicPermission]
    [UnitOfWork]
    [Route("realtime-translate")]
    public async Task<string?> RealtimeContentTranslateAsync([FromBody] RealtimeTranslateDto input)
    {
        return await _formRecognitionService.RealtimeContentTranslateAsync(input.Content);
    }

    /// <summary>
    /// 表单提取回调
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreFormat]
    [AllowAnonymous]
    [UnitOfWork]
    [Route("attachment_parse_callback")]
    public async Task<AttachmentParseResponseDto> AttachmentParseCallbackAsync(
        [FromBody] AttachmentParseDto input)
    {
        _logger.LoggingInformation("unicom", input.ToJson());
        try
        {
            await _formRecognitionService.CompletedExtractAsync(input.Id, input);
            return new AttachmentParseResponseDto()
            {
                Code = 200,
                Msg = "Ok",
                TaskId = input.Id
            };
        }
        catch (Exception ex)
        {
            _logger.LoggingException("unicom", ex);
            return new AttachmentParseResponseDto()
            {
                Code = 500,
                Msg = ex.Message,
                TaskId = input.Id
            };
        }
    }

    /// <summary>
    /// 表单差异比较回调
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreFormat]
    [AllowAnonymous]
    [UnitOfWork]
    [Route("content_compare_callback")]
    public async Task<ContentCompareResponseDto> ContentCompareCallbackAsync(
        [FromBody] ContentCompareDto input)
    {
        _logger.LoggingInformation("unicom", input.ToJson());
        try
        {
            await _formRecognitionService.CompletedContentCompareAsync(input.Id, input);
            return new ContentCompareResponseDto()
            {
                Code = 200,
                Msg = "Ok",
                TaskId = input.Id
            };
        }
        catch (Exception ex)
        {
            _logger.LoggingException("unicom", ex);
            return new ContentCompareResponseDto()
            {
                Code = 500,
                Msg = ex.Message,
                TaskId = input.Id
            };
        }
    }

    /// <summary>
    /// 全文翻译回调
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost]
    [IgnoreFormat]
    [AllowAnonymous]
    [UnitOfWork]
    [Route("content_translate_callback")]
    public async Task<ContentTranslateResponseDto> ContentTranslateCallbackAsync(
        [FromBody] ContentTranslateDto input)
    {
        _logger.LoggingInformation("unicom", input.ToJson());
        try
        {
            await _formRecognitionService.CompletedContentTranslateAsync(input.Id, input);
            return new ContentTranslateResponseDto()
            {
                Code = 200,
                Msg = "Ok",
                RequestId = input.Id
            };
        }
        catch (Exception ex)
        {
            _logger.LoggingException("unicom", ex);
            return new ContentTranslateResponseDto()
            {
                Code = 500,
                Msg = ex.Message,
                RequestId = input.Id
            };
        }
    }
}

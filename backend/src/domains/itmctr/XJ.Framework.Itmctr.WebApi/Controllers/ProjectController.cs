using Microsoft.AspNetCore.Authorization;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Files.ApiClient;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Itmctr.WebApi.Controllers;

[ApiController]
[PublicPermission]
[Route("[controller]")]
public partial class ProjectController : ControllerBase
{
    private readonly IProjectService _projectService;
    private readonly IProjectHistoryService _projectHistoryService;

    public ProjectController(IProjectService projectService, IProjectHistoryService projectHistoryService)
    {
        _projectService = projectService;
        _projectHistoryService = projectHistoryService;
    }

    /// <summary>
    /// 获取表单定义
    /// </summary>
    /// <returns></returns>
    [HttpGet("define")]
    [EnableGzip]
    [IgnoreLogging]
    public async Task<FormDefinitionDto> GetDefinitionAsync()
    {
        return await _projectService.GetFormDefinitionAsync();
    }

    /// <summary>
    /// 使用异步任务id创建表单
    /// </summary>
    /// <param name="taskId"></param>
    /// <returns></returns>
    [HttpPost("create/{taskId}")]
    public async Task<string> CreateAsync(string taskId)
    {
        return await _projectService.CreateAsync(taskId);
    }

    /// <summary>
    /// 无需参数直接保存 （用于首次保存）
    /// </summary>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    [HttpPost("save")]
    public async Task<string> SaveAsync([FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await _projectService.CreateAsync(formDefinitionDto);
    }

    /// <summary>
    /// 保存（用于修改）
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    [HttpPost("save/{businessId}")]
    public async Task<string> SaveAsync(string businessId, [FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await _projectService.SaveAsync(businessId, formDefinitionDto);
    }

    /// <summary>
    /// 提交
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpPost("submit/{businessId}")]
    public async Task<bool> SubmitAsync(string businessId)
    {
        return await _projectService.SubmitAsync(businessId);
    }

    /// <summary>
    /// 再修改申请
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("edit-apply-project/{businessId}")]
    [PublicPermission]
    public async Task<bool> EditApplyAsync(string businessId, [FromBody] EditApplyProjectOperationDto input)
    {
        return await _projectService.EditApplyAsync(businessId, input);
    }


    /// <summary>
    /// 获取最新表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpGet("newest/{businessId}")]
    [IgnoreLogging]
    public async Task<FormDefinitionDto> GetNewestFormInstanceAsync(string businessId)
    {
        return await _projectService.GetAsync(businessId);
    }

    [HttpGet("named/{businessId}/{version}")]
    [IgnoreLogging]
    public async Task<FormDefinitionDto> GetNamedFormInstanceAsync(string businessId, string version)
    {
        return await _projectService.GetAsync(businessId, version);
    }

    /// <summary>
    /// 获取最新表单实例差异
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpGet("diff/{businessId}")]
    [IgnoreLogging]
    public async Task<FormDefinitionDto> GetNewestDiffFormInstanceAsync(string businessId)
    {
        return await _projectService.GetWithPreviousCompareAsync(businessId);
    }


    [HttpGet("dashboard")]
    [PublicPermission]
    [IgnoreLogging]
    public async Task<List<DashboardStatisticsDto>> GetDashboardAsync()
    {
        return await _projectService.GetDashboardAsync();
    }


    /// <summary>
    /// 分页获取表单实例
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("trialsearch")]
    [AllowAnonymous]
    [IgnoreLogging]
    public async Task<ProjectPageDto> GetProjectSearchPageAsync(
        [FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria)
    {
        return await _projectService.GetNewestProjectPageAsync(criteria);
    }

    [HttpGet("options")]
    [AllowAnonymous]
    [IgnoreLogging]
    public async Task<Dictionary<string, List<OptionDto>?>?> GetSelectOptionsAsync()
    {
        return await _projectService.GetSelectOptionAsync();
    }

    [HttpGet("info/{key}")]
    [AllowAnonymous]
    public async Task<ProjectsDto?> GetProjectInfoAsync(long key)
    {
        return await _projectService.GetProjectInfoAsync(key);
    }

    [HttpGet("history")]
    [AllowAnonymous]
    [IgnoreLogging]
    public async Task<ProjectPageDto?> GetProjectHistoryPageAsync(
        [FromQuery] PagedQueryCriteria<ProjectQueryCriteria> criteria)
    {
        criteria.Condition.BusinessId ??= Guid.NewGuid().ToString();
        return await _projectService.GetProjectPageAsync(criteria);
    }

    [HttpGet("history/{businessId}/newest/pid")]
    [AllowAnonymous]
    public async Task<long?> GetProjectHistoryNewestPidAsync(string businessId)
    {
        return await _projectService.GetProjectHistoryNewestPidAsync(businessId);
    }
    
}

using System.Xml.Serialization;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

[XmlRoot(ElementName = "trials")]
public class ExportXmlInfo
{
    [XmlAttribute(AttributeName = "subjects")]
    public int Subjects {
        get;
        set;
    }

    [XmlElement("Trial")] public List<TrialInfo> Trials { get; set; } = new();
}

public class TrialInfo
{
    [XmlElement("main")] public MainStr Main { get; set; } = new();

    [XmlElement("contacts")] public ContactsStr Contacts { get; set; } = new();

    [XmlElement("countries")] public CountriesStr Countries { get; set; } = new();

    [XmlElement("criteria")] public CriteriaStr Criteria { get; set; } = new();

    [XmlElement("health_condition_code")] public HealthConditionCodeStr HealthConditionCode { get; set; } = new();

    [XmlElement("health_condition_keyword")]
    public HealthConditionKeywordStr HealthConditionKeyword { get; set; } = new();

    [XmlElement("intervention_code")] public InterventionCodeStr InterventionCode { get; set; } = new();

    [XmlElement("intervention_keyword")] public InterventionKeywordStr InterventionKeyword { get; set; } = new();

    [XmlElement("primary_outcome")] public PrimaryOutcomeStr PrimaryOutcome { get; set; } = new();

    [XmlElement("secondary_outcome")] public SecondaryOutcomeStr SecondaryOutcome { get; set; } = new();

    [XmlElement("secondary_sponsor")] public SecondarySponsorStr SecondarySponsor { get; set; } = new();

    [XmlElement("secondary_ids")] public SecondaryIdsStr SecondaryIds { get; set; } = new();

    [XmlElement("source_support")] public SourceSupportStr SourceSupport { get; set; } = new();

    [XmlElement("ethics_reviews")] public EthicsReviews EthicsReviews { get; set; } = new();
}

public class MainStr
{
    [XmlElement("trial_id")] public string TrialId { get; set; } = "";

    [XmlElement("utrn")] public string Utrn { get; set; } = "";

    [XmlElement("reg_name")] public string RegName { get; set; } = "";

    [XmlElement("date_registration")] public string DateRegistration { get; set; } = "";

    [XmlElement("primary_sponsor")] public string PrimarySponsor { get; set; } = "";

    [XmlElement("public_title")] public string PublicTitle { get; set; } = "";

    [XmlElement("acronym")] public string Acronym { get; set; } = "";

    [XmlElement("scientific_title")] public string ScientificTitle { get; set; } = "";

    [XmlElement("Scientific_acronym")] public string ScientificAcronym { get; set; } = "";

    [XmlElement("date_enrolment")] public string DateEnrolment { get; set; } = "";

    [XmlElement("type_enrolment")] public string TypeEnrolment { get; set; } = "";

    [XmlElement("target_size")] public string TargetSize { get; set; } = "";

    [XmlElement("recruitment_status")] public string RecruitmentStatus { get; set; } = "";

    [XmlElement("url")] public string Url { get; set; } = "";

    [XmlElement("study_type")] public string StudyType { get; set; } = "";

    [XmlElement("study_design")] public string StudyDesign { get; set; } = "";

    [XmlElement("phase")] public string Phase { get; set; } = "";

    [XmlElement("hc_freetext")] public string HcFreetext { get; set; } = "";

    [XmlElement("i_freetext")] public string IFreetext { get; set; } = "";

    [XmlElement("results_actual_enrolment")]
    public string ResultsActualEnrolment { get; set; } = "";

    [XmlElement("results_date_completed")] public string ResultsDateCompleted { get; set; } = "";

    [XmlElement("results_url_link")] public string ResultsUrlLink { get; set; } = "";

    [XmlElement("results_summary")] public string ResultsSummary { get; set; } = "";

    [XmlElement("results_date_posted")] public string ResultsDatePosted { get; set; } = "";

    [XmlElement("results_date_first_publication")]
    public string ResultsDateFirstPublication { get; set; } = "";

    [XmlElement("results_baseline_char")] public string ResultsBaselineChar { get; set; } = "";

    [XmlElement("results_participant_flow")]
    public string ResultsParticipantFlow { get; set; } = "";

    [XmlElement("results_adverse_events")] public string ResultsAdverseEvents { get; set; } = "";

    [XmlElement("results_outcome_measures")]
    public string ResultsOutcomeMeasures { get; set; } = "";

    [XmlElement("results_url_protocol")] public string ResultsUrlProtocol { get; set; } = "";

    [XmlElement("results_IPD_plan")] public string ResultsIpdPlan { get; set; } = "";

    [XmlElement("results_IPD_description")]
    public string ResultsIpdDescription { get; set; } = "";
}

public class ContactStr
{
    [XmlElement("type")] public string Type { get; set; } = "";

    [XmlElement("firstname")] public string Firstname { get; set; } = "";

    [XmlElement("middlename")] public string Middlename { get; set; } = "";

    [XmlElement("lastname")] public string Lastname { get; set; } = "";

    [XmlElement("address")] public string Address { get; set; } = "";

    [XmlElement("city")] public string City { get; set; } = "";

    [XmlElement("country1")] public string Country1 { get; set; } = "";

    [XmlElement("zip")] public string Zip { get; set; } = "";

    [XmlElement("telephone")] public string Telephone { get; set; } = "";

    [XmlElement("email")] public string Email { get; set; } = "";

    [XmlElement("affiliation")] public string Affiliation { get; set; } = "";
}

public class ContactsStr
{
    [XmlElement("contact")] public ContactStr[] Contact { get; set; } = [];
}

public class CountriesStr
{
    [XmlElement("country2")] public string Country2 { get; set; } = "";
}

public class CriteriaStr
{
    [XmlElement("inclusion_criteria")] public string InclusionCriteria { get; set; } = "";

    [XmlElement("agemin")] public string Agemin { get; set; } = "";

    [XmlElement("agemax")] public string Agemax { get; set; } = "";

    [XmlElement("gender")] public string Gender { get; set; } = "";

    [XmlElement("exclusion_criteria")] public string ExclusionCriteria { get; set; } = "";
}

public class HealthConditionCodeStr
{
    [XmlElement("hc_code")] public string HcCode { get; set; } = "";
}

public class HealthConditionKeywordStr
{
    [XmlElement("hc_keyword")] public string HcCode { get; set; } = "";
}

public class InterventionCodeStr
{
    [XmlElement("i_code")] public string ICode { get; set; } = "";
}

public class InterventionKeywordStr
{
    [XmlElement("i_keyword")] public string IKeyword { get; set; } = "";
}

public class PrimaryOutcomeStr
{
    [XmlElement("prim_outcome")] public string PrimOutcome { get; set; } = "";
}

public class SecondaryIdsStr
{
    [XmlElement("secondary_id")] public List<SecondaryIdStr> SecondaryId { get; set; } = new();
}

public class SecondaryIdStr
{
    [XmlElement("sec_id")] public string SecId { get; set; } = "";

    [XmlElement("issuing_authority")] public string IssuingAuthority { get; set; } = "";
}

public class SecondaryOutcomeStr
{
    [XmlElement("sec_outcome")] public string SecOutcome { get; set; } = "";
}

public class SecondarySponsorStr
{
    [XmlElement("sponsor_name")] public string SponsorName { get; set; } = "";
}

public class SourceSupportStr
{
    [XmlElement("source_name")] public string SourceName { get; set; } = "";
}

public class EthicsReview
{
    [XmlElement("status")] public string Status { get; set; } = "";

    [XmlElement("approval_date")] public string ApprovalDate { get; set; } = "";

    [XmlElement("contact_name")] public string ContactName { get; set; } = "";

    [XmlElement("contact_address")] public string ContactAddress { get; set; } = "";

    [XmlElement("contact_phone")] public string ContactPhone { get; set; } = "";

    [XmlElement("contact_email")] public string ContactEmail { get; set; } = "";
}

public class EthicsReviews
{
    [XmlElement("ethics_review")] public EthicsReview EthicsReview { get; set; } = new();
}

using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Text.Json.Serialization;
using System.Threading.Tasks;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class ProjectsDto : BaseDto<long>
{
    [JsonPropertyName("chi_reg_number")] public string? chi_reg_number { get; set; }

    [JsonPropertyName("created_time")] public DateTimeOffset? CreatedTime { get; set; }

    [JsonPropertyName("release_time")] public DateTimeOffset? release_time { get; set; }

    /// <summary>
    /// 语言
    /// </summary>
    [JsonPropertyName("language")]
    public string language { get; set; } = null!;

    /// <summary>
    /// 关联业务id
    /// </summary>
    [JsonPropertyName("business_id")]
    public string BusinessId { get; set; } = null!;

    /// <summary>
    /// 版本号
    /// </summary>
    [JsonPropertyName("version")]
    public string Version { get; set; } = null!;

    /// <summary>
    /// 表单code
    /// </summary>
    [JsonPropertyName("form_code")]
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 注册号
    /// </summary>
    [JsonPropertyName("registration_number")]

    public string? registration_number { get; set; }

    /// <summary>
    /// 注册号状态
    /// </summary>
    [JsonPropertyName("registration_status")]

    public string? registration_status { get; set; }

    /// <summary>
    /// 注册题目中文
    /// </summary>
    [JsonPropertyName("publictitle_zh")]
    public string? publictitle_zh { get; set; }

    /// <summary>
    /// 注册题目英文
    /// </summary>
    [JsonPropertyName("publictitle_en")]
    public string? publictitle_en { get; set; }

    /// <summary>
    /// 注册题目简写中文
    /// </summary>
    [JsonPropertyName("english_acronym_zh")]

    public string? english_acronym_zh { get; set; }

    /// <summary>
    /// 注册题目简写英文
    /// </summary>
    [JsonPropertyName("english_acronym_en")]

    public string? english_acronym_en { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称中文
    /// </summary>
    [JsonPropertyName("scientific_title_zh")]

    public string? scientific_title_zh { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称英文
    /// </summary>
    [JsonPropertyName("scientific_title_en")]

    public string? scientific_title_en { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称简写（中文）
    /// </summary>
    [JsonPropertyName("scientific_title_acronym_zh")]

    public string? scientific_title_acronym_zh { get; set; }

    /// <summary>
    /// 研究课题的正式科学名称简写（英文）
    /// </summary>
    [JsonPropertyName("scientific_title_acronym_en")]

    public string? scientific_title_acronym_en { get; set; }

    /// <summary>
    /// 研究课题代号(代码)
    /// </summary>
    [JsonPropertyName("study_subject_id")]
    public string? study_subject_id { get; set; }

    /// <summary>
    /// 在二级注册机构或其它机构的注册号
    /// </summary>
    [JsonPropertyName("partner_registry_number")]

    public string? partner_registry_number { get; set; }

    /// <summary>
    /// 申请注册联系人（中文）
    /// </summary>
    [JsonPropertyName("applicant_zh")]
    public string? applicant_zh { get; set; }

    /// <summary>
    /// 申请注册联系人（英文）
    /// </summary>
    [JsonPropertyName("applicant_en")]
    public string? applicant_en { get; set; }

    /// <summary>
    /// 研究负责人（中文）
    /// </summary>
    [JsonPropertyName("study_leader_zh")]
    public string? study_leader_zh { get; set; }

    /// <summary>
    /// 研究负责人（英文）
    /// </summary>
    [JsonPropertyName("study_leader_en")]
    public string? study_leader_en { get; set; }

    /// <summary>
    /// 申请注册联系人电话
    /// </summary>
    [JsonPropertyName("applicant_telephone")]

    public string? applicant_telephone { get; set; }

    /// <summary>
    /// 研究负责人电话
    /// </summary>
    [JsonPropertyName("study_leader_telephone")]

    public string? study_leader_telephone { get; set; }

    /// <summary>
    /// 申请注册联系人传真
    /// </summary>
    [JsonPropertyName("applicant_fax")]
    public string? applicant_fax { get; set; }

    /// <summary>
    /// 研究负责人传真
    /// </summary>
    [JsonPropertyName("study_leader_fax")]
    public string? study_leader_fax { get; set; }

    /// <summary>
    /// 申请注册联系人电子邮件
    /// </summary>
    [JsonPropertyName("applicant_email")]
    public string? applicant_email { get; set; }

    /// <summary>
    /// 研究负责人电子邮件
    /// </summary>
    [JsonPropertyName("study_leader_email")]

    public string? study_leader_email { get; set; }

    /// <summary>
    /// 申请单位网址(自愿提供)
    /// </summary>
    [JsonPropertyName("applicant_website")]

    public string? applicant_website { get; set; }

    /// <summary>
    /// 研究负责人网址(自愿提供)
    /// </summary>
    [JsonPropertyName("study_leader_website")]

    public string? study_leader_website { get; set; }

    /// <summary>
    /// 申请注册联系人通讯地址（中文）
    /// </summary>
    [JsonPropertyName("applicant_address_zh")]

    public string? applicant_address_zh { get; set; }

    /// <summary>
    /// 申请注册联系人通讯地址（英文）
    /// </summary>
    [JsonPropertyName("applicant_address_en")]

    public string? applicant_address_en { get; set; }

    /// <summary>
    /// 研究负责人通讯地址（中文）
    /// </summary>
    [JsonPropertyName("study_leader_address_zh")]

    public string? study_leader_address_zh { get; set; }

    /// <summary>
    /// 研究负责人通讯地址（英文）
    /// </summary>
    [JsonPropertyName("study_leader_address_en")]

    public string? study_leader_address_en { get; set; }

    /// <summary>
    /// 申请注册联系人邮政编码
    /// </summary>
    [JsonPropertyName("applicant_postcode")]

    public string? applicant_postcode { get; set; }

    /// <summary>
    /// 研究负责人邮政编码
    /// </summary>
    [JsonPropertyName("study_leader_postcode")]

    public string? study_leader_postcode { get; set; }

    /// <summary>
    /// 申请人所在单位（中文）
    /// </summary>
    [JsonPropertyName("applicant_affiliation_zh")]

    public string? applicant_affiliation_zh { get; set; }

    /// <summary>
    /// 申请人所在单位（英文）
    /// </summary>
    [JsonPropertyName("applicant_affiliation_en")]

    public string? applicant_affiliation_en { get; set; }

    /// <summary>
    /// 研究负责人所在单位（中文）
    /// </summary>
    [JsonPropertyName("study_leader_affiliation_zh")]
    [JsonIgnore]

    public string? study_leader_affiliation_zh { get; set; }

    /// <summary>
    /// 研究负责人所在单位（英文）
    /// </summary>
    [JsonPropertyName("study_leader_affiliation_en")]
    [JsonIgnore]

    public string? study_leader_affiliation_en { get; set; }

    /// <summary>
    /// 是否获伦理委员会批准
    /// </summary>
    [JsonPropertyName("ethic_committee_approved")]

    public string? ethic_committee_approved { get; set; }

    /// <summary>
    /// 伦理委员会批件文号
    /// </summary>
    [JsonPropertyName("ethic_committee_approved_no")]

    public string? ethic_committee_approved_no { get; set; }

    /// <summary>
    /// 批准本研究的伦理委员会名称（中文）
    /// </summary>
    [JsonPropertyName("ethic_committee_name_zh")]

    public string? ethic_committee_name_zh { get; set; }

    /// <summary>
    /// 批准本研究的伦理委员会名称（英文）
    /// </summary>
    [JsonPropertyName("ethic_committee_name_en")]

    public string? ethic_committee_name_en { get; set; }

    /// <summary>
    /// 伦理委员会批准日期
    /// </summary>
    [JsonPropertyName("ethic_committee_approved_date")]
    public DateTime? ethic_committee_approved_date { get; set; }

    /// <summary>
    /// 伦理委员会联系人（中文）
    /// </summary>
    [JsonPropertyName("ethic_committee_contact_zh")]

    public string? ethic_committee_contact_zh { get; set; }

    /// <summary>
    /// 伦理委员会联系人（英文）
    /// </summary>
    [JsonPropertyName("ethic_committee_contact_en")]

    public string? ethic_committee_contact_en { get; set; }

    /// <summary>
    /// 伦理委员会联系地址（中文）
    /// </summary>
    [JsonPropertyName("ethic_committee_address_zh")]

    public string? ethic_committee_address_zh { get; set; }

    /// <summary>
    /// 伦理委员会联系地址（英文）
    /// </summary>
    [JsonPropertyName("ethic_committee_address_en")]

    public string? ethic_committee_address_en { get; set; }

    /// <summary>
    /// 伦理委员会联系人电话
    /// </summary>
    [JsonPropertyName("ethic_committee_phone")]

    public string? ethic_committee_phone { get; set; }

    /// <summary>
    /// 伦理委员会联系人邮箱
    /// </summary>
    [JsonPropertyName("ethic_committee_email")]

    public string? ethic_committee_email { get; set; }

    /// <summary>
    /// 国家药监局批准文号
    /// </summary>
    [JsonPropertyName("mpa_approved_no")]
    [JsonIgnore]

    public string? mpa_approved_no { get; set; }

    /// <summary>
    /// 国家药监局批准日期
    /// </summary>
    [JsonPropertyName("mpa_approved_date")]
    [JsonIgnore]
    public DateTime? mpa_approved_date { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位（中文）
    /// </summary>
    [JsonPropertyName("primary_sponsor_zh")]

    public string? primary_sponsor_zh { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位（英文）
    /// </summary>
    [JsonPropertyName("primary_sponsor_en")]

    public string? primary_sponsor_en { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位地址（中文）
    /// </summary>
    [JsonPropertyName("primary_sponsor_address_zh")]

    public string? primary_sponsor_address_zh { get; set; }

    /// <summary>
    /// 研究实施负责（组长）单位地址（英文）
    /// </summary>
    [JsonPropertyName("primary_sponsor_address_en")]

    public string? primary_sponsor_address_en { get; set; }

    /// <summary>
    /// 经费或物资来源（中文）
    /// </summary>
    [JsonPropertyName("funding_source_zh")]

    public string? funding_source_zh { get; set; }

    /// <summary>
    /// 经费或物资来源（英文）
    /// </summary>
    [JsonPropertyName("funding_source_en")]

    public string? funding_source_en { get; set; }

    /// <summary>
    /// 研究疾病（中文）
    /// </summary>
    [JsonPropertyName("target_disease_zh")]

    public string? target_disease_zh { get; set; }

    /// <summary>
    /// 研究疾病（英文）
    /// </summary>
    [JsonPropertyName("target_disease_en")]

    public string? target_disease_en { get; set; }

    /// <summary>
    /// 研究疾病代码
    /// </summary>
    [JsonPropertyName("target_disease_code")]

    public string? target_disease_code { get; set; }

    /// <summary>
    /// 研究类型
    /// </summary>
    [JsonPropertyName("study_type")]
    public string? study_type { get; set; }

    /// <summary>
    /// 研究设计
    /// </summary>
    [JsonPropertyName("study_design")]
    public string? study_design { get; set; }

    /// <summary>
    /// 研究所处阶段
    /// </summary>
    [JsonPropertyName("study_phase")]
    public string? study_phase { get; set; }

    /// <summary>
    /// 研究目的（中文）
    /// </summary>
    [JsonPropertyName("study_objectives_zh")]

    public string? study_objectives_zh { get; set; }

    /// <summary>
    /// 研究目的（英文）
    /// </summary>
    [JsonPropertyName("study_objectives_en")]

    public string? study_objectives_en { get; set; }

    /// <summary>
    /// 药物成份或治疗方案详述（中文）
    /// </summary>
    [JsonPropertyName("treatment_description_zh")]

    public string? treatment_description_zh { get; set; }

    /// <summary>
    /// 药物成份或治疗方案详述（英文）
    /// </summary>
    [JsonPropertyName("treatment_description_en")]

    public string? treatment_description_en { get; set; }

    /// <summary>
    /// 纳入标准（中文）
    /// </summary>
    [JsonPropertyName("inclusion_criteria_zh")]

    public string? inclusion_criteria_zh { get; set; }

    /// <summary>
    /// 纳入标准（英文）
    /// </summary>
    [JsonPropertyName("inclusion_criteria_en")]

    public string? inclusion_criteria_en { get; set; }

    /// <summary>
    /// 排除标准（中文）
    /// </summary>
    [JsonPropertyName("exclusion_criteria_zh")]

    public string? exclusion_criteria_zh { get; set; }

    /// <summary>
    /// 排除标准（英文）
    /// </summary>
    [JsonPropertyName("exclusion_criteria_en")]

    public string? exclusion_criteria_en { get; set; }

    /// <summary>
    /// 研究实施时间开始
    /// </summary>
    [JsonPropertyName("study_time_start")]
    public DateTime? study_time_start { get; set; }

    /// <summary>
    /// 研究实施时间结束
    /// </summary>
    [JsonPropertyName("study_time_end")]
    public DateTime? study_time_end { get; set; }

    /// <summary>
    /// 征募观察对象时间开始
    /// </summary>
    [JsonPropertyName("recruiting_time_start")]
    public DateTime? recruiting_time_start { get; set; }

    /// <summary>
    /// 征募观察对象时间结束
    /// </summary>
    [JsonPropertyName("recruiting_time_end")]
    public DateTime? recruiting_time_end { get; set; }

    /// <summary>
    /// 金标准或参考标准（中文）
    /// </summary>
    [JsonPropertyName("gold_standard_zh")]
    public string? gold_standard_zh { get; set; }

    /// <summary>
    /// 金标准或参考标准（英文）
    /// </summary>
    [JsonPropertyName("gold_standard_en")]
    public string? gold_standard_en { get; set; }

    /// <summary>
    /// 指标试验（中文）
    /// </summary>
    [JsonPropertyName("index_test_zh")]
    public string? index_test_zh { get; set; }

    /// <summary>
    /// 指标试验（英文）
    /// </summary>
    [JsonPropertyName("index_test_en")]
    public string? index_test_en { get; set; }

    /// <summary>
    /// 目标人群（中文）
    /// </summary>
    [JsonPropertyName("target_condition_zh")]

    public string? target_condition_zh { get; set; }

    /// <summary>
    /// 目标人群（英文）
    /// </summary>
    [JsonPropertyName("target_condition_en")]

    public string? target_condition_en { get; set; }

    /// <summary>
    /// 目标人群例数
    /// </summary>
    [JsonPropertyName("target_sample_size")]
    public string? target_sample_size { get; set; }

    /// <summary>
    /// 容易混淆的疾病人群（中文）
    /// </summary>
    [JsonPropertyName("confounding_condition_zh")]

    public string? confounding_condition_zh { get; set; }

    /// <summary>
    /// 容易混淆的疾病人群（英文）
    /// </summary>
    [JsonPropertyName("confounding_condition_en")]

    public string? confounding_condition_en { get; set; }

    /// <summary>
    /// 容易混淆的疾病人群例数
    /// </summary>
    [JsonPropertyName("confounding_sample_size")]

    public string? confounding_sample_size { get; set; }

    /// <summary>
    /// 干预措施样本总量
    /// </summary>
    [JsonPropertyName("intervention_total_sample_size")]

    public string? intervention_total_sample_size { get; set; }

    /// <summary>
    /// 征募研究对象状况
    /// </summary>
    [JsonPropertyName("recruiting_status")]

    public string? recruiting_status { get; set; }

    /// <summary>
    /// 年龄范围开始
    /// </summary>
    [JsonPropertyName("age_range_min")]
    public string? age_range_min { get; set; }

    /// <summary>
    /// 年龄范围结束
    /// </summary>
    [JsonPropertyName("age_range_max")]
    public string? age_range_max { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    [JsonPropertyName("gender")]
    public string? gender { get; set; }

    /// <summary>
    /// 随机方法（请说明由何人用什么方法产生随机序列）（中文）
    /// </summary>
    [JsonPropertyName("randomization_procedure_zh")]

    public string? randomization_procedure_zh { get; set; }

    /// <summary>
    /// 随机方法（请说明由何人用什么方法产生随机序列）（英文）
    /// </summary>
    [JsonPropertyName("randomization_procedure_en")]

    public string? randomization_procedure_en { get; set; }

    /// <summary>
    /// 研究对象是否签署知情同意书
    /// </summary>
    [JsonPropertyName("sign_informed_consent")]
    [JsonIgnore]

    public string? sign_informed_consent { get; set; }

    /// <summary>
    /// 随访时间
    /// </summary>
    [JsonPropertyName("follow_up_length")]
    // [JsonIgnore]

    public string? follow_up_length { get; set; }

    /// <summary>
    /// 随访时间单位
    /// </summary>
    [JsonPropertyName("follow_up_unit")]
    public string? follow_up_unit { get; set; }

    /// <summary>
    /// 隐蔽分组方法和过程（中文）
    /// </summary>
    [JsonPropertyName("allocation_concealment_zh")]
    [JsonIgnore]

    public string? allocation_concealment_zh { get; set; }

    /// <summary>
    /// 隐蔽分组方法和过程（英文）
    /// </summary>
    [JsonPropertyName("allocation_concealment_en")]
    [JsonIgnore]

    public string? allocation_concealment_en { get; set; }

    /// <summary>
    /// 盲法（中文）
    /// </summary>
    [JsonPropertyName("blinding_zh")]
    public string? blinding_zh { get; set; }

    /// <summary>
    /// 盲法（英文）
    /// </summary>
    [JsonPropertyName("blinding_en")]
    public string? blinding_en { get; set; }

    /// <summary>
    /// 揭盲或破盲原则和方法（中文）
    /// </summary>
    [JsonPropertyName("unblinding_rules_zh")]
    [JsonIgnore]

    public string? unblinding_rules_zh { get; set; }

    /// <summary>
    /// 揭盲或破盲原则和方法（英文）
    /// </summary>
    [JsonPropertyName("unblinding_rules_en")]
    [JsonIgnore]

    public string? unblinding_rules_en { get; set; }

    /// <summary>
    /// 统计方法名称（中文）
    /// </summary>
    [JsonPropertyName("statistical_methods_zh")]
    [JsonIgnore]

    public string? statistical_methods_zh { get; set; }

    /// <summary>
    /// 统计方法名称（英文）
    /// </summary>
    [JsonPropertyName("statistical_methods_en")]
    [JsonIgnore]

    public string? statistical_methods_en { get; set; }

    /// <summary>
    /// 试验完成后的统计结果（中文）
    /// </summary>
    [JsonPropertyName("calculated_results_zh")]
    [JsonIgnore]

    public string? calculated_results_zh { get; set; }

    /// <summary>
    /// 试验完成后的统计结果（英文）
    /// </summary>
    [JsonPropertyName("calculated_results_en")]
    [JsonIgnore]

    public string? calculated_results_en { get; set; }

    /// <summary>
    /// 是否公开试验完成后的统计结果
    /// </summary>
    [JsonPropertyName("calculated_results_public")]
    [JsonIgnore]

    public string? calculated_results_public { get; set; }

    /// <summary>
    /// 全球唯一识别码
    /// </summary>
    [JsonPropertyName("utn")]
    [JsonIgnore]
    public string? utn { get; set; }

    /// <summary>
    /// 是否共享原始数据
    /// </summary>
    [JsonPropertyName("ipd_sharing")]
    public string? ipd_sharing { get; set; }

    /// <summary>
    /// 共享原始数据的方式（中文）
    /// </summary>
    [JsonPropertyName("ipd_sharing_way_zh")]

    public string? ipd_sharing_way_zh { get; set; }

    /// <summary>
    /// 共享原始数据的方式（英文）
    /// </summary>
    [JsonPropertyName("ipd_sharing_way_en")]

    public string? ipd_sharing_way_en { get; set; }

    /// <summary>
    /// 数据采集和管理（中文）
    /// </summary>
    [JsonPropertyName("data_collection_zh")]

    public string? data_collection_zh { get; set; }

    /// <summary>
    /// 数据采集和管理（英文）
    /// </summary>
    [JsonPropertyName("data_collection_en")]

    public string? data_collection_en { get; set; }

    /// <summary>
    /// 数据与安全监察委员会
    /// </summary>
    [JsonPropertyName("safety_committee")]
    public string? safety_committee { get; set; }

    /// <summary>
    /// 研究计划书或研究结果报告发表信息（中文）
    /// </summary>
    [JsonPropertyName("publication_info_zh")]

    public string? publication_info_zh { get; set; }

    /// <summary>
    /// 研究计划书或研究结果报告发表信息（英文）
    /// </summary>
    [JsonPropertyName("publication_info_en")]

    public string? publication_info_en { get; set; }

    [JsonPropertyName("first_submit_time")]
    public DateTimeOffset? first_submit_time { get; set; }

    [JsonPropertyName("send_number_time")] public DateTimeOffset? send_number_time { get; set; }

    /// <summary>
    /// 采集人体标本
    /// </summary>
    public List<ProjectHumanSampleDto> humanSample { get; set; } = new List<ProjectHumanSampleDto>();

    /// <summary>
    /// 干预措施
    /// </summary>
    public List<ProjectInterventionDto> intervention { get; set; } = new List<ProjectInterventionDto>();

    /// <summary>
    /// 试验主办单位
    /// </summary>
    public List<ProjectSponsorDto> sponsor { get; set; } = new List<ProjectSponsorDto>();

    /// <summary>
    /// 研究实施地点
    /// </summary>
    public List<ProjectResearchSiteDto> researchSite { get; set; } = new List<ProjectResearchSiteDto>();

    /// <summary>
    /// 测量指标
    /// </summary>
    public List<ProjectMeasurementDto> measurement { get; set; } = new List<ProjectMeasurementDto>();
}

/// <summary>
/// 采集人体标本
/// </summary>
public class ProjectHumanSampleDto
{
    /// <summary>
    /// 标本中文名（中文）
    /// </summary>
    [JsonPropertyName("sample_name_zh")]
    public string? sample_name_zh { get; set; }

    /// <summary>
    /// 标本中文名（英文）
    /// </summary>
    [JsonPropertyName("sample_name_en")]
    public string? sample_name_en { get; set; }

    /// <summary>
    /// 组织（中文）
    /// </summary>
    [JsonPropertyName("tissue_zh")]
    public string? tissue_zh { get; set; }

    /// <summary>
    /// 组织（英文）
    /// </summary>
    [JsonPropertyName("tissue_en")]
    public string? tissue_en { get; set; }

    /// <summary>
    /// 人体标本去向
    /// </summary>
    [JsonPropertyName("fate_of_sample")]
    public string? fate_of_sample { get; set; }

    /// <summary>
    /// 说明（中文）
    /// </summary>
    [JsonPropertyName("sample_note_zh")]
    public string? sample_note_zh { get; set; }

    /// <summary>
    /// 说明（英文）
    /// </summary>
    [JsonPropertyName("sample_note_en")]
    public string? sample_note_en { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("row_index")]
    public int? RowIndex { get; set; }
}

/// <summary>
/// 干预措施
/// </summary>
public class ProjectInterventionDto
{
    /// <summary>
    /// 组别（中文）
    /// </summary>
    [JsonPropertyName("intervention_group_zh")]
    public string? intervention_group_zh { get; set; }

    /// <summary>
    /// 组别（英文）
    /// </summary>
    [JsonPropertyName("intervention_group_en")]
    public string? intervention_group_en { get; set; }

    /// <summary>
    /// 样本量
    /// </summary>
    [JsonPropertyName("intervention_sample_size")]
    public string? intervention_sample_size { get; set; }

    /// <summary>
    /// 干预措施（中文）
    /// </summary>
    [JsonPropertyName("intervention_name_zh")]
    public string? intervention_name_zh { get; set; }

    /// <summary>
    /// 干预措施（英文）
    /// </summary>
    [JsonPropertyName("intervention_name_en")]
    public string? intervention_name_en { get; set; }

    /// <summary>
    /// 干预措施代码
    /// </summary>
    [JsonPropertyName("intervention_code")]
    public string? intervention_code { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("row_index")]
    public int? RowIndex { get; set; }
}

/// <summary>
/// 试验主办单位
/// </summary>
public class ProjectSponsorDto
{
    /// <summary>
    /// 国家（中文）
    /// </summary>
    [JsonPropertyName("sponsor_country_zh")]
    public string? sponsor_country_zh { get; set; }

    /// <summary>
    /// 国家（英文）
    /// </summary>
    [JsonPropertyName("sponsor_country_en")]
    public string? sponsor_country_en { get; set; }

    /// <summary>
    /// 省(直辖市)（中文）
    /// </summary>
    [JsonPropertyName("sponsor_province_zh")]
    public string? sponsor_province_zh { get; set; }

    /// <summary>
    /// 省(直辖市)（英文）
    /// </summary>
    [JsonPropertyName("sponsor_province_en")]
    public string? sponsor_province_en { get; set; }

    /// <summary>
    /// 市(区县)（中文）
    /// </summary>
    [JsonPropertyName("sponsor_city_zh")]
    public string? sponsor_city_zh { get; set; }

    /// <summary>
    /// 市(区县)（英文）
    /// </summary>
    [JsonPropertyName("sponsor_city_en")]
    public string? sponsor_city_en { get; set; }

    /// <summary>
    /// 单位（中文）
    /// </summary>
    [JsonPropertyName("sponsor_institution_zh")]
    public string? sponsor_institution_zh { get; set; }

    /// <summary>
    /// 单位（英文）
    /// </summary>
    [JsonPropertyName("sponsor_institution_en")]
    public string? sponsor_institution_en { get; set; }

    /// <summary>
    /// 具体地址（中文）
    /// </summary>
    [JsonPropertyName("sponsor_address_zh")]
    public string? sponsor_address_zh { get; set; }

    /// <summary>
    /// 具体地址（英文）
    /// </summary>
    [JsonPropertyName("sponsor_address_en")]
    public string? sponsor_address_en { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("row_index")]
    public int? RowIndex { get; set; }
}

/// <summary>
/// 研究实施地点
/// </summary>
public class ProjectResearchSiteDto
{
    /// <summary>
    /// 国家（中文）
    /// </summary>
    [JsonPropertyName("site_country_zh")]
    public string? site_country_zh { get; set; }

    /// <summary>
    /// 国家（英文）
    /// </summary>
    [JsonPropertyName("site_country_en")]
    public string? site_country_en { get; set; }

    /// <summary>
    /// 省(直辖市)（中文）
    /// </summary>
    [JsonPropertyName("site_province_zh")]
    public string? site_province_zh { get; set; }

    /// <summary>
    /// 省(直辖市)（英文）
    /// </summary>
    [JsonPropertyName("site_province_en")]
    public string? site_province_en { get; set; }

    /// <summary>
    /// 市(区县)（中文）
    /// </summary>
    [JsonPropertyName("site_city_zh")]
    public string? site_city_zh { get; set; }

    /// <summary>
    /// 市(区县)（英文）
    /// </summary>
    [JsonPropertyName("site_city_en")]
    public string? site_city_en { get; set; }

    /// <summary>
    /// 单位(医院)（中文）
    /// </summary>
    [JsonPropertyName("site_institution_zh")]
    public string? site_institution_zh { get; set; }

    /// <summary>
    /// 单位(医院)（英文）
    /// </summary>
    [JsonPropertyName("site_institution_en")]
    public string? site_institution_en { get; set; }

    /// <summary>
    /// 单位级别（中文）
    /// </summary>
    [JsonPropertyName("site_level_zh")]
    public string? site_level_zh { get; set; }

    /// <summary>
    /// 单位级别（英文）
    /// </summary>
    [JsonPropertyName("site_level_en")]
    public string? site_level_en { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("row_index")]
    public int? RowIndex { get; set; }
}

/// <summary>
/// 测量指标
/// </summary>
public class ProjectMeasurementDto
{
    /// <summary>
    /// 指标中文名（中文）
    /// </summary>
    [JsonPropertyName("outcome_name_zh")]
    public string? outcome_name_zh { get; set; }

    /// <summary>
    /// 指标中文名（英文）
    /// </summary>
    [JsonPropertyName("outcome_name_en")]
    public string? outcome_name_en { get; set; }

    /// <summary>
    /// 指标类型
    /// </summary>
    [JsonPropertyName("outcome_type")]
    public string? outcome_type { get; set; }

    /// <summary>
    /// 测量时间点（中文）
    /// </summary>
    [JsonPropertyName("measure_time_point_zh")]
    public string? measure_time_point_zh { get; set; }

    /// <summary>
    /// 测量时间点（英文）
    /// </summary>
    [JsonPropertyName("measure_time_point_en")]
    public string? measure_time_point_en { get; set; }

    /// <summary>
    /// 测量方法（中文）
    /// </summary>
    [JsonPropertyName("measure_method_zh")]
    public string? measure_method_zh { get; set; }

    /// <summary>
    /// 测量方法（英文）
    /// </summary>
    [JsonPropertyName("measure_method_en")]
    public string? measure_method_en { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    [JsonPropertyName("row_index")]
    public int? RowIndex { get; set; }
}

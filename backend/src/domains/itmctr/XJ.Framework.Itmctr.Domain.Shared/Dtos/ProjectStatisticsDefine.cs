using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Itmctr.Domain.Shared.Enums;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class ProjectStatisticsDefine
{
    public string Title { get; set; } = null!;
    public string Route { get; set; } = null!;

    public string? CurrentUserIdFieldName { get; set; } = null!;

    public List<FormInstanceStatus>? Status { get; set; }
    public bool UsingNewest { get; set; } = true;

    public bool Mgt { get; set; } = false;
    public List<FormDataDynamicQuery> FormDataQueries { get; set; } = new();

    public string? Color { get; set; }
}

public static class ProjectStatisticsDefineExtensions
{
    public readonly static Dictionary<ProjectStatisticsEnum, ProjectStatisticsDefine> Map = new()
    {
        {
            ProjectStatisticsEnum.ProjectSystemAllList, new()
            {
                Title = "所有项目",
                Route = "projectSystemAllList",
                Status = null,
                FormDataQueries = [],
                Mgt = true,
                UsingNewest = false,
                Color = "#ff4500"
            }
        },
        {
            ProjectStatisticsEnum.UserAll, new()
            {
                Title = "我的项目",
                Route = "projectUserAllList",
                Status = null,
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide))
                ],
                Mgt = false,
                UsingNewest = true,
                Color = "#ff4500"
            }
        },
        {
            ProjectStatisticsEnum.UserPendingSubmit, new()
            {
                Title = "待提交项目",
                Route = "projectUserPendingSubmit",
                Status = [FormInstanceStatus.Draft, FormInstanceStatus.Rejected],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide)),
                    new(key: "EditProcessStatus", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectEditProcessStatusEnum.Approved)),
                    new(key: "EditProcessStatus", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectEditProcessStatusEnum.PendingFirstConfirmation))
                ],
                Mgt = false,
                UsingNewest = true,
                Color = "#ff8c00"
            }
        },
        {
            ProjectStatisticsEnum.UserPendingApproval, new()
            {
                Title = "待审核项目",
                Route = "projectUserPendingApproval",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide))
                ],
                Mgt = false,
                UsingNewest = true,
                Color = "#ffd700"
            }
        },
        {
            ProjectStatisticsEnum.UserApproved, new()
            {
                Title = "已通过项目",
                Route = "projectUserApprovedList",
                Status = [FormInstanceStatus.Confirmed, FormInstanceStatus.Draft],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.Approved))
                ],
                Mgt = false,
                UsingNewest = true,
                Color = "#90ee90"
            }
        },

        {
            ProjectStatisticsEnum.Level1PendingJudge, new()
            {
                Title = "待判断项目",
                Route = "projectSystemPendingJudgeList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Empty, value: "")
                ],
                Mgt = true,
                UsingNewest = true,
                Color = "#00ced1"
            }
        },
        {
            ProjectStatisticsEnum.Level1PendingSendNumber, new()
            {
                Title = "待发号项目",
                Route = "projectSystemPendingSendNumberList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingFirstApproval)),
                    new(key: "RegistrationNumber", @operator: FormDataQueryOperator.Empty,
                        value: "")
                ],
                // CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#1e90ff"
            }
        },
        {
            ProjectStatisticsEnum.Level1ApplyEditPendingConfirmation, new()
            {
                Title = "再修改申请项目",
                Route = "projectSystemApplyEditList",
                Status = [FormInstanceStatus.Draft],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.Approved)),
                    new(key: "EditProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectEditProcessStatusEnum.PendingFirstConfirmation))
                ],
                // CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#c71585"
            }
        },
        {
            ProjectStatisticsEnum.Level1ApplyEditPendingApproval, new()
            {
                Title = "再修改复核项目",
                Route = "projectSystemPendingReviewList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingFirstApproval)),
                    new(key: "RegistrationNumber", @operator: FormDataQueryOperator.NotEmpty,
                        value: "")
                ],
                // CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#c7158577"
            }
        },
        {
            ProjectStatisticsEnum.Level1ApplyEditReturned, new()
            {
                Title = "再修改退回项目",
                Route = "projectSystemReturnEditList",
                Status = [FormInstanceStatus.Draft, FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.Approved)),
                    new(key: "EditProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectEditProcessStatusEnum.Rejected))
                ],
                Mgt = true,
                UsingNewest = true,
                Color = "#ff4500"
            }
        },
        {
            ProjectStatisticsEnum.Level1SentNumber, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.Approved))
                ],
                // CurrentUserIdFieldName = "FirstApprovalUserId",
                Mgt = true,
                UsingNewest = false,
                Color = "#ff8c00"
            }
        },
        {
            ProjectStatisticsEnum.Level1NonTraditional, new()
            {
                Title = "已终止项目",
                Route = "projectSystemNonTraditionalList",
                Status = [FormInstanceStatus.Submitted, FormInstanceStatus.Rejected],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Termination)),

                    // new(key: "ProjectTerminationCause", @operator: FormDataQueryOperator.Equal,
                    //     value: nameof(ProjectTerminationCauseEnum.NonTraditionalProject))
                ],
                Mgt = true,
                UsingNewest = true,
                Color = "#ffd700"
            }
        },
        {
            ProjectStatisticsEnum.Level1AllSubmitted, new()
            {
                Title = "审核状态查询",
                Route = "projectSystemAllSubmittedList",
                Status =
                [
                    FormInstanceStatus.Draft, FormInstanceStatus.Submitted, FormInstanceStatus.Rejected,
                    FormInstanceStatus.Confirmed
                ],
                FormDataQueries =
                [
                    new(key: "SubmitTime", @operator: FormDataQueryOperator.NotEmpty, value: ""),
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide))
                ],
                Mgt = true,
                UsingNewest = false,
                Color = "#90ee90"
            }
        },
        {
            ProjectStatisticsEnum.Level1AllAvailableSubmitted, new()
            {
                Title = "可用审核项目",
                Route = "projectSystemAllAvailableSubmittedList",
                Status =
                [
                    FormInstanceStatus.Draft, FormInstanceStatus.Submitted, FormInstanceStatus.Rejected,
                    FormInstanceStatus.Confirmed
                ],
                FormDataQueries =
                [
                    new(key: "SubmitTime", @operator: FormDataQueryOperator.NotEmpty, value: ""),
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide)),
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Termination))
                ],
                Mgt = true,
                UsingNewest = false,
                Color = "#90ee90"
            }
        },

        {
            ProjectStatisticsEnum.Level2PendingAssign, new()
            {
                Title = "待分配项目",
                Route = "projectSystemPendingAssignList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingSecondAssignment))
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#00ced1"
            }
        },
        {
            ProjectStatisticsEnum.Level2PendingReview, new()
            {
                Title = "待核审项目",
                Route = "projectSystemPendingReviewList2",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingSecondApproval)),
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#1e90ff"
            }
        },
        {
            ProjectStatisticsEnum.Level2ReviewReturned, new()
            {
                Title = "已退回项目",
                Route = "projectSystemReviewReturnedList2",
                Status = [FormInstanceStatus.Draft, FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "LastProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingSecondApproval)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.NotEqual,
                        value: nameof(ProjectProcessStatusEnum.PendingFirstApproval))
                ],
                CurrentUserIdFieldName = "LastApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#c71585"
            }
        },
        {
            ProjectStatisticsEnum.Level2PendingApproval, new()
            {
                Title = "已核审通过项目",
                Route = "projectSystemPendingApprovedList2",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingFirstApproval)),
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#c7158577"
            }
        },
        {
            ProjectStatisticsEnum.Level2Approved, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList2",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.Approved))
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = false,
                Color = "#ff4500"
            }
        },
        {
            ProjectStatisticsEnum.Level2ReAssign, new()
            {
                Title = "已分配项目",
                Route = "projectSystemReAssignList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "LastProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingSecondAssignment))
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#ff8c00"
            }
        },
        {
            ProjectStatisticsEnum.Level2AllSubmitted, new()
            {
                Title = "审核状态查询",
                Route = "projectSystemAllSubmittedList2",
                Status =
                [
                    FormInstanceStatus.Draft, FormInstanceStatus.Submitted, FormInstanceStatus.Rejected,
                    FormInstanceStatus.Confirmed
                ],
                FormDataQueries =
                [
                    
                    new(key: "SubmitTime", @operator: FormDataQueryOperator.NotEmpty, value: ""),
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide))
                ],
                CurrentUserIdFieldName = "SecondApprovalUserId",
                Mgt = true,
                UsingNewest = false,
                Color = "#90ee90"
            }
        },
        {
            ProjectStatisticsEnum.Level3PendingAssignReview, new()
            {
                Title = "待分配项目",
                Route = "projectSystemPendingAssignReviewList",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingThirdAssignment))
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#ffd700"
            }
        },
        {
            ProjectStatisticsEnum.Level3PendingReview, new()
            {
                Title = "待复审项目",
                Route = "projectSystemPendingReviewList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingThirdApproval))
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#90ee90"
            }
        },
        {
            ProjectStatisticsEnum.Level3PendingApproval, new()
            {
                Title = "已复审通过项目",
                Route = "projectSystemPendingApprovedList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingSecondApproval))
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#00ced1"
            }
        },
        {
            ProjectStatisticsEnum.Level3ReviewReturned, new()
            {
                Title = "已退回项目",
                Route = "projectSystemReviewReturnedList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "LastProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingThirdApproval)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingFourthApproval))
                ],
                CurrentUserIdFieldName = "LastApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#1e90ff"
            }
        },
        {
            ProjectStatisticsEnum.Level3Approved, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList3",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.Approved))
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = false,
                Color = "#c71585"
            }
        },
        {
            ProjectStatisticsEnum.Level3ReAssign, new()
            {
                Title = "已分配项目",
                Route = "projectSystemReAssignList3",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingFourthApproval)),
                    new(key: "LastProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingThirdAssignment))
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#ff8c00"
            }
        },
        {
            ProjectStatisticsEnum.Level3AllSubmitted, new()
            {
                Title = "审核状态查询",
                Route = "projectSystemAllSubmittedList3",
                Status =
                [
                    FormInstanceStatus.Draft, FormInstanceStatus.Submitted, FormInstanceStatus.Rejected,
                    FormInstanceStatus.Confirmed
                ],
                FormDataQueries =
                [
                    new(key: "SubmitTime", @operator: FormDataQueryOperator.NotEmpty, value: ""),
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.NotEqualOrNotExist,
                        value: nameof(ProjectTagEnum.Hide))
                ],
                CurrentUserIdFieldName = "ThirdApprovalUserId",
                Mgt = true,
                UsingNewest = false,
                Color = "#90ee90"
            }
        },

        {
            ProjectStatisticsEnum.Level4PendingReview, new()
            {
                Title = "待初审项目",
                Route = "projectSystemPendingReviewList4",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.PendingFourthApproval))
                ],
                CurrentUserIdFieldName = "FourthApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#c7158577"
            }
        },
        {
            ProjectStatisticsEnum.Level4PendingApproval, new()
            {
                Title = "已初审通过项目",
                Route = "projectSystemPendingApprovedList4",
                Status = [FormInstanceStatus.Submitted],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.NotEqual,
                        value: nameof(ProjectProcessStatusEnum.RejectToApply)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.NotEqual,
                        value: nameof(ProjectProcessStatusEnum.PendingFourthApproval)),
                ],
                CurrentUserIdFieldName = "LastApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#ff4500"
            }
        },
        {
            ProjectStatisticsEnum.Level4ReviewReturned, new()
            {
                Title = "已退回项目",
                Route = "projectSystemReviewReturnedList4",
                Status = [FormInstanceStatus.Rejected, FormInstanceStatus.Draft],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                    new(key: "ProcessStatus", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectProcessStatusEnum.RejectToApply))
                ],
                CurrentUserIdFieldName = "FourthApprovalUserId",
                Mgt = true,
                UsingNewest = true,
                Color = "#ff8c00"
            }
        },
        {
            ProjectStatisticsEnum.Level4Approved, new()
            {
                Title = "已发号项目",
                Route = "projectSystemApprovedList4",
                Status = [FormInstanceStatus.Confirmed],
                FormDataQueries =
                [
                    new(key: "ProjectTag", @operator: FormDataQueryOperator.Equal,
                        value: nameof(ProjectTagEnum.Normal)),
                ],
                CurrentUserIdFieldName = "FourthApprovalUserId",
                Mgt = true,
                UsingNewest = false,
                Color = "#ffd700"
            }
        }
    };
}

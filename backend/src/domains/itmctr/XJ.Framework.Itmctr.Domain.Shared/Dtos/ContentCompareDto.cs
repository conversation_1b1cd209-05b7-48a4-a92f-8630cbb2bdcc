using System.Text;
using System.Text.Json.Serialization;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class ContentCompareResponseDto
{
    [JsonPropertyName("code")] public int Code { get; set; }
    [JsonPropertyName("msg")] public string Msg { get; set; } = null!;
    [JsonPropertyName("task_id")] public string TaskId { get; set; } = null!;
}

public class ContentCompareDto : FormRecognitionResponse
{
    [JsonPropertyName("data")] public List<CompareItemDto> Data { get; set; } = new List<CompareItemDto>();
}

public class CompareItemDto
{
    [JsonPropertyName("item_code")] public string ItemCode { get; set; } = null!;
    [JsonPropertyName("item_name")] public string ItemName { get; set; } = null!;
    [JsonPropertyName("item_type")] public string ItemType { get; set; } = null!;

    [JsonPropertyName("compare_results")]
    public List<CompareResultDto> CompareResults { get; set; } = new List<CompareResultDto>();
}

public class CompareResultDto
{
    [JsonPropertyName("index")] public int? Index { get; set; }
    [JsonPropertyName("input")] public string Input { get; set; } = null!;

    [JsonPropertyName("if_match")] public string IfMatch { get; set; } = null!;
    [JsonPropertyName("score")] public double Score { get; set; }
    [JsonPropertyName("comment")] public string Comment { get; set; } = null!;
    [JsonPropertyName("references_count")] public int ReferencesCount { get; set; }
    [JsonPropertyName("references")] public List<ReferenceDto> References { get; set; } = new List<ReferenceDto>();
}

public class ReferenceDto
{
    [JsonPropertyName("filename")] public string FileName { get; set; } = null!;
    [JsonPropertyName("text")] public string Text { get; set; } = null!;
    [JsonPropertyName("chunks")] public List<ChunkDto> Chunks { get; set; } = new List<ChunkDto>();
}

public class ChunkDto
{
    [JsonPropertyName("page_num")] public int PageNum { get; set; }
    [JsonPropertyName("coordinate")] public CoordinateDto Coordinate { get; set; } = new CoordinateDto();
}

public class CoordinateDto
{
    [JsonPropertyName("x0")] public double X0 { get; set; }
    [JsonPropertyName("y0")] public double Y0 { get; set; }
    [JsonPropertyName("x1")] public double X1 { get; set; }
    [JsonPropertyName("y1")] public double Y1 { get; set; }
}

public class ContentCompareRequestDto
{
    [JsonPropertyName("task_id")] public string TaskId { get; set; } = null!;
    [JsonPropertyName("data")] public List<ParseItemDto> Data { get; set; } = new List<ParseItemDto>();
}

public class FullContentTranslateRequestDto
{
    [JsonPropertyName("data")] public List<ParseItemDto> Data { get; set; } = new List<ParseItemDto>();
}
using System.Text.Json.Serialization;

namespace XJ.Framework.Itmctr.Domain.Shared.Dtos;

public class AttachmentParseResponseDto
{
    [JsonPropertyName("code")] public int Code { get; set; }
    [JsonPropertyName("msg")] public string Msg { get; set; } = null!;
    [JsonPropertyName("task_id")] public string TaskId { get; set; } = null!;
}

public class AttachmentParseDto : FormRecognitionResponse
{
    [JsonPropertyName("data")] public List<ParseItemDto> Data { get; set; } = new List<ParseItemDto>();
}

public class ParseItemDto
{
    [JsonPropertyName("item_code")] public string ItemCode { get; set; } = null!;
    [JsonPropertyName("item_name")] public string ItemName { get; set; } = null!;
    [JsonPropertyName("item_type")] public string ItemType { get; set; } = null!;

    [JsonPropertyName("contents")]
    public List<ParseItemContentDto> Contents { get; set; } = new List<ParseItemContentDto>();
}

public class ParseItemContentDto
{
    [JsonPropertyName("index")] public int? Index { get; set; }
    [JsonPropertyName("text")] public string? Text { get; set; }
    [JsonPropertyName("slots")] public List<ParseItemContentSlotDto>? Slots { get; set; }
}

public class ParseItemContentSlotDto
{
    [JsonPropertyName("slot_code")] public string SlotCode { get; set; } = null!;
    [JsonPropertyName("slot_name")] public string SlotName { get; set; } = null!;
    [JsonPropertyName("value")] public string? Value { get; set; }
}
using XJ.Framework.Library.Common.Abstraction.Reflection;

namespace XJ.Framework.Itmctr.Domain.Shared.Enums;

public enum ProjectProcessStatusEnum
{
    /// <summary>
    /// 待四级审批
    /// </summary>
    [EnumItemDescription("待四级审批")] PendingFourthApproval,

    /// <summary>
    /// 待二级审批
    /// </summary>
    [EnumItemDescription("待二级审批")] PendingSecondApproval,

    /// <summary>
    /// 待三级审批
    /// </summary>
    [EnumItemDescription("待三级审批")] PendingThirdApproval,

    /// <summary>
    /// 待一级审批
    /// </summary>
    [EnumItemDescription("待一级审批")] PendingFirstApproval,

    /// <summary>
    /// 已审批
    /// </summary>
    [EnumItemDescription("已审批")] Approved,

    /// <summary>
    /// 退回到发起人
    /// </summary>
    [EnumItemDescription("退回到发起人")] RejectToApply,

    /// <summary>
    /// 待二级分配
    /// </summary>
    [EnumItemDescription("待二级分配")] PendingSecondAssignment,

    /// <summary>
    /// 待三级分配
    /// </summary>
    [EnumItemDescription("待三级分配")] PendingThirdAssignment,
}


namespace XJ.Framework.Files.WebApi.Controllers;

/// <summary>
/// FileTypeStorageRel 控制器
/// </summary>

public class FileTypeStorageRelController : BaseEditableAppController<long, FileTypeStorageRelDto, FileTypeStorageRelOperationDto, IFileTypeStorageRelService, FileTypeStorageRelQueryCriteria>
{
    public FileTypeStorageRelController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(FileTypeStorageRelOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<FileTypeStorageRelDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, FileTypeStorageRelOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, FileTypeStorageRelDto>> GetPageAsync([FromQuery] PagedQueryCriteria<FileTypeStorageRelQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<FileTypeStorageRelDto>> GetListAsync([FromQuery] FileTypeStorageRelQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}

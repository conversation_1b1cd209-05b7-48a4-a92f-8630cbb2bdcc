using XJ.Framework.Rbac.ApiClient;

namespace XJ.Framework.Files.WebApi;

public class FilesWebApiMgtWrapper : WebApiMgtWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<FilesApplicationWrapper, FilesInfrastructureWrapper>(configuration);
        services.AddHttpClient<UserApiClient>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
        
    }
} 
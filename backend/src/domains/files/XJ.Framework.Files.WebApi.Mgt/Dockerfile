FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app

EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制解决方案文件
COPY ["Common.props", "./"]
COPY ["Common.Secrets.props", "./"]
COPY ["Directory.Build.props", "./"]
COPY ["Directory.Packages.props", "./"]

# 复制项目文件
COPY ["src/domains/files/XJ.Framework.Files.WebApi.Mgt/XJ.Framework.Files.WebApi.Mgt.csproj", "src/domains/files/XJ.Framework.Files.WebApi.Mgt/"]
COPY ["src/domains/files/XJ.Framework.Files.Application/XJ.Framework.Files.Application.csproj", "src/domains/files/XJ.Framework.Files.Application/"]
COPY ["src/domains/files/XJ.Framework.Files.Domain/XJ.Framework.Files.Domain.csproj", "src/domains/files/XJ.Framework.Files.Domain/"]
COPY ["src/domains/files/XJ.Framework.Files.EntityFrameworkCore/XJ.Framework.Files.EntityFrameworkCore.csproj", "src/domains/files/XJ.Framework.Files.EntityFrameworkCore/"]
COPY ["src/shared/XJ.Framework.Library.WebApi/XJ.Framework.Library.WebApi.csproj", "src/shared/XJ.Framework.Library.WebApi/"]
COPY ["src/shared/XJ.Framework.Library.Application.Contract/XJ.Framework.Library.Application.Contract.csproj", "src/shared/XJ.Framework.Library.Application.Contract/"]
COPY ["src/shared/XJ.Framework.Library.Domain.Shared/XJ.Framework.Library.Domain.Shared.csproj", "src/shared/XJ.Framework.Library.Domain.Shared/"]

# 复制配置文件
COPY ["settings/", "settings/"]

# 复制所有源代码
COPY ["src/", "src/"]

# 还原和构建
RUN dotnet restore "src/domains/files/XJ.Framework.Files.WebApi.Mgt/XJ.Framework.Files.WebApi.Mgt.csproj"
RUN dotnet build "src/domains/files/XJ.Framework.Files.WebApi.Mgt/XJ.Framework.Files.WebApi.Mgt.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "src/domains/files/XJ.Framework.Files.WebApi.Mgt/XJ.Framework.Files.WebApi.Mgt.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
COPY --from=build /src/settings /app/settings
ENTRYPOINT ["dotnet", "XJ.Framework.Files.WebApi.Mgt.dll"] 
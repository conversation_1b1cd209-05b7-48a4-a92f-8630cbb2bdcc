
namespace XJ.Framework.Files.Domain.Shared.Dtos;

/// <summary>
/// FileAcl DTO
/// </summary>
public class FileAclDto : BaseDto<long>
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public long FileId { get; set; }

    /// <summary>
    /// 主体类型（user、role、token等）
    /// </summary>
    public string PrincipalType { get; set; } = null!;

    /// <summary>
    /// 主体ID
    /// </summary>
    public string PrincipalId { get; set; } = null!;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTimeOffset? ExpireAt { get; set; }

} 
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Files.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Files.Domain.Entities;

/// <summary>
/// FileInfo 实体
/// </summary>
[Table("file_infos", Schema = "f")]
[SoftDeleteIndex("IX_file_info_file_type_code", nameof(FileTypeCode))]
[SoftDeleteIndex("IX_file_info_uploader_id", nameof(UploaderId))]
public class FileInfoEntity : BaseSoftDeleteEntity<Guid>
{
    /// <summary>
    /// 文件原始名称
    /// </summary>
    [Column("file_name")]
    [StringLength(510)]
    public required string FileName { get; set; } = null!;

    /// <summary>
    /// 文件总大小（字节）
    /// </summary>
    [Column("file_size")]
    public required long FileSize { get; set; }

    /// <summary>
    /// 总计分块数量
    /// </summary>
    [Column("chunk_count")]
    public int ChunkCount { get; set; }

    /// <summary>
    /// 文件类型code
    /// </summary>
    [Column("file_type_code")]
    public required string FileTypeCode { get; set; }

    /// <summary>
    /// 上传用户ID
    /// </summary>
    [Column("uploader_id")]
    [StringLength(100)]
    public long? UploaderId { get; set; }

    /// <summary>
    /// 状态（0-上传中，1-完成，2-删除）
    /// </summary>
    [Column("status")]
    public required FileStatus Status { get; set; }
}

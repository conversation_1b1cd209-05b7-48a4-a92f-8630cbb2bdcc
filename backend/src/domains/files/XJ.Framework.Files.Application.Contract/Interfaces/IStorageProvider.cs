namespace XJ.Framework.Files.Application.Contract.Interfaces;

public interface IStorageProvider : IDisposable
{
    void Initialize(StorageDto configuration);
    Task<Stream> OpenReadAsync(string relativePath, string fileName, CancellationToken cancellationToken = default);

    Task WriteAsync(string relativePath, string fileName, Stream content,
        CancellationToken cancellationToken = default);

    Task WriteAsync(string relativePath, string fileName, byte[] content,
        CancellationToken cancellationToken = default);

    Task<bool> ExistsAsync(string relativePath, string fileName, CancellationToken cancellationToken = default);

    Task DeleteAsync(string relativePath, string fileName, CancellationToken cancellationToken = default);

    Task<FileMetadata> GetMetadataAsync(string path, string fileName, CancellationToken cancellationToken = default);
}
using XJ.Framework.Files.Domain.Entities;

namespace XJ.Framework.Files.Application.Contract.Interfaces;

/// <summary>
/// FileInfo 服务接口
/// </summary>
public interface IFileInfoService :
    IAppService<Guid, FileInfoDto, FileInfoQueryCriteria>,
    IEditableAppService<Guid, FileInfoOperationDto>
{
    Task<bool> CreateAsync(FileInfoEntity entity, bool saveChange = false);
    Task<string> GenerateDownloadTokenAsync(Guid fileId);
    Task<bool> CheckCanAccessAsync(Guid fileId, string fileTypeCode, string? token);
    Task<long> ReCalculateFileSizeAsync(Guid fileId);
}

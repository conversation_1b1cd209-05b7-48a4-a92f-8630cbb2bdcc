namespace XJ.Framework.Files.Application.Contract.OperationDtos;

/// <summary>
/// FileType 操作 DTO
/// </summary>
public class FileTypeOperationDto : BaseOperationDto
{
    /// <summary>
    /// 类型编码
    /// </summary>
    public string TypeCode { get; set; } = null!;

    /// <summary>
    /// 类型名称
    /// </summary>
    public string TypeName { get; set; } = null!;

    /// <summary>
    /// 允许的文件后缀（如.jpg,.png)
    /// </summary>
    public string Extension { get; set; } = null!;

    /// <summary>
    /// 大小限制（字节）
    /// </summary>
    public long SizeLimit { get; set; }

    /// <summary>
    /// 分块大小限制（字节）
    /// </summary>
    public long ChunkLimit { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 所需权限类型 none=无 user=用户 role=角色
    /// </summary>
    public string PermissionRequired { get; set; } = null!;
}

namespace XJ.Framework.Files.Application.Contract.OperationDtos;

/// <summary>
/// FileAcl 操作 DTO
/// </summary>
public class FileAclOperationDto : BaseOperationDto
{
    /// <summary>
    /// 文件ID
    /// </summary>
    public long FileId { get; set; }

    /// <summary>
    /// 主体类型（user、role、token等）
    /// </summary>
    public string PrincipalType { get; set; } = null!;

    /// <summary>
    /// 主体ID
    /// </summary>
    public string PrincipalId { get; set; } = null!;

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTime? ExpireAt { get; set; }

} 
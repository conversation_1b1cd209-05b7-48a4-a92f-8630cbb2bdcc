using XJ.Framework.Files.Domain.Shared.Enums;

namespace XJ.Framework.Files.Application.Contract.OperationDtos;

/// <summary>
/// Storage 操作 DTO
/// </summary>
public class StorageOperationDto : BaseOperationDto
{
    /// <summary>
    /// 存储编码
    /// </summary>
    public required string StorageCode { get; set; } = null!;

    /// <summary>
    /// 存储名称
    /// </summary>
    public string StorageName { get; set; } = null!;

    /// <summary>
    /// 存储服务地址
    /// </summary>
    public string Endpoint { get; set; } = null!;

    /// <summary>
    /// 协议（如oss、s3、ftp）
    /// </summary>
    public StorageProtocol Protocol { get; set; }

    /// <summary>
    /// 访问key
    /// </summary>
    public string AccessKey { get; set; } = null!;

    /// <summary>
    /// 访问secret
    /// </summary>
    public string SecretKey { get; set; } = null!;

    /// <summary>
    /// 桶/容器名
    /// </summary>
    public string Bucket { get; set; } = null!;

    /// <summary>
    /// 备注
    /// </summary>
    public string? Remark { get; set; }
}
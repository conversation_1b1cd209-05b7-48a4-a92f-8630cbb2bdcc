using Microsoft.Extensions.Logging;
using XJ.Framework.Files.Application.Services.StorageProviders;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Common.Abstraction.Exceptions;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Files.Application.Services;

/// <summary>
/// FileInfo 服务实现
/// </summary>
public sealed class FileInfoService :
    BaseEditableAppService<Guid, FileInfoEntity, FileInfoDto, FileInfoOperationDto, IFileInfoRepository,
        FileInfoQueryCriteria>,
    IFileInfoService
{
    private readonly ICurrentUserContext _currentUserContext;
    private readonly IFileDownloadTokenService _fileDownloadTokenService;
    private readonly ILogger<FileInfoService> _logger;
    private readonly IFileTypeService _fileTypeService;
    private readonly IAuthProvider _authProvider;
    private readonly IStorageProviderFactory _storageProviderFactory;
    private readonly IStorageService _storageService;
    private readonly IFileChunkRepository _fileChunkRepository;

    public FileInfoService(IFileInfoRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<Guid> keyGenerator, ICurrentUserContext currentUserContext,
        IFileDownloadTokenService fileDownloadTokenService, ILogger<FileInfoService> logger,
        IFileTypeService fileTypeService, IAuthProvider authProvider, IStorageProviderFactory storageProviderFactory,
        IStorageService storageService, IFileChunkRepository fileChunkRepository) : base(repository, mapper,
        unitOfWork,
        keyGenerator, currentUserContext)
    {
        _currentUserContext = currentUserContext;
        _fileDownloadTokenService = fileDownloadTokenService;
        _logger = logger;
        _fileTypeService = fileTypeService;
        _authProvider = authProvider;
        _storageProviderFactory = storageProviderFactory;
        _storageService = storageService;
        _fileChunkRepository = fileChunkRepository;
    }

    public async Task<bool> CreateAsync(FileInfoEntity entity, bool saveChange = false)
    {
        return await Repository.InsertAsync(entity, saveChange);
    }

    public async Task<string> GenerateDownloadTokenAsync(Guid fileId)
    {
        // 获取当前用户ID
        var userId = _currentUserContext.GetCurrentUserId()!.Value;

        var fileInfo = await GetByIdAsync(fileId);
        fileInfo.NullCheck();

        var fileType = await _fileTypeService.GetByCodeAsync(fileInfo!.FileTypeCode);

        fileType.NullCheck();


        var canAccess = await CheckAccessAclAsync(fileId, userId, fileType!.PermissionRequired);

        if (!canAccess)
        {
            throw new AuthorizationException("没有权限访问该文件/No permission to access this file");
        }

        // 生成令牌
        var token = await _fileDownloadTokenService.GenerateTokenAsync(fileId, userId);

        return token;
    }

    private async Task<long> ValidateTokenAsync(Guid fileId, string token)
    {
        var tokenInfo = await _fileDownloadTokenService.ValidateTokenAsync(token);
        if (tokenInfo == null)
        {
            try
            {
                // 尝试获取当前用户
                var userProfile = await _authProvider.GetUserProfileAsync();
                if (userProfile != null)
                {
                    // 如果当前用户存在，则返回当前用户ID
                    return userProfile.Key;
                }
                else
                {
                    throw new ValidationException("用户不存在/ User does not exist");
                }
            }
            catch (Exception ex)
            {
                throw new ValidationException(
                    "无效的下载令牌或用户不存在/The download link is invalid or the user account doesn't exist", ex);
            }
        }

        if (tokenInfo.FileId != fileId)
        {
            throw new ValidationException("下载令牌验证失败/Download token validation failed");
        }

        if (tokenInfo.ExpireTime < DateTime.Now)
        {
            throw new ValidationException("下载令牌已过期/The download token has expired");
        }

        return tokenInfo.UserId;
    }

    public async Task<bool> CheckCanAccessAsync(Guid fileId, string fileTypeCode, string? token)
    {
        var fileType = await _fileTypeService.GetByCodeAsync(fileTypeCode);

        fileType.NullCheck();

        if (fileType!.PermissionRequired != "none")
        {
            //先验证token 再验证权限
            var userId = await ValidateTokenAsync(fileId, token!);

            if (!await CheckAccessAclAsync(fileId, userId, fileType.PermissionRequired))
            {
                throw new AuthorizationException("没有权限访问该文件/No permission to access this file");
            }
        }

        return true;
    }

    public async Task<long> ReCalculateFileSizeAsync(Guid fileId)
    {
        var fileChunks = (await _fileChunkRepository.GetListAsync(q => q.FileId == fileId))
            .OrderBy(q => q.ChunkIndex).ToList();

        long fileSize = 0;

        await fileChunks.ForEachAsync(async chunk =>
        {
            var storage = await _storageService.GetStorageAsync(chunk.StorageCode);
            var storageProvider = _storageProviderFactory.CreateProvider(storage);
            var meta = await storageProvider.GetMetadataAsync(chunk.StoragePath, chunk.FileName);

            chunk.ChunkSize = meta.Size;
            chunk.ChunkHash = meta.Hash;
            await _fileChunkRepository.UpdateAsync(chunk);

            fileSize += meta.Size;
        });

        var fileInfo = await Repository.GetAsync(fileId);
        fileInfo!.FileSize = fileSize;
        await Repository.UpdateAsync(fileInfo);

        return fileSize;
    }

    private async Task<bool> CheckAccessAclAsync(Guid fileId, long userId, string permissionRequired)
    {
        return await Task.FromResult(true);
    }
}

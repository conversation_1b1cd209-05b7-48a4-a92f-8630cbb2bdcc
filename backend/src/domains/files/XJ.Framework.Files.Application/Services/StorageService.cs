using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Files.Application.Services;

/// <summary>
/// Storage 服务实现
/// </summary>
public sealed class StorageService :
    BaseEditableAppService<long, StorageEntity, StorageDto, StorageOperationDto, IStorageRepository,
        StorageQueryCriteria>,
    IStorageService
{
    private readonly IFileTypeStorageRelRepository _fileTypeStorageRelRepository;
    private const string DEFAULT_STORAGE_CODE = "default";

    public StorageService(IStorageRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext,
        IFileTypeStorageRelRepository fileTypeStorageRelRepository) : base(repository, mapper, unitOfWork, keyGenerator,
        currentUserContext)
    {
        _fileTypeStorageRelRepository = fileTypeStorageRelRepository;
    }

    public async Task<StorageDto> GetStorageAsync(string fileTypeCode)
    {
        var storageRels =
            (await _fileTypeStorageRelRepository.GetListAsync(q =>
                q.FileTypeCode.ToLower().Equals(fileTypeCode.ToLower()))).ToList();
        string? storageCode;
        if (storageRels.Count > 0)
        {
            storageCode = storageRels.OrderBy(q => q.Priority).FirstOrDefault()?.StorageCode;
        }
        else
        {
            storageCode = DEFAULT_STORAGE_CODE;
        }

        if (storageCode == null)
        {
            throw new ValidationException("Storage code not found.");
        }

        var storage = await Repository.GetAsync(q =>
            q.StorageCode.ToLower().Equals(storageCode.ToLower()));

        if (storage == null)
        {
            throw new ValidationException("Storage not found.");
        }

        return (await GetDtoAsync(storage))!;
    }
}
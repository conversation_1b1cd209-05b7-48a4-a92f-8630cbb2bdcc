namespace XJ.Framework.Files.Application.Services.StorageProviders;

public abstract class StorageProviderBase : IStorageProvider
{
    protected StorageDto Configuration { get; private set; } = null!;

    public virtual void Initialize(StorageDto configuration)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
    }

    public abstract Task<Stream> OpenReadAsync(string path, string fileName,
        CancellationToken cancellationToken = default);

    public abstract Task WriteAsync(string path, string fileName, Stream content,
        CancellationToken cancellationToken = default);

    public async virtual Task WriteAsync(string path, string fileName, byte[] content,
        CancellationToken cancellationToken = default)
    {
        using var stream = new MemoryStream(content);
        await WriteAsync(path, fileName, stream, cancellationToken);
    }

    public abstract Task<bool> ExistsAsync(string path, string fileName, CancellationToken cancellationToken = default);

    public abstract Task DeleteAsync(string path, string filename, CancellationToken cancellationToken = default);

    public abstract Task<FileMetadata> GetMetadataAsync(string path, string fileName,
        CancellationToken cancellationToken = default);

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
    }
}
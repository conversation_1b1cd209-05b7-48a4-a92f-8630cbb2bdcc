#pragma warning disable CS1998 // 异步方法缺少 "await" 运算符，将以同步方式运行
namespace XJ.Framework.Files.Application.Services.StorageProviders.Providers;

public class S3StorageProvider : StorageProviderBase
{
    public async override Task<Stream> OpenReadAsync(string path, string fileName, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async override Task WriteAsync(string path, string fileName, Stream content, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async override Task<bool> ExistsAsync(string path, string fileName, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async override Task DeleteAsync(string path, string filename, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }

    public async override Task<FileMetadata> GetMetadataAsync(string path, string fileName, CancellationToken cancellationToken = default)
    {
        throw new NotImplementedException();
    }
}

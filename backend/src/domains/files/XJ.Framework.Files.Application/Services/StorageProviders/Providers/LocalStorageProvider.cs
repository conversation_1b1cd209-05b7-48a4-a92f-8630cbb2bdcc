using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Files.Application.Services.StorageProviders.Providers;

public class LocalStorageProvider : StorageProviderBase
{
    public async override Task<Stream> OpenReadAsync(string path, string fileName,
        CancellationToken cancellationToken = default)
    {
        var fullPath = Path.Combine(Configuration.Endpoint, Configuration.Bucket, path, fileName);
        // Console.WriteLine($@"OpenReadAsync: {fullPath}");
        if (!File.Exists(fullPath))
        {
            throw new FileNotFoundException($"File {fullPath} not found.");
        }
        var stream = new FileStream(fullPath,
            FileMode.Open);
        return await Task.FromResult(stream);
    }

    public async override Task WriteAsync(string path, string fileName, Stream content,
        CancellationToken cancellationToken = default)
    {
        // Ensure directory exists
        Directory.CreateDirectory(Path.Combine(Configuration.Endpoint, Configuration.Bucket, path));
        var filePath = Path.Combine(Configuration.Endpoint, Configuration.Bucket, path, fileName);

        content.Seek(0, SeekOrigin.Begin);

        await using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
        await content.CopyToAsync(fileStream, cancellationToken);
    }

    public async override Task<bool> ExistsAsync(string path, string fileName,
        CancellationToken cancellationToken = default)
    {
        var filePath = Path.Combine(Configuration.Endpoint, Configuration.Bucket, path, fileName);

        return await Task.FromResult(File.Exists(filePath));
    }

    public async override Task DeleteAsync(string path, string filename, CancellationToken cancellationToken = default)
    {
        var filePath = Path.Combine(Configuration.Endpoint, Configuration.Bucket, path, filename);
        if (File.Exists(filePath))
        {
            File.Delete(filePath);
        }
        else
        {
            throw new FileNotFoundException($"File {filename} not found in path {path}");
        }

        // Delete the file
        File.Delete(filePath);
        await Task.CompletedTask;
    }

    public async override Task<FileMetadata> GetMetadataAsync(string path, string fileName,
        CancellationToken cancellationToken = default)
    {
        var filePath = Path.Combine(Configuration.Endpoint, Configuration.Bucket, path, fileName);
        // Console.WriteLine($@"GetMetadataAsync: {filePath}");
        if (!File.Exists(filePath))
        {
            throw new FileNotFoundException($"{filePath} not found.");
        }

        var fileInfo = new FileInfo(filePath);
        var metadata = new FileMetadata
        {
            Name = fileInfo.Name,
            Size = fileInfo.Length,
            Hash = fileInfo.OpenRead().ToHashString(),
            LastModified = fileInfo.LastWriteTimeUtc
        };
        return await Task.FromResult(metadata);
    }
}

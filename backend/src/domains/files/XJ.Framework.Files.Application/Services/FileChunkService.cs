using XJ.Framework.Files.Application.Services.StorageProviders;
using XJ.Framework.Files.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Files.Application.Services;

/// <summary>
/// FileChunk 服务实现
/// </summary>
public sealed class FileChunkService :
    BaseEditableAppService<long, FileChunkEntity, FileChunkDto, FileChunkOperationDto, IFileChunkRepository,
        FileChunkQueryCriteria>,
    IFileChunkService
{
    private readonly IFileTypeService _fileTypeService;
    private readonly IFileInfoService _fileInfoService;
    private readonly IFileAclService _fileAclService;
    private readonly IStorageService _storageService;
    private readonly IStorageProviderFactory _storageProviderFactory;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly IFileInfoRepository _fileInfoRepository;
    private readonly IAuthProvider _authProvider;

    public FileChunkService(IFileChunkRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext, IFileTypeService fileTypeService,
        IFileInfoService fileInfoService, IFileAclService fileAclService, IStorageService storageService,
        IStorageProviderFactory storageProviderFactory, IFileInfoRepository fileInfoRepository,
        IAuthProvider authProvider) : base(
        repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _currentUserContext = currentUserContext;
        _fileTypeService = fileTypeService;
        _fileInfoService = fileInfoService;
        _fileAclService = fileAclService;
        _storageService = storageService;
        _storageProviderFactory = storageProviderFactory;
        _fileInfoRepository = fileInfoRepository;
        _authProvider = authProvider;
    }


    private async Task SaveFileChunkAsync(int chunkIndex,
        StorageDto storage,
        FileTypeDto fileType,
        FileInfoEntity fileInfo,
        Stream fileStream,
        CancellationToken cancellationToken)
    {
        var relativePath = Path.Combine(DateTime.Now.ToString("yyyyMMdd"), fileType.TypeCode);

        {
            var chunkId = KeyGenerator.GenerateKey();

            var chunkFileName = $"{fileInfo.Key}.{chunkId}";
            // 插入当前文件块数据库信息
            var fileChunk = new FileChunkEntity
            {
                Key = chunkId,
                FileId = fileInfo.Key,
                ChunkIndex = chunkIndex,
                ChunkHash = fileStream.ToHashString(),
                ChunkSize = fileStream.Length,
                Status = FileChunkStatus.NoStart,
                StorageCode = storage.StorageCode,
                StoragePath = relativePath,
                FileName = chunkFileName,
            };
            await Repository.InsertAsync(fileChunk);
            // 保存当前文件块
            var storageProvider = _storageProviderFactory.CreateProvider(storage);
            await storageProvider.WriteAsync(relativePath, chunkFileName, fileStream, cancellationToken);

            fileChunk.Status = FileChunkStatus.Completed;
            await Repository.UpdateAsync(fileChunk);


            // 重新计算FileInfo的状态
            var fileChunks = (await Repository.GetListAsync(q => q.FileId == fileInfo.Key)).ToList();
            var uploadedChunks = fileChunks.Count(q => q.Status == FileChunkStatus.Completed);
            var isCompleted = uploadedChunks == fileInfo.ChunkCount;

            if (isCompleted)
            {
                fileInfo.Status = FileStatus.Completed;

                await _fileInfoRepository.DetachAndUpdateAsync(fileInfo);
            }
        }
    }


    public async Task<FileUploadResponseDto> UploadChunkAsync(FileChunkUploadDto request, Stream fileStream,
        long? operatorUserId = null,
        CancellationToken cancellationToken = default)
    {
        var chunkIndex = request.ChunkIndex;
        // Guid fileId;
        //
        FileInfoEntity? fileInfo = null;
        // 第一种情况 第一块
        if (request.ChunkIndex == 0)
        {
            // 验证必填
            if (string.IsNullOrWhiteSpace(request.FileName))
            {
                throw new ValidationException("File name is required.");
            }

            if (string.IsNullOrWhiteSpace(request.FileTypeCode))
            {
                throw new ValidationException("File type code is required.");
            }

            if (request.TotalSize <= 0)
            {
                throw new ValidationException("File size is required.");
            }

            if (request.TotalChunk <= 0)
            {
                throw new ValidationException("Total chunk count is required.");
            }


            var fileId = Guid.NewGuid();
            var fileName = request.FileName;

            var invalidChars = Path.GetInvalidFileNameChars();

            invalidChars.ForEach(ch => { fileName = fileName.Replace(ch, '_'); });

            var fileTypeCode = request.FileTypeCode;


            // 插入当前文件信息数据库信息
            fileInfo = new FileInfoEntity
            {
                Key = fileId,
                FileName = fileName,
                FileTypeCode = fileTypeCode,
                FileSize = request.TotalSize!.Value,
                ChunkCount = request.TotalChunk!.Value,
                Status = FileStatus.Uploading,
                UploaderId = operatorUserId
            };
            await _fileInfoRepository.DetachAndInsertAsync(fileInfo);
        }
        else
        {
            if (request.FileId == null)
            {
                throw new ValidationException("File ID is required.");
            }

            fileInfo = await _fileInfoRepository.DetachAndGetAsync(request.FileId!.Value);
        }

        if (fileInfo == null)
        {
            throw new ValidationException("File not found.");
        }

        var fileType = await _fileTypeService.GetByCodeAsync(fileInfo.FileTypeCode);
        if (fileType == null)
        {
            throw new ValidationException("File type not found.");
        }

        var storage = await _storageService.GetStorageAsync(fileType.TypeCode);

        // 验证文件类型
        var allowedExtensions = fileType.Extension.Split(',').Select(q => q.ToUpper()).ToList();
        var fileExtension = Path.GetExtension(fileInfo.FileName).ToUpper();
        if (!allowedExtensions.Contains(fileExtension))
        {
            throw new ValidationException($"File extension not allowed: {fileExtension}");
        }

        // 验证文件大小
        if (fileInfo.FileSize > fileType.SizeLimit)
        {
            throw new ValidationException($"File size exceeds limit: {fileType.SizeLimit} bytes.");
        }

        if (fileStream.Length > fileType.ChunkLimit)
        {
            throw new ValidationException($"File chunk size exceeds limit: {fileType.ChunkLimit} bytes.");
        }

        // 验证文件块索引
        if (chunkIndex < 0 || chunkIndex >= fileInfo.ChunkCount)
        {
            throw new ValidationException($"Invalid chunk index: {chunkIndex}");
        }

        // 使用传入的request.TotalSize 除以 fileType.ChunkLimit（取整，如果有余数则+1）判断是否等于request.TotalChunk
        var totalChunk = (int)Math.Ceiling((double)fileInfo.FileSize / fileType.ChunkLimit);
        if (totalChunk != fileInfo.ChunkCount)
        {
            throw new ValidationException(
                $"Invalid total chunk count: {fileInfo.ChunkCount} or file size: {fileInfo.FileSize}");
        }


        var fileChunks = (await Repository.GetListAsync(q => q.FileId == fileInfo.Key)).ToList();

        // 验证文件块是否已经存在
        var existChunk = fileChunks.FirstOrDefault(q => q.ChunkIndex == request.ChunkIndex);
        if (existChunk != null)
        {
            throw new ValidationException($"File chunk already exists: {existChunk.ChunkIndex}");
        }

        // 验证已上传大小
        var uploadedSize = fileChunks.Sum(q => q.ChunkSize);
        if (uploadedSize + fileStream.Length > fileType.SizeLimit)
        {
            throw new ValidationException($"Uploaded file size exceeds limit: {fileType.SizeLimit} bytes.");
        }

        await SaveFileChunkAsync(chunkIndex, storage, fileType, fileInfo, fileStream, cancellationToken);

        // 返回当前文件信息
        return await GetUploadProgressAsync(fileInfo.Key);
    }

    public async Task<FileUploadResponseDto> GetUploadProgressAsync(Guid fileId)
    {
        var fileInfo = await _fileInfoService.GetByIdAsync(fileId);

        if (fileInfo == null)
        {
            throw new ValidationException("File not found.");
        }

        var fileChunks = (await Repository.GetListAsync(q => q.FileId == fileId)).ToList();

        var uploadedSize = fileChunks.Sum(q => q.ChunkSize);

        var totalChunks = fileInfo.ChunkCount;

        var uploadedChunks = fileChunks.Count(q => q.Status == FileChunkStatus.Completed);

        var isCompleted = uploadedChunks == totalChunks;


        return new FileUploadResponseDto()
        {
            UploadedChunks = uploadedChunks,
            TotalChunks = totalChunks,
            IsCompleted = isCompleted,
            UploadedSize = uploadedSize,
            FileInfo = fileInfo
        };
    }

    public async IAsyncEnumerable<byte[]> GetFileStreamAsync(
        Guid fileId,
        long? start = null,
        long? end = null,
        int chunkSize = 64 * 1024,
        int delayMs = 0,
        [System.Runtime.CompilerServices.EnumeratorCancellation]
        CancellationToken cancellationToken = default)
    {
        var fileInfo = await _fileInfoRepository.GetAsync(q => q.Key == fileId);
        if (fileInfo == null)
            throw new ValidationException("File not found.");

        if (fileInfo.Status != FileStatus.Completed)
            throw new ValidationException("File not completed.");

        start ??= 0;
        end ??= fileInfo.FileSize - 1;

        if (start < 0 || end < start || end >= fileInfo.FileSize)
            throw new ValidationException("Invalid range.");

        var fileChunks = (await Repository.GetListAsync(q => q.FileId == fileId))
            .OrderBy(q => q.ChunkIndex).ToList();

        long filePos = 0;
        foreach (var chunk in fileChunks)
        {
            var chunkStart = filePos;
            var chunkEnd = filePos + chunk.ChunkSize - 1;

            if (chunkEnd < start)
            {
                filePos += chunk.ChunkSize;
                continue;
            }

            if (chunkStart > end)
            {
                break;
            }

            var innerStart = Math.Max(0, start.Value - chunkStart);
            var innerEnd = Math.Min(chunk.ChunkSize - 1, end.Value - chunkStart);
            var readLength = innerEnd - innerStart + 1;

            var storage = await _storageService.GetStorageAsync(chunk.StorageCode);
            var storageProvider = _storageProviderFactory.CreateProvider(storage);

            await using (var chunkStream =
                         await storageProvider.OpenReadAsync(chunk.StoragePath, chunk.FileName, cancellationToken))
            {
                if (innerStart > 0)
                    chunkStream.Seek(innerStart, SeekOrigin.Begin);

                long left = readLength;
                while (left > 0)
                {
                    int readSize = (int)Math.Min(chunkSize, left);
                    var buffer = new byte[readSize];
                    int bytesRead = await chunkStream.ReadAsync(buffer, 0, readSize, cancellationToken);
                    if (bytesRead <= 0) break;

                    if (bytesRead < buffer.Length)
                    {
                        var realBuffer = new byte[bytesRead];
                        Array.Copy(buffer, realBuffer, bytesRead);
                        yield return realBuffer;
                    }
                    else
                    {
                        yield return buffer;
                    }

                    left -= bytesRead;

                    if (delayMs > 0)
                        await Task.Delay(delayMs, cancellationToken);
                }
            }

            filePos += chunk.ChunkSize;
        }
    }

    public async Task<FileInfoDto> UploadAsync(FileUploadMgtDto request, Stream openReadStream,
        long? requestOperator = null,
        CancellationToken cancellationToken = default)
    {
        var fileType = await _fileTypeService.GetByCodeAsync(request.FileTypeCode);
        fileType.NullCheck();


        // 将openReadStream 按照fileType中设置的fileType.ChunkLimit大小拆分成多个流
        var chunkSize = fileType!.ChunkLimit;
        var chunkCount = (int)Math.Ceiling((double)openReadStream.Length / chunkSize);
        var chunks = new List<Stream>();
        for (var i = 0; i < chunkCount; i++)
        {
            var chunk = new MemoryStream();
            var buffer = new byte[chunkSize];
            var bytesRead = await openReadStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken);
            chunk.Write(buffer, 0, bytesRead);
            chunk.Seek(0, SeekOrigin.Begin);
            chunks.Add(chunk);
        }

        // 拆分后循环调用UploadChunkAsync方法 调用前需要先构建 FileChunkUploadDto对象

        FileInfoDto? fileInfo = null;

        for (var i = 0; i < chunkCount; i++)
        {
            var chunkDto = new FileChunkUploadDto()
            {
                FileId = fileInfo?.Key,
                ChunkIndex = i,
                FileName = request.FileName,
                FileTypeCode = request.FileTypeCode,
                TotalSize = openReadStream.Length,
                TotalChunk = chunkCount
            };
            var result = await UploadChunkAsync(chunkDto, chunks[i], requestOperator, cancellationToken);

            fileInfo = result.FileInfo;
        }

        return fileInfo!;
    }
}

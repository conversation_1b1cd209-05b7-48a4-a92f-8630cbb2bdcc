using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Files.Domain.Shared.Dtos;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Files.ApiClient;

public class FilesApplicationApiClient : BaseApplicationApiClient
{
    private readonly string _baseUrl;

    public FilesApplicationApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption,
        ILogger<BaseApiClient> logger, IAuthInfoGetter authInfoGetter, IContextContainer contextContainer,
        ICurrentUserContext currentUserContext, ICurrentApplicationContext applicationContext,
        IOptions<JsonOptions> jsonOptions) : base(httpClient,
        logger, authInfoGetter, contextContainer, currentUserContext, applicationContext, jsonOptions)
    {
        _baseUrl = endpointOption.Value["Files"]!.Url.TrimEnd('/');
    }

    public async Task<byte[]> DownloadFileAsync(string fileId, string fileName,
        CancellationToken cancellationToken)
    {
        var url = $"{_baseUrl}/FileInfo/app/{fileId}/{fileName}";
        return await GetByteArrayAsync(url: url, cancellationToken: cancellationToken);
    }
    public async Task<FileInfoDto> UploadAsync(string fileTypeCode, string fileName, long operatorUserId, byte[] bytes)
    {
        var url = $"{_baseUrl}/FileInfo/app/upload";

        // 提交request和bytes

        using var form = new MultipartFormDataContent();
        form.Add(new StringContent(fileTypeCode), "fileTypeCode");
        form.Add(new StringContent(fileName), "fileName");
        form.Add(new StringContent(operatorUserId.ToString()), "operator");
        form.Add(new ByteArrayContent(bytes), "file", fileName);

        return await InternalPostFileAsync<FileInfoDto>(url, form);
    }
}

namespace XJ.Framework.Files.EntityFrameworkCore.Repositories;

/// <summary>
/// FileInfo 仓储实现
/// </summary>
public class FileInfoRepository : BaseSoftDeleteRepository<FilesDbContext, Guid, FileInfoEntity>, IFileInfoRepository
{
    public FileInfoRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<bool> DetachAndInsertAsync(FileInfoEntity entity, bool isSaveChange = true)
    {
        DetachWhenExists(entity.Key);
        return await base.InsertAsync(entity, isSaveChange);
    }

    public async Task<bool> DetachAndUpdateAsync(FileInfoEntity entity, bool isSaveChange = true,
        List<string>? updatePropertyList = null)
    {
        DetachWhenExists(entity.Key);
        return await base.UpdateAsync(entity, isSaveChange, updatePropertyList);
    }

    public async Task<FileInfoEntity?> DetachAndGetAsync(Guid id)
    {
        DetachWhenExists(id);
        return await base.GetAsync(id);
    }
}

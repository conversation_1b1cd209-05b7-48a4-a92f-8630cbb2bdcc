// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XJ.Framework.Files.EntityFrameworkCore;

#nullable disable

namespace XJ.Framework.Files.EntityFrameworkCore.Migrations
{
    [DbContext(typeof(FilesDbContext))]
    partial class FilesDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XJ.Framework.Files.Domain.Entities.FileAclEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("ExpireAt")
                        .HasColumnType("datetime2")
                        .HasColumnName("expire_at")
                        .HasComment("过期时间");

                    b.Property<long>("FileId")
                        .HasColumnType("bigint")
                        .HasColumnName("file_id")
                        .HasComment("文件ID");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("PrincipalId")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("principal_id")
                        .HasComment("主体ID");

                    b.Property<string>("PrincipalType")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("principal_type")
                        .HasComment("主体类型（user、role、token等）");

                    b.HasKey("Key");

                    b.HasIndex("FileId")
                        .HasDatabaseName("IX_file_acl_file_id")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("PrincipalType", "PrincipalId", "ExpireAt")
                        .HasDatabaseName("IX_file_acl_principal")
                        .HasFilter("[is_deleted] = 0");

                    SqlServerIndexBuilderExtensions.IncludeProperties(b.HasIndex("PrincipalType", "PrincipalId", "ExpireAt"), new[] { "FileId" });

                    b.ToTable("file_acls", "f", t =>
                        {
                            t.HasComment("FileAcl 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Files.Domain.Entities.FileChunkEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("ChunkHash")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("chunk_hash")
                        .HasComment("分块hash值");

                    b.Property<int>("ChunkIndex")
                        .HasColumnType("int")
                        .HasColumnName("chunk_index")
                        .HasComment("分块序号（从0开始）");

                    b.Property<long>("ChunkSize")
                        .HasColumnType("bigint")
                        .HasColumnName("chunk_size")
                        .HasComment("分块大小（字节）");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("FileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("file_id")
                        .HasComment("文件ID");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("file_name")
                        .HasComment("分块文件名称");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态（0-未上传，1-已上传）");

                    b.Property<string>("StorageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("storage_code")
                        .HasComment("存储信息code");

                    b.Property<string>("StoragePath")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("storage_path")
                        .HasComment("分块存储路径");

                    b.HasKey("Key");

                    b.HasIndex("FileId")
                        .HasDatabaseName("IX_file_chunks_file_id")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("StorageCode")
                        .HasDatabaseName("IX_file_chunks_storage_code")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FileId", "ChunkIndex")
                        .HasDatabaseName("IX_file_chunks_file_id_chunk_index")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("file_chunks", "f", t =>
                        {
                            t.HasComment("FileChunk 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Files.Domain.Entities.FileInfoEntity", b =>
                {
                    b.Property<Guid>("Key")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<int>("ChunkCount")
                        .HasColumnType("int")
                        .HasColumnName("chunk_count")
                        .HasComment("总计分块数量");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FileName")
                        .IsRequired()
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("file_name")
                        .HasComment("文件原始名称");

                    b.Property<long>("FileSize")
                        .HasColumnType("bigint")
                        .HasColumnName("file_size")
                        .HasComment("文件总大小（字节）");

                    b.Property<string>("FileTypeCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("file_type_code")
                        .HasComment("文件类型code");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态（0-上传中，1-完成，2-删除）");

                    b.Property<long?>("UploaderId")
                        .HasMaxLength(100)
                        .HasColumnType("bigint")
                        .HasColumnName("uploader_id")
                        .HasComment("上传用户ID");

                    b.HasKey("Key");

                    b.HasIndex("FileTypeCode")
                        .HasDatabaseName("IX_file_info_file_type_code")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("UploaderId")
                        .HasDatabaseName("IX_file_info_uploader_id")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("file_infos", "f", t =>
                        {
                            t.HasComment("FileInfo 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Files.Domain.Entities.FileTypeEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<long>("ChunkLimit")
                        .HasColumnType("bigint")
                        .HasColumnName("chunk_limit")
                        .HasComment("分块大小限制（字节）");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Extension")
                        .IsRequired()
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("extensions")
                        .HasComment("允许的文件后缀（如.jpg,.png)");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("PermissionRequired")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasComment("所需权限类型 none=无 user=用户 role=角色");

                    b.Property<string>("Remark")
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("remark")
                        .HasComment("备注");

                    b.Property<long>("SizeLimit")
                        .HasColumnType("bigint")
                        .HasColumnName("size_limit")
                        .HasComment("大小限制（字节）");

                    b.Property<string>("TypeCode")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("type_code")
                        .HasComment("类型编码");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("type_name")
                        .HasComment("类型名称");

                    b.HasKey("Key");

                    b.HasIndex("TypeCode")
                        .IsUnique()
                        .HasDatabaseName("IX_file_types_type_code")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("file_types", "f", t =>
                        {
                            t.HasComment("FileType 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1941212317200224257L,
                            ChunkLimit = 5242880L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Extension = ".jpg,.jpeg,.png",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionRequired = "login",
                            Remark = "头像文件类型",
                            SizeLimit = 5242880L,
                            TypeCode = "avatar",
                            TypeName = "头像"
                        },
                        new
                        {
                            Key = 1941212317200224258L,
                            ChunkLimit = 5242880L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Extension = ".pdf",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionRequired = "login",
                            Remark = "",
                            SizeLimit = 5242880L,
                            TypeCode = "ethic_committee_approved_file",
                            TypeName = "伦理委员会审批件"
                        },
                        new
                        {
                            Key = 1941212317200224259L,
                            ChunkLimit = 5242880L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Extension = ".pdf",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionRequired = "login",
                            Remark = "",
                            SizeLimit = 5242880L,
                            TypeCode = "mpa_approved_file",
                            TypeName = "国家药监局批准附件"
                        },
                        new
                        {
                            Key = 1941212317200224260L,
                            ChunkLimit = 5242880L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Extension = ".pdf",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionRequired = "login",
                            Remark = "",
                            SizeLimit = 5242880L,
                            TypeCode = "study_protocol",
                            TypeName = "研究方案"
                        },
                        new
                        {
                            Key = 1941212317200224261L,
                            ChunkLimit = 5242880L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Extension = ".pdf",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionRequired = "login",
                            Remark = "",
                            SizeLimit = 5242880L,
                            TypeCode = "informed_consent_file",
                            TypeName = "知情同意书"
                        },
                        new
                        {
                            Key = 1941212317200224262L,
                            ChunkLimit = 5242880L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Extension = ".pdf",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionRequired = "login",
                            Remark = "",
                            SizeLimit = 5242880L,
                            TypeCode = "statistical_results_file",
                            TypeName = "上传试验完成后的统计结果"
                        });
                });

            modelBuilder.Entity("XJ.Framework.Files.Domain.Entities.FileTypeStorageRelEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FileTypeCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("file_type_code")
                        .HasComment("文件类型code");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("Priority")
                        .HasColumnType("int")
                        .HasColumnName("priority")
                        .HasComment("优先级，数字越小优先级越高");

                    b.Property<string>("StorageCode")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("storage_code")
                        .HasComment("存储信息code");

                    b.HasKey("Key");

                    b.HasIndex("StorageCode")
                        .HasDatabaseName("IX_file_type_storage_rel_storage_code")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FileTypeCode", "Priority")
                        .HasDatabaseName("IX_file_type_storage_rel_file_type_code")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("file_type_storage_rels", "f", t =>
                        {
                            t.HasComment("FileTypeStorageRel 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Files.Domain.Entities.StorageEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AccessKey")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("access_key")
                        .HasComment("访问key");

                    b.Property<string>("Bucket")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("bucket")
                        .HasComment("桶/容器名");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Endpoint")
                        .IsRequired()
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("endpoint")
                        .HasComment("存储服务地址");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("Protocol")
                        .HasColumnType("int")
                        .HasColumnName("protocol")
                        .HasComment("协议（如oss、s3、ftp）");

                    b.Property<string>("Remark")
                        .HasMaxLength(510)
                        .HasColumnType("nvarchar(510)")
                        .HasColumnName("remark")
                        .HasComment("备注");

                    b.Property<string>("SecretKey")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("secret_key")
                        .HasComment("访问secret");

                    b.Property<string>("StorageCode")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("storage_code")
                        .HasComment("存储编码");

                    b.Property<string>("StorageName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("storage_name")
                        .HasComment("存储名称");

                    b.HasKey("Key");

                    b.ToTable("storages", "f", t =>
                        {
                            t.HasComment("Storage 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1941212317200224256L,
                            AccessKey = "",
                            Bucket = "default",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Endpoint = "",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 7, 4, 19, 7, 37, 58, DateTimeKind.Unspecified).AddTicks(7830), new TimeSpan(0, 0, 0, 0, 0)),
                            Protocol = 1,
                            SecretKey = "",
                            StorageCode = "default",
                            StorageName = "默认存储"
                        });
                });
#pragma warning restore 612, 618
        }
    }
}

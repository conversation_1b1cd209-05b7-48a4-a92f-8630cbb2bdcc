using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.DynamicForm.Domain.Entities;

/// <summary>
/// 表单数据实体
/// </summary>
[SoftDeleteIndex("IX_form_instance_data_form_code_version_code_not_deleted", nameof(FormCode), nameof(FormVersion),
    nameof(Code),
    nameof(Version),
    nameof(BusinessId), IsUnique = true)]

[SoftDeleteIndex("IX_form_instance_datas_form_version_version_business_id_value",nameof(FormVersion), nameof(Version),
    nameof(BusinessId),
    nameof(Value))]

[Table("form_instance_datas", Schema = "d")]
public class FormInstanceDataEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 表单编码
    /// </summary>
    [Column("form_code")]
    [StringLength(50)]
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    [Column("form_version")]
    [StringLength(50)]
    public string FormVersion { get; set; } = null!;


    /// <summary>
    /// 当前实例版本
    /// </summary>
    [Column("version")]
    [StringLength(50)]
    public string Version { get; set; } = null!;


    /// <summary>
    /// 当前实例业务id
    /// </summary>
    [Column("business_id")]
    [StringLength(50)]
    public string BusinessId { get; set; } = null!;


    /// <summary>
    /// 数据编码
    /// </summary>
    [Column("code")]
    [StringLength(50)]
    public string Code { get; set; } = null!;

    /// <summary>
    /// 数据值
    /// </summary>
    [Column("value")]
    [StringLength(-1)]
    public string? Value { get; set; } = null!;
}

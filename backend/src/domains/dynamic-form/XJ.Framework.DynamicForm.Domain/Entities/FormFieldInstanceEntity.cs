using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.DynamicForm.Domain.Entities;

/// <summary>
/// FormFieldInstance 实体
/// </summary>
[Table("form_field_instances", Schema = "d")]
[SoftDeleteIndex("IX_form_field_instance_form_code_version_code_not_deleted", nameof(FormCode), nameof(FormVersion),
    nameof(Code),
    nameof(Version),
    nameof(RowIndex),
    nameof(BusinessId), IsUnique = true)]
public class FormFieldInstanceEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 表单编码
    /// </summary>
    [Column("form_code")]
    [StringLength(50)]
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    [Column("form_version")]
    [StringLength(50)]
    public string FormVersion { get; set; } = null!;


    /// <summary>
    /// 当前实例版本
    /// </summary>
    [Column("version")]
    [StringLength(50)]
    public string Version { get; set; } = null!;


    /// <summary>
    /// 当前实例业务id
    /// </summary>
    [Column("business_id")]
    [StringLength(50)]
    public string BusinessId { get; set; } = null!;


    /// <summary>
    /// 字段编码
    /// </summary>
    [Column("code")]
    [StringLength(50)]
    public string Code { get; set; } = null!;

    /// <summary>
    /// 字段实例值
    /// </summary>
    [Column("json_value")]
    [StringLength(-1)]
    public string? JsonValue { get; set; }

    /// <summary>
    /// 批注
    /// </summary>
    [Column("annotations")]
    [StringLength(-1)]
    public string? Annotations { get; set; }


    /// <summary>
    /// 字段实例值显示
    /// </summary>
    [Column("display")]
    [StringLength(400)]
    public string? Display { get; set; }

    // /// <summary>
    // /// 字段驳回时的批注信息
    // /// </summary>
    // [Column("warning")]
    // [StringLength(400)]
    // public string? Warning { get; set; }

    /// <summary>
    /// 上级字段编码
    /// </summary>
    [StringLength(50)]
    [Column("parent_code")]
    public string? ParentCode { get; set; }

    /// <summary>
    /// 字段类型
    /// </summary>
    [StringLength(50)]
    [Column("type")]
    public string Type { get; set; } = null!;

    /// <summary>
    /// 值所在行 该值仅在多行子表单时有效
    /// </summary>
    [Column("row_index")]
    public int? RowIndex { get; set; }
}

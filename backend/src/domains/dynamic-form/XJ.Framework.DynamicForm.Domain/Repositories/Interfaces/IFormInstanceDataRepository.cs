using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.DynamicForm.Domain.Repositories.Interfaces;

/// <summary>
/// FormInstanceData 仓储接口
/// </summary>
public interface IFormInstanceDataRepository : IAuditRepository<long, FormInstanceDataEntity>
{
    Task<List<FormInstanceDataEntity>> GetListByInstanceAsync(List<string> columns, string formVersion, string formCode, List<(string businessId, string version)> toList);
}

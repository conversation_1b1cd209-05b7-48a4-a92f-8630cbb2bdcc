namespace XJ.Framework.DynamicForm.EntityFrameworkCore;

public class DynamicFormInfrastructureWrapper : InfrastructureWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddDbContext<DynamicFormDbContext>(
            optionsAction: (serviceProvider, contextOptions) =>
            {
                var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
                if (env.IsDevelopment())
                {
                    contextOptions.EnableSensitiveDataLogging();
                }

                contextOptions.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            },
            contextLifetime:
            ServiceLifetime.Scoped
        );

        services.AddScoped<IUnitOfWork, UnitOfWork<DynamicFormDbContext>>();
    }
} 
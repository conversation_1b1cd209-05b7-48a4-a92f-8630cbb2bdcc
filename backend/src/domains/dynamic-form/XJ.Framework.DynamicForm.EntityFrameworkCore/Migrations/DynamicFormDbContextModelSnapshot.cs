// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XJ.Framework.DynamicForm.EntityFrameworkCore;

#nullable disable

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Migrations
{
    [DbContext(typeof(DynamicFormDbContext))]
    partial class DynamicFormDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XJ.Framework.DynamicForm.Domain.Entities.FormEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("code")
                        .HasComment("表单唯一编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("description")
                        .HasComment("表单描述");

                    b.Property<string>("JsonConfig")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("json_config")
                        .HasComment("全局配置（json）");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("name")
                        .HasComment("表单名称");

                    b.Property<bool>("NewestVersion")
                        .HasColumnType("bit")
                        .HasColumnName("newest_version")
                        .HasComment("是否最新版本");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("version")
                        .HasComment("表单版本");

                    b.HasKey("Key");

                    b.HasIndex("Code", "Version")
                        .IsUnique()
                        .HasDatabaseName("UX_forms_code_version_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("forms", "d", t =>
                        {
                            t.HasComment("Form 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1942570380830179328L,
                            Code = "demo",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            Deleted = false,
                            Name = "试验登记表",
                            NewestVersion = true,
                            Version = "20250507144008890"
                        });
                });

            modelBuilder.Entity("XJ.Framework.DynamicForm.Domain.Entities.FormFieldEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("code")
                        .HasComment("字段唯一编码，同一form_id下唯一");

                    b.Property<int>("Colspan")
                        .HasColumnType("int")
                        .HasColumnName("colspan")
                        .HasComment("控件占用列数，默认1，范围1-3");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<string>("DefaultValue")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("default_value")
                        .HasComment("默认值，类型与控件type对应。\\nselect/checkbox/radio为选项value，checkbox为数组；\\nunit_select为{\"value\":1,\"unit\":\"day\"}；\\nint_range为{\"min\":10, \"max\":20}；\\ndate_range为{\"start\":\"2023-01-01\", \"end\":\"2023-12-31\"}；\\ntext_multilang/textarea_multilang为{\"zh\":\"中文值\", \"en\":\"英文值\"}");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Description")
                        .HasMaxLength(800)
                        .HasColumnType("nvarchar(800)")
                        .HasColumnName("description")
                        .HasComment("字段备注");

                    b.Property<string>("ExtraConfig")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("extra_config")
                        .HasComment("额外配置（json），如校验规则、动态行为等");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单ID，关联[d].[forms].[code]");

                    b.Property<string>("GroupCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("group_code")
                        .HasComment("分组编码，关联[d].[form_field_groups].[code]，可为空");

                    b.Property<string>("LabelEn")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("label_en")
                        .HasComment("英文标签");

                    b.Property<string>("LabelZh")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("label_zh")
                        .HasComment("中文标签");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<bool>("NewLine")
                        .HasColumnType("bit")
                        .HasColumnName("newLine")
                        .HasComment("是否新起一行，0-否，1-是");

                    b.Property<string>("Option")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("options")
                        .HasComment("选项（json），type为select/checkbox/radio/unit_select等有选项的控件时使用，支持中英文双语。\\nselect/checkbox/radio格式如：[{'label_zh':'选项1', 'label_en':'Option 1', 'value':'1'}]；\\nunit_select格式如：[{'label_zh':'天', 'label_en':'Day', 'value':'day'}]；\\nint_range格式如：{\"min\":0, \"max\":100, \"step\":1}；\\ndate_range格式如：{\"min\":\"2020-01-01\", \"max\":\"2030-12-31\"}");

                    b.Property<string>("ParentCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("parent_code")
                        .HasComment("父字段编码，支持子表单结构，根字段为NULL");

                    b.Property<string>("RejectReason")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("reject_reason")
                        .HasComment("字段驳回时的批注信息");

                    b.Property<bool>("Required")
                        .HasColumnType("bit")
                        .HasColumnName("required")
                        .HasComment("是否必填，0-否，1-是");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order")
                        .HasComment("排序，正序排列");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("type")
                        .HasComment("控件类型，可选值：text（单行文本）、text_multilang（双语单行文本）、textarea（多行文本）、textarea_multilang（双语多行文本）、select（下拉）、checkbox（多选）、radio（单选）、date（日期）、number（数字）、unit_select（数字+单位下拉）、int_range（整数范围）、date_range（日期范围）、form_single（单行子表单）、form_multi（多行子表单）");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("表单版本");

                    b.HasKey("Key");

                    b.HasIndex("FormCode")
                        .HasDatabaseName("IX_form_fields_form_id_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FormCode", "Code", "Version", "ParentCode")
                        .IsUnique()
                        .HasDatabaseName("UX_form_fields_form_code_version_code_parent_code_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("form_fields", "d", t =>
                        {
                            t.HasComment("FormField 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1942570380830179330L,
                            Code = "name",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "{\"zh\":\"\",\"en\":\"\"}",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Name",
                            LabelZh = "姓名",
                            NewLine = false,
                            Required = true,
                            SortOrder = 0,
                            Type = "text_multilang",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179331L,
                            Code = "sex",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Sex",
                            LabelZh = "性别",
                            NewLine = false,
                            Option = "[{\"labelZh\":\"男性\",\"labelEn\":\"Male\",\"value\":\"male\"},{\"labelZh\":\"女性\",\"labelEn\":\"Female\",\"value\":\"female\"}]",
                            Required = true,
                            SortOrder = 1,
                            Type = "radio",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179332L,
                            Code = "location",
                            Colspan = 3,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Location",
                            LabelZh = "所在地",
                            NewLine = false,
                            Required = false,
                            SortOrder = 2,
                            Type = "textarea",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179333L,
                            Code = "hobby",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "[]",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Hobby",
                            LabelZh = "爱好",
                            NewLine = false,
                            Option = "[{\"labelZh\":\"游泳\",\"labelEn\":\"Swimming\",\"value\":\"swimming\"},{\"labelZh\":\"跑步\",\"labelEn\":\"Running\",\"value\":\"running\"},{\"labelZh\":\"滑雪\",\"labelEn\":\"Skiing\",\"value\":\"skiing\"}]",
                            Required = false,
                            SortOrder = 3,
                            Type = "checkbox",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179334L,
                            Code = "mobile",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Mobile",
                            LabelZh = "手机号",
                            NewLine = true,
                            Required = true,
                            SortOrder = 4,
                            Type = "input",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179335L,
                            Code = "trialTime",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "{\"start\":\"\",\"end\":\"\"}",
                            Deleted = false,
                            ExtraConfig = "{\"format\":\"YYYY-MM-DD\"}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "TrialTime",
                            LabelZh = "试验参与时间",
                            NewLine = false,
                            Required = true,
                            SortOrder = 5,
                            Type = "date_range",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179336L,
                            Code = "period",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "{\"value\":\"\",\"unit\":\"\"}",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Period",
                            LabelZh = "访试周期",
                            NewLine = false,
                            Option = "[{\"labelZh\":\"月\",\"labelEn\":\"Month\",\"value\":\"month\"},{\"labelZh\":\"周\",\"labelEn\":\"Week\",\"value\":\"week\"},{\"labelZh\":\"天\",\"labelEn\":\"Day\",\"value\":\"day\"}]",
                            Required = true,
                            SortOrder = 6,
                            Type = "unit_select",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179337L,
                            Code = "edu_group",
                            Colspan = 4,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "[{\"edu_school_name\":\"\",\"edu_level\":\"\",\"edu_end\":\"\"}]",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "",
                            LabelZh = "教育经历",
                            NewLine = false,
                            Required = false,
                            SortOrder = 7,
                            Type = "multiSubForm",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179338L,
                            Code = "edu_school_name",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "",
                            LabelZh = "学校名称",
                            NewLine = false,
                            ParentCode = "edu_group",
                            Required = false,
                            SortOrder = 8,
                            Type = "input",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179339L,
                            Code = "edu_level",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "",
                            LabelZh = "学历",
                            NewLine = false,
                            Option = "[{\"labelZh\":\"高中\",\"labelEn\":\"Option 1\",\"value\":\"option1\"},{\"labelZh\":\"专科\",\"labelEn\":\"Option 2\",\"value\":\"option2\"},{\"labelZh\":\"本科\",\"labelEn\":\"Option 3\",\"value\":\"option3\"}]",
                            ParentCode = "edu_group",
                            Required = false,
                            SortOrder = 9,
                            Type = "select",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179340L,
                            Code = "edu_end",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{\"format\":\"YYYY-MM-DD\"}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "",
                            LabelZh = "毕业时间",
                            NewLine = false,
                            ParentCode = "edu_group",
                            Required = false,
                            SortOrder = 10,
                            Type = "date",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179341L,
                            Code = "random_group",
                            Colspan = 4,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "null",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "Random Group",
                            LabelZh = "随机分组",
                            NewLine = false,
                            Required = false,
                            SortOrder = 11,
                            Type = "subForm",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179342L,
                            Code = "random_group_sex",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "",
                            LabelZh = "所属随机性别分组",
                            NewLine = false,
                            Option = "[{\"labelZh\":\"男\",\"labelEn\":\"Male\",\"value\":\"male\"},{\"labelZh\":\"女\",\"labelEn\":\"Female\",\"value\":\"female\"}]",
                            ParentCode = "random_group",
                            Required = false,
                            SortOrder = 12,
                            Type = "radio",
                            Version = "20250507144008890"
                        },
                        new
                        {
                            Key = 1942570380830179343L,
                            Code = "random_group_name",
                            Colspan = 2,
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 12, 15, 10, 40, 559, DateTimeKind.Unspecified).AddTicks(8890), new TimeSpan(0, 8, 0, 0, 0)),
                            DefaultValue = "\"\"",
                            Deleted = false,
                            ExtraConfig = "{}",
                            FormCode = "demo",
                            GroupCode = "grp_1746628396941-8913",
                            LabelEn = "",
                            LabelZh = "随机分组",
                            NewLine = false,
                            Option = "[{\"labelZh\":\"随机组\",\"labelEn\":\"Random Group\",\"value\":\"random_group\"},{\"labelZh\":\"对照组\",\"labelEn\":\"Control Group\",\"value\":\"control_group\"}]",
                            ParentCode = "random_group",
                            Required = false,
                            SortOrder = 13,
                            Type = "select",
                            Version = "20250507144008890"
                        });
                });

            modelBuilder.Entity("XJ.Framework.DynamicForm.Domain.Entities.FormFieldGroupEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("code")
                        .HasComment("分组编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单ID");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order")
                        .HasComment("排序");

                    b.Property<string>("TitleEn")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("title_en")
                        .HasComment("英文标题");

                    b.Property<string>("TitleZh")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("title_zh")
                        .HasComment("中文标题");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("表单版本");

                    b.HasKey("Key");

                    b.HasIndex("FormCode", "Code", "Version")
                        .IsUnique()
                        .HasDatabaseName("IX_form_field_groups_form_code_version_code_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("form_field_groups", "d", t =>
                        {
                            t.HasComment("FormFieldGroup 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1942570380830179329L,
                            Code = "grp_1746628396941-8913",
                            CreatedBy = "System",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 5, 7, 14, 40, 8, 936, DateTimeKind.Unspecified).AddTicks(7730), new TimeSpan(0, 8, 0, 0, 0)),
                            Deleted = false,
                            FormCode = "demo",
                            SortOrder = 0,
                            TitleEn = "Basic Information",
                            TitleZh = "基本信息",
                            Version = "20250507144008890"
                        });
                });

            modelBuilder.Entity("XJ.Framework.DynamicForm.Domain.Entities.FormFieldInstanceEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Annotations")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("annotations")
                        .HasComment("批注");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("business_id")
                        .HasComment("当前实例业务id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("code")
                        .HasComment("字段编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Display")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("display")
                        .HasComment("字段实例值显示");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单编码");

                    b.Property<string>("FormVersion")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_version")
                        .HasComment("表单版本");

                    b.Property<string>("JsonValue")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("json_value")
                        .HasComment("字段实例值");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("ParentCode")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("parent_code")
                        .HasComment("上级字段编码");

                    b.Property<int?>("RowIndex")
                        .HasColumnType("int")
                        .HasColumnName("row_index")
                        .HasComment("值所在行 该值仅在多行子表单时有效");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("type")
                        .HasComment("字段类型");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("当前实例版本");

                    b.HasKey("Key");

                    b.HasIndex("FormCode", "FormVersion", "Code", "Version", "RowIndex", "BusinessId")
                        .IsUnique()
                        .HasDatabaseName("IX_form_field_instance_form_code_version_code_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("form_field_instances", "d", t =>
                        {
                            t.HasComment("FormFieldInstance 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.DynamicForm.Domain.Entities.FormInstanceDataEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("business_id")
                        .HasComment("当前实例业务id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("code")
                        .HasComment("数据编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单编码");

                    b.Property<string>("FormVersion")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_version")
                        .HasComment("表单版本");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("value")
                        .HasComment("数据值");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("当前实例版本");

                    b.HasKey("Key");

                    b.HasIndex("FormVersion", "Version", "BusinessId", "Value")
                        .HasDatabaseName("IX_form_instance_datas_form_version_version_business_id_value")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("FormCode", "FormVersion", "Code", "Version", "BusinessId")
                        .IsUnique()
                        .HasDatabaseName("IX_form_instance_data_form_code_version_code_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("form_instance_datas", "d", t =>
                        {
                            t.HasComment("表单数据实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.DynamicForm.Domain.Entities.FormInstanceEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<long>("ApplyUserId")
                        .HasColumnType("bigint")
                        .HasColumnName("apply_user_id")
                        .HasComment("发起人");

                    b.Property<string>("BusinessId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("business_id")
                        .HasComment("关联业务id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("FormCode")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_code")
                        .HasComment("表单code");

                    b.Property<string>("FormVersion")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("form_version")
                        .HasComment("表单版本");

                    b.Property<bool>("IsObsoleted")
                        .HasColumnType("bit")
                        .HasColumnName("obsoleted")
                        .HasComment("是否已失效");

                    b.Property<string>("Language")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("language")
                        .HasComment("填写语言");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long?>("PreviousInstanceId")
                        .HasColumnType("bigint")
                        .HasColumnName("previous_instance_id")
                        .HasComment("前一个版本的表单实例id");

                    b.Property<string>("PreviousVersion")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("previous_version")
                        .HasComment("前一个版本的版本号");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("表单状态 1- 草稿 2- 已提交 3- 已确认 4- 已作废 5- 已驳回");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("version")
                        .HasComment("版本号");

                    b.Property<DateTimeOffset>("VersionTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("version_time")
                        .HasComment("版本创建时间");

                    b.HasKey("Key");

                    b.HasIndex("BusinessId")
                        .HasDatabaseName("IX_form_instances_business_id_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Version", "BusinessId")
                        .IsUnique()
                        .HasDatabaseName("UX_form_instances_version_business_id_not_deleted")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("form_instances", "d", t =>
                        {
                            t.HasComment("FormInstance 实体");
                        });
                });
#pragma warning restore 612, 618
        }
    }
}

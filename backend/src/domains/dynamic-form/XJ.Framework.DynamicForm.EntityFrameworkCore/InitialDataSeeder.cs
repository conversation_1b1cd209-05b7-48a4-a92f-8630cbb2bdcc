using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore;

using Microsoft.EntityFrameworkCore;
using System;

public static class InitialDataSeeder
{
    public static void Seed(this ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<FormEntity>().HasData(new FormEntity
        {
            Key = IdGenerator.NextId(),
            Code = "demo",
            Name = "试验登记表",
            Version = "20250507144008890",
            NewestVersion = true,
            CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
            CreatedBy = "System",
            LastModifiedTime = null,
            LastModifiedBy = null,
            Deleted = false
        });

        modelBuilder.Entity<FormFieldGroupEntity>().HasData(
            new FormFieldGroupEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                Code = "grp_1746628396941-8913",
                TitleZh = "基本信息",
                TitleEn = "Basic Information",
                SortOrder = 0,
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            });

        modelBuilder.Entity<FormFieldEntity>().HasData(
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "name",
                LabelZh = "姓名",
                LabelEn = "Name",
                Type = "text_multilang",
                Required = true,
                NewLine = false,
                Option = null,
                DefaultValue = "{\"zh\":\"\",\"en\":\"\"}",
                Colspan = 2,
                SortOrder = 0,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "sex",
                LabelZh = "性别",
                LabelEn = "Sex",
                Type = "radio",
                Required = true,
                NewLine = false,
                Option =
                    "[{\"labelZh\":\"男性\",\"labelEn\":\"Male\",\"value\":\"male\"},{\"labelZh\":\"女性\",\"labelEn\":\"Female\",\"value\":\"female\"}]",
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 1,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "location",
                LabelZh = "所在地",
                LabelEn = "Location",
                Type = "textarea",
                Required = false,
                NewLine = false,
                Option = null,
                DefaultValue = "\"\"",
                Colspan = 3,
                SortOrder = 2,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "hobby",
                LabelZh = "爱好",
                LabelEn = "Hobby",
                Type = "checkbox",
                Required = false,
                NewLine = false,
                Option =
                    "[{\"labelZh\":\"游泳\",\"labelEn\":\"Swimming\",\"value\":\"swimming\"},{\"labelZh\":\"跑步\",\"labelEn\":\"Running\",\"value\":\"running\"},{\"labelZh\":\"滑雪\",\"labelEn\":\"Skiing\",\"value\":\"skiing\"}]",
                DefaultValue = "[]",
                Colspan = 2,
                SortOrder = 3,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "mobile",
                LabelZh = "手机号",
                LabelEn = "Mobile",
                Type = "input",
                Required = true,
                NewLine = true,
                Option = null,
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 4,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "trialTime",
                LabelZh = "试验参与时间",
                LabelEn = "TrialTime",
                Type = "date_range",
                Required = true,
                NewLine = false,
                Option = null,
                DefaultValue = "{\"start\":\"\",\"end\":\"\"}",
                Colspan = 2,
                SortOrder = 5,
                ExtraConfig = "{\"format\":\"YYYY-MM-DD\"}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "period",
                LabelZh = "访试周期",
                LabelEn = "Period",
                Type = "unit_select",
                Required = true,
                NewLine = false,
                Option =
                    "[{\"labelZh\":\"月\",\"labelEn\":\"Month\",\"value\":\"month\"},{\"labelZh\":\"周\",\"labelEn\":\"Week\",\"value\":\"week\"},{\"labelZh\":\"天\",\"labelEn\":\"Day\",\"value\":\"day\"}]",
                DefaultValue = "{\"value\":\"\",\"unit\":\"\"}",
                Colspan = 2,
                SortOrder = 6,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-07 14:40:08.9367730"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "edu_group",
                LabelZh = "教育经历",
                LabelEn = "",
                Type = "multiSubForm",
                Required = false,
                NewLine = false,
                Option = null,
                DefaultValue = "[{\"edu_school_name\":\"\",\"edu_level\":\"\",\"edu_end\":\"\"}]",
                Colspan = 4,
                SortOrder = 7,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = "edu_group",
                GroupCode = "grp_1746628396941-8913",
                Code = "edu_school_name",
                LabelZh = "学校名称",
                LabelEn = "",
                Type = "input",
                Required = false,
                NewLine = false,
                Option = null,
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 8,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = "edu_group",
                GroupCode = "grp_1746628396941-8913",
                Code = "edu_level",
                LabelZh = "学历",
                LabelEn = "",
                Type = "select",
                Required = false,
                NewLine = false,
                Option =
                    "[{\"labelZh\":\"高中\",\"labelEn\":\"Option 1\",\"value\":\"option1\"},{\"labelZh\":\"专科\",\"labelEn\":\"Option 2\",\"value\":\"option2\"},{\"labelZh\":\"本科\",\"labelEn\":\"Option 3\",\"value\":\"option3\"}]",
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 9,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = "edu_group",
                GroupCode = "grp_1746628396941-8913",
                Code = "edu_end",
                LabelZh = "毕业时间",
                LabelEn = "",
                Type = "date",
                Required = false,
                NewLine = false,
                Option = null,
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 10,
                ExtraConfig = "{\"format\":\"YYYY-MM-DD\"}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = null,
                GroupCode = "grp_1746628396941-8913",
                Code = "random_group",
                LabelZh = "随机分组",
                LabelEn = "Random Group",
                Type = "subForm",
                Required = false,
                NewLine = false,
                Option = null,
                DefaultValue = "null",
                Colspan = 4,
                SortOrder = 11,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = "random_group",
                GroupCode = "grp_1746628396941-8913",
                Code = "random_group_sex",
                LabelZh = "所属随机性别分组",
                LabelEn = "",
                Type = "radio",
                Required = false,
                NewLine = false,
                Option =
                    "[{\"labelZh\":\"男\",\"labelEn\":\"Male\",\"value\":\"male\"},{\"labelZh\":\"女\",\"labelEn\":\"Female\",\"value\":\"female\"}]",
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 12,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            },
            new FormFieldEntity
            {
                Key = IdGenerator.NextId(),
                FormCode = "demo",
                Version = "20250507144008890",
                ParentCode = "random_group",
                GroupCode = "grp_1746628396941-8913",
                Code = "random_group_name",
                LabelZh = "随机分组",
                LabelEn = "",
                Type = "select",
                Required = false,
                NewLine = false,
                Option =
                    "[{\"labelZh\":\"随机组\",\"labelEn\":\"Random Group\",\"value\":\"random_group\"},{\"labelZh\":\"对照组\",\"labelEn\":\"Control Group\",\"value\":\"control_group\"}]",
                DefaultValue = "\"\"",
                Colspan = 2,
                SortOrder = 13,
                ExtraConfig = "{}",
                CreatedTime = DateTime.Parse("2025-05-12T07:10:40.5598890+00:00"),
                CreatedBy = "System",
                LastModifiedTime = null,
                LastModifiedBy = null,
                Deleted = false
            }
        );
    }
}
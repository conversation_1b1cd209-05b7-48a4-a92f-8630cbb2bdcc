using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.DynamicForm.WebApi.Mgt.Controllers;

/// <summary>
/// FormInstance 控制器
/// </summary>
public class
    FormInstanceController : BaseAppController<long, FormInstanceDto, IFormInstanceService, FormInstanceQueryCriteria>
{
    private readonly IFormInstanceDataService _formInstanceDataService;
    private readonly IFormFieldInstanceService _formFieldInstanceService;

    public FormInstanceController(IServiceProvider serviceProvider, IFormInstanceDataService formInstanceDataService,
        IFormFieldInstanceService formFieldInstanceService) :
        base(serviceProvider)
    {
        _formInstanceDataService = formInstanceDataService;
        _formFieldInstanceService = formFieldInstanceService;
    }

    /// <summary>
    /// 更新表单数据
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <param name="formData"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission(true)]
    [HttpPost("instance/{businessId}/{version}/data")]
    public async Task<bool> SetFormDataAsync(string businessId, string version,
        [FromBody] Dictionary<string, string?> formData)
    {
        var formInstanceDto = await Service.GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await _formInstanceDataService.SetFormDataAsync(formInstanceDto, formData);
    }


    /// <summary>
    /// 获取表单业务数据
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [PublicPermission(true)]
    [HttpGet("instance/formData/{businessId}")]
    public async Task<Dictionary<string, string?>> GetFormDataAsync(string businessId)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);

        var result = await _formInstanceDataService.GetByFormInstanceAsync(formInstanceDto);

        return result.ToDictionary(k => k.Code, v => v.Value);
    }

    /// <summary>
    /// 确认当前版本表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [UnitOfWork]
    // [PublicPermission]
    [HttpPost("instance/confirm/{businessId}/{version}")]
    public async Task<bool> ConfirmInstanceAsync(string businessId, string version)
    {
        return await Service.ConfirmInstanceAsync(businessId, version);
    }


    /// <summary>
    /// 确认表单
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [UnitOfWork]
    // [PublicPermission]
    [HttpPost("instance/confirm/{businessId}")]
    public async Task<bool> ConfirmInstanceAsync(string businessId)
    {
        return await Service.ConfirmInstanceAsync(businessId);
    }

    /// <summary>
    /// 获取表单实体定义
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [HttpGet("entity/{businessId}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<FormInstanceDto> GetFormInstanceAsync(string businessId)
    {
        return await Service.GetNewestFormInstanceDtoAsync(businessId);
    }

    /// <summary>
    /// 获取表单实体定义
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <returns></returns>
    [HttpGet("entity/{businessId}/{version}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<FormInstanceDto> GetFormInstanceAsync(string businessId, string version)
    {
        return await Service.GetNamedVersionFormInstanceDtoAsync(businessId, version);
    }

    /// <summary>
    /// 保存当前版本表单实例
    /// 与用户端保存实例接口有差异
    /// 该接口以原值为基础使用新值进行替换后创建新版本，主要用于审批通过类的保存动作，比如分配注册号
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission(true)]
    [HttpPut("instance/{businessId}/{version}")]
    public async Task<bool> SaveInstanceAsync(string businessId, string version,
        [FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await Service.SaveAsync(businessId, version, formDefinitionDto);
    }

    /// <summary>
    /// 保存表单
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="formDefinitionDto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission(true)]
    [HttpPut("instance/{businessId}")]
    public async Task<bool> SaveInstanceAsync(string businessId,
        [FromBody] FormDefinitionDto formDefinitionDto)
    {
        return await Service.SaveAsync(businessId, formDefinitionDto);
    }


    /// <summary>
    /// 驳回当前版本表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <param name="annotationValues"></param>
    /// <returns></returns>
    [UnitOfWork]
    // [PublicPermission]
    [HttpPost("instance/reject/{businessId}/{version}")]
    public async Task<bool> RejectInstanceAsync(string businessId, string version,
        [FromBody] Dictionary<string, AnnotationValue?>? annotationValues)
    {
        return await Service.RejectInstanceAsync(businessId, version, annotationValues);
    }

    /// <summary>
    /// 驳回表单实例
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="annotationValues"></param>
    /// <returns></returns>
    [UnitOfWork]
    // [PublicPermission]
    [HttpPost("instance/reject/{businessId}")]
    public async Task<bool> RejectInstanceAsync(string businessId,
        [FromBody] Dictionary<string, AnnotationValue?>? annotationValues)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);
        return await Service.RejectInstanceAsync(formInstanceDto, annotationValues);
    }


    /// <summary>
    /// 保存当前版本表单批注数据
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="version"></param>
    /// <param name="annotationValues"></param>
    /// <returns></returns>
    [UnitOfWork]
    // [PublicPermission]
    [HttpPost("instance/save-annotation/{businessId}/{version}")]
    public async Task<bool> SaveAnnotationInstanceAsync(string businessId, string version,
        [FromBody] Dictionary<string, AnnotationValue?>? annotationValues)
    {
        return await Service.SaveInstanceAnnotationAsync(businessId, version, annotationValues);
    }

    /// <summary>
    /// 保存最新版本表单批注数据
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="annotationValues"></param>
    /// <returns></returns>
    [UnitOfWork]
    // [PublicPermission]
    [HttpPost("instance/save-annotation/{businessId}")]
    public async Task<bool> SaveAnnotationInstanceAsync(string businessId,
        [FromBody] Dictionary<string, AnnotationValue?>? annotationValues)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);
        return await Service.SaveInstanceAnnotationAsync(formInstanceDto, annotationValues);
    }


    /// <summary>
    /// 分页获取表单实例
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> GetPageAsync(
        [FromQuery] PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    /// <summary>
    /// 分页获取表单最新状态实例
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("newest-page")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<FormInstancePageDto> GetNewestPageAsync(
        [FromQuery] PagedQueryCriteria<FormInstanceQueryCriteria> criteria)
    {
        return await Service.GetNewestPageAsync(criteria);
    }

    [HttpPost("count/{formCode}")]
    [IgnoreLogging]
    [PublicPermission(true)]
    public async Task<int> GetCountAsync([FromRoute] string formCode, [FromBody] GetFormInstanceCountDto input)
    {
        return await Service.GetCountAsync(formCode, input.Status, input.DynamicQueries,
            input.FormDataDynamicQueries);
    }

    [HttpPost("newest-count/{formCode}")]
    [IgnoreLogging]
    [PublicPermission(true)]
    public async Task<int> GetNewestCountAsync([FromRoute] string formCode, [FromBody] GetFormInstanceCountDto input)
    {
        return await Service.GetNewestCountAsync(formCode, input.Status, input.DynamicQueries,
            input.FormDataDynamicQueries);
    }

    /// <summary>
    /// 保存表单业务数据
    /// </summary>
    /// <param name="businessId"></param>
    /// <param name="formData"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission(true)]
    [HttpPost("instance/formData/{businessId}")]
    public async Task<bool> SetFormDataAsync(string businessId, [FromBody] Dictionary<string, string?> formData)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);

        var result = await _formInstanceDataService.SetFormDataAsync(formInstanceDto, formData);

        return result;
    }


    [ApplicationPermission]
    [IgnoreLogging]
    [HttpPut("app/instance/formData/{businessId}/{version}")]
    public async Task<bool> AppSetFormDataAsync(string businessId, string version,
        [FromBody] Dictionary<string, string?> formData)
    {
        var formInstanceDto = await Service.GetNamedVersionFormInstanceDtoAsync(businessId, version);

        return await _formInstanceDataService.SetFormDataAsync(formInstanceDto, formData);
    }

    /// <summary>
    /// 清空表单批注数据
    /// </summary>
    /// <param name="businessId"></param>
    /// <returns></returns>
    [UnitOfWork]
    [PublicPermission(true)]
    [HttpPost("instance/annotation/{businessId}/clear")]
    public async Task<bool> ClearFormAnnotationAsync(string businessId)
    {
        var formInstanceDto = await Service.GetNewestFormInstanceDtoAsync(businessId);

        var result = await _formFieldInstanceService.SaveAnnotationsAsync(formInstanceDto, null);

        return result;
    }


    /// <summary>
    /// 自定义查询表单业务数据结果 通过queryCode协定
    /// </summary>
    /// <param name="queryCode"></param>
    /// <param name="queryParameters"></param>
    /// <returns></returns>
    [HttpPost("formData/query/{queryCode}")]
    [PublicPermission(true)]
    public async Task<object> QueryFormDataAsync(string queryCode,
        [FromBody] Dictionary<string, string> queryParameters)
    {
        return await _formInstanceDataService.QueryFormDataAsync(queryCode, queryParameters);
    }
}

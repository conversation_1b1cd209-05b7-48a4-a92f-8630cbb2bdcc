using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;

namespace XJ.Framework.DynamicForm.Application.Services;

public partial class FormInstanceService
{
    public async Task<bool> ConfirmInstanceAsync(string businessId, string version)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await ConfirmInstanceAsync(formInstance);
    }

    public async Task<bool> ConfirmInstanceAsync(FormInstanceDto formInstance)
    {
        if (formInstance.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("只有已提交的版本可以确认/Confirmation is only allowed for submitted versions.");
        }

        if (formInstance.IsObsoleted)
        {
            throw new ValidationException("已过期版本，无法进行该操作/ The version has expired and cannot be operated on.");
        }

        var confirmedFormInstances = (await Repository.GetListAsync(q =>
            q.BusinessId.ToLower().Equals(formInstance.BusinessId.ToLower()) &&
            q.Status == FormInstanceStatus.Confirmed)).ToList();
        if (confirmedFormInstances.Any())
        {
            confirmedFormInstances.ForEach(q => q.IsObsoleted = true);
            await Repository.UpdateAsync(confirmedFormInstances);
        }

        var formInstanceOperationDto = Mapper.Map<FormInstanceOperationDto>(formInstance);
        formInstanceOperationDto.Status = FormInstanceStatus.Confirmed;
        await UpdateAsync(formInstance.Key, formInstanceOperationDto);

        await _formFieldInstanceService.SaveAnnotationsAsync(formInstance, null);
        // await _formFieldInstanceService.SaveWarningsAsync(formInstance, null);

        return true;
    }

    public async Task<bool> ConfirmInstanceAsync(string businessId)
    {
        var formInstance = await GetNewestFormInstanceDtoAsync(businessId);
        return await ConfirmInstanceAsync(formInstance);
    }


    public async Task<bool> SubmitInstanceAsync(string businessId)
    {
        var formInstance = await GetNewestFormInstanceDtoAsync(businessId);
        return await SubmitInstanceAsync(formInstance);
    }

    public async Task<bool> SubmitInstanceAsync(FormInstanceDto formInstance)
    {
        if (formInstance.Status != FormInstanceStatus.Draft)
        {
            throw new ValidationException("只有草稿的版本可以提交/ Only draft versions can be submitted.");
        }

        if (formInstance.ApplyUserId != _currentUserContext.GetCurrentUserId())
        {
            throw new ValidationException("只有发起人可以提交/ Only the initiator can submit.");
        }

        if (formInstance.IsObsoleted)
        {
            throw new ValidationException("已过期版本，无法进行该操作/ The version has expired and cannot be operated on.");
        }

        var formInstanceOperationDto = Mapper.Map<FormInstanceOperationDto>(formInstance);
        formInstanceOperationDto.Status = FormInstanceStatus.Submitted;
        await UpdateAsync(formInstance.Key, formInstanceOperationDto);
        return true;
    }

    public async Task<bool> SubmitInstanceAsync(string businessId, string version)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        return await SubmitInstanceAsync(formInstance);
    }


    public async Task<bool> CancelInstanceAsync(string businessId, string version)
    {
        var formInstance = await GetNamedVersionFormInstanceDtoAsync(businessId, version);
        if (formInstance.Status != FormInstanceStatus.Draft && formInstance.Status != FormInstanceStatus.Rejected)
        {
            throw new ValidationException("只有草稿或已驳回可以作废/ Only drafts or rejected versions can be cancelled.");
        }

        if (formInstance.ApplyUserId != _currentUserContext.GetCurrentUserId())
        {
            throw new ValidationException("只有发起人可以作废/ Only the initiator can cancel.");
        }

        if (formInstance.IsObsoleted)
        {
            throw new ValidationException("已过期版本，无法进行该操作/ The version has expired and cannot be operated on.");
        }

        var referenceFormInstances = (await Repository.GetListAsync(q =>
            q.PreviousInstanceId == formInstance.Key)).ToList();
        if (referenceFormInstances.Any())
        {
            throw new ValidationException(
                "当前版本已经被其他版本引用，不能作废/ The current version has been referenced by other versions and cannot be cancelled.");
        }

        var formInstanceOperationDto = Mapper.Map<FormInstanceOperationDto>(formInstance);
        formInstanceOperationDto.Status = FormInstanceStatus.Cancelled;
        formInstanceOperationDto.IsObsoleted = true;
        await UpdateAsync(formInstance.Key, formInstanceOperationDto);

        return true;
    }

    public async Task<bool> RejectInstanceAsync(string businessId, string version,
        // Dictionary<string, string>? warnings
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        var formInstance = await GetNamedInstanceAsync(businessId, version);
        formInstance.NullCheck();
        return await RejectInstanceAsync(formInstance!, annotationValues);
    }

    public async Task<bool> RejectInstanceAsync(FormInstanceDto formInstance,
        // Dictionary<string, string>? warnings
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        if (formInstance!.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("只有已提交的版本可以驳回/ Only submitted versions can be rejected.");
        }

        if (formInstance.IsObsoleted)
        {
            throw new ValidationException("已过期版本，无法进行该操作/ The version has expired and cannot be operated on.");
        }

        var formInstanceOperationDto = Mapper.Map<FormInstanceOperationDto>(formInstance);
        formInstanceOperationDto.Status = FormInstanceStatus.Rejected;

        await UpdateAsync(formInstance.Key, formInstanceOperationDto);

        // await _formFieldInstanceService.SaveWarningsAsync(formInstance, warnings);
        await _formFieldInstanceService.SaveAnnotationsAsync(formInstance, annotationValues);

        return true;
    }


    public async Task<bool> SaveInstanceAnnotationAsync(string businessId, string version,
        // Dictionary<string, string>? warnings
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        var formInstance = await GetNamedInstanceAsync(businessId, version);
        formInstance.NullCheck();
        return await SaveInstanceAnnotationAsync(formInstance!, annotationValues);
    }

    public async Task<bool> SaveInstanceAnnotationAsync(FormInstanceDto formInstance,
        // Dictionary<string, string>? warnings
        Dictionary<string, AnnotationValue?>? annotationValues)
    {
        if (formInstance!.Status != FormInstanceStatus.Submitted)
        {
            throw new ValidationException("只有已提交的版本可以进行该操作/ Only submitted versions can be saved.");
        }

        if (formInstance.IsObsoleted)
        {
            throw new ValidationException("已过期版本，无法进行该操作/ The version has expired and cannot be operated on.");
        }

        // await _formFieldInstanceService.SaveWarningsAsync(formInstance, warnings);
        await _formFieldInstanceService.SaveAnnotationsAsync(formInstance, annotationValues);

        return true;
    }
}


namespace XJ.Framework.DynamicForm.Application.Services;

/// <summary>
/// FormFieldGroup 服务实现
/// </summary>
public sealed class FormFieldGroupService :
    BaseEditableAppService<long, FormFieldGroupEntity, FormFieldGroupDto, FormFieldGroupOperationDto, IFormFieldGroupRepository, FormFieldGroupQueryCriteria>,
    IFormFieldGroupService
{
    public FormFieldGroupService(IFormFieldGroupRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 
using System.Text.Json;
using System.Text.Json.Serialization;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;

namespace XJ.Framework.DynamicForm.Application.JsonConverters;

public class MultiTypeValueConverter : JsonConverter<MultiTypeValue>
{
    public override MultiTypeValue? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        MultiTypeValue result = new MultiTypeValue();

        if (reader.TokenType == JsonTokenType.Number)
        {
            result.IntValue = reader.GetInt32();
        }
        else if (reader.TokenType == JsonTokenType.String)
        {
            result.StringValue = reader.GetString();
        }
        else if (reader.TokenType == JsonTokenType.StartArray)
        {
            using (var doc = JsonDocument.ParseValue(ref reader))
            {
                var root = doc.RootElement;
                if (root.GetArrayLength() == 0 || root[0].ValueKind == JsonValueKind.String)
                {
                    // 字符串数组
                    var list = root.EnumerateArray().Select(e => e.GetString()!).ToList();
                    result.StringArray = list;
                }
                else if (root[0].ValueKind == JsonValueKind.Object)
                {
                    // 对象数组
                    var objList = new List<Dictionary<string, object?>>();
                    foreach (var elem in root.EnumerateArray())
                    {
                        var dict = JsonSerializer.Deserialize<Dictionary<string, object?>>(elem.GetRawText(), options);
                        if (dict != null)
                            objList.Add(dict);
                    }

                    result.ObjectArray = objList;
                }
                else
                {
                    throw new JsonException("Unsupported array element type for MultiTypeValue");
                }
            }
        }
        else if (reader.TokenType == JsonTokenType.StartObject)
        {
            var dict = JsonSerializer.Deserialize<Dictionary<string, object?>>(ref reader, options);
            result.KeyValue = dict;
        }
        else if (reader.TokenType == JsonTokenType.Null)
        {
            return null;
        }
        else
        {
            throw new JsonException("Unknown type for MultiTypeValue");
        }

        return result;
    }

    public override void Write(Utf8JsonWriter writer, MultiTypeValue value, JsonSerializerOptions options)
    {
        if (value.IntValue != null)
        {
            writer.WriteNumberValue(value.IntValue.Value);
        }
        else if (value.StringValue != null)
        {
            writer.WriteStringValue(value.StringValue);
        }
        else if (value.StringArray != null)
        {
            JsonSerializer.Serialize(writer, value.StringArray, options);
        }
        else if (value.KeyValue != null)
        {
            JsonSerializer.Serialize(writer, value.KeyValue, options);
        }
        else if (value.ObjectArray != null)
        {
            JsonSerializer.Serialize(writer, value.ObjectArray, options);
        }
        else
        {
            writer.WriteNullValue();
        }
    }
}
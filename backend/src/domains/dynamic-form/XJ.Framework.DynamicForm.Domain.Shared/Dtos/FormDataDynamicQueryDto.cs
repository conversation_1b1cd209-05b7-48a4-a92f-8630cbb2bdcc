namespace XJ.Framework.DynamicForm.Domain.Shared.Dtos;

public class FormDataDynamicQuery
{
    public FormDataDynamicQuery()
    {
    }

    public FormDataDynamicQuery(string key, FormDataQueryOperator @operator, string? value)
    {
        Key = key;
        Operator = @operator;
        Value = value;
    }

    public string Key { get; set; } = null!;

    /// <summary>
    /// Equal Empty NotEqual
    /// </summary>
    public FormDataQueryOperator Operator { get; set; }

    public string? Value { get; set; }
}

public enum FormDataQueryOperator
{
    Equal = 1,
    Empty = 2,
    NotEqual = 3,
    In = 4,
    NotEmpty = 5,
    NotEqualOrNotExist = 6
}

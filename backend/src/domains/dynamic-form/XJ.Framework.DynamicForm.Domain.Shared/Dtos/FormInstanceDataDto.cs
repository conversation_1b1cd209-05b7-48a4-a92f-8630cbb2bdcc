using System.Text.Json.Serialization;
using XJ.Framework.DynamicForm.Domain.Shared.Enums;

namespace XJ.Framework.DynamicForm.Domain.Shared.Dtos;

/// <summary>
/// FormInstanceData DTO
/// </summary>
public class FormInstanceDataDto : BaseDto<long>
{
    /// <summary>
    /// 表单编码
    /// </summary>
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    public string FormVersion { get; set; } = null!;


    /// <summary>
    /// 当前实例版本
    /// </summary>
    public string Version { get; set; } = null!;

    /// <summary>
    /// 当前实例业务id
    /// </summary>
    public string BusinessId { get; set; } = null!;

    /// <summary>
    /// 数据编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 数据值
    /// </summary>
    public string? Value { get; set; }
}
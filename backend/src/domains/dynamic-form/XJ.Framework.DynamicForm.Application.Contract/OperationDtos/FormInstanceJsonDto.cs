using XJ.Framework.DynamicForm.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

public class FormInstanceJsonDto : BaseDto<long>
{
    public FormInstanceStatus Status { get; set; }
    public Dictionary<string, MultiTypeValue?> Value { get; set; } = new();

    public Dictionary<string, string?> FormData { get; set; } = new Dictionary<string, string?>();
    public string Version { get; set; } = null!;
    public string FormVersion { get; set; } = null!;
    public string FormCode { get; set; } = null!;
    public string BusinessId { get; set; } = null!;
}

public class FormInstancePageDto : PageDtoData<long, FormInstanceJsonDto>
{
    public FormDefinitionDto FormDefinition { get; set; } = null!;
}
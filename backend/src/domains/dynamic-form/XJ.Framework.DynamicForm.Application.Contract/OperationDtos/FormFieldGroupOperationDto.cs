namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

/// <summary>
/// FormFieldGroup 操作 DTO
/// </summary>
public class FormFieldGroupOperationDto : BaseOperationDto
{
    /// <summary>
    /// 表单编码
    /// </summary>
    public string FormCode { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    public string Version { get; set; } = null!;

    /// <summary>
    /// 分组编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 中文标题
    /// </summary>
    public string? TitleZh { get; set; }

    /// <summary>
    /// 英文标题
    /// </summary>
    public string? TitleEn { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int SortOrder { get; set; }
}
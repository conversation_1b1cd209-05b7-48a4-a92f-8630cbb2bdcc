namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

/// <summary>
/// Form 操作 DTO
/// </summary>
public class FormOperationDto : BaseOperationDto
{
    /// <summary>
    /// 表单唯一编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 表单名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    public string Version { get; set; } = null!;

    /// <summary>
    /// 表单描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 全局配置（json）
    /// </summary>
    public string? JsonConfig { get; set; }

    /// <summary>
    /// 是否最新版本
    /// </summary>
    public bool NewestVersion { get; set; }
}
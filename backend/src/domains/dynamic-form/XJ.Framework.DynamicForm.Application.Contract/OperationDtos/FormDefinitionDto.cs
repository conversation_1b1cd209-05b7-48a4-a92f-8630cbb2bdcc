using Microsoft.AspNetCore.Mvc.ActionConstraints;
using System.Text.Json;
using System.Text.Json.Serialization;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;

namespace XJ.Framework.DynamicForm.Application.Contract.OperationDtos;

public class FormDefinitionDto
{
    public MultiTypeValue? GetFieldValue(string fieldCode)
    {
        foreach (var group in this.Groups)
        {
            foreach (var field in group.Fields)
            {
                if (field.Code.Equals(fieldCode, StringComparison.OrdinalIgnoreCase))
                {
                    return field.Value;
                }

                else
                {
                    foreach (var subField in field.Fields)
                    {
                        if (subField.Code.Equals(fieldCode, StringComparison.OrdinalIgnoreCase))
                        {
                            return subField.Value;
                        }
                    }
                }
            }
        }

        return null;
    }

    public void SetFieldValue(string fieldCode, MultiTypeValue? value)
    {
        this.Groups.ForEach(group =>
        {
            group.Fields.ForEach(field =>
            {
                if (field.Code.Equals(fieldCode, StringComparison.OrdinalIgnoreCase))
                {
                    field.Value = value;
                }
                else
                {
                    field.Fields.ForEach(subField =>
                    {
                        if (subField.Code.Equals(fieldCode, StringComparison.OrdinalIgnoreCase))
                        {
                            subField.Value = value;
                        }
                    });
                }
            });
        });
    }

    public string Version { get; set; } = null!;
    public string? PreviousVersion { get; set; }
    public string? Language { get; set; } = "both";

    public string Code { get; set; } = null!;

    public Dictionary<string, string?> FormData { get; set; } = new();

    public List<GroupDto> Groups { get; set; } = new List<GroupDto>();

    public Dictionary<string, object>? JsonConfig { get; set; }
}

public class GroupDto
{
    public string Id { get; set; } = null!;
    public required string Code { get; set; }
    public required string LabelZh { get; set; }
    public required string LabelEn { get; set; }
    public List<FieldDto> Fields { get; set; } = new List<FieldDto>();
}

public class FieldDto
{
    public string Id { get; set; } = null!;
    public required string Code { get; set; }
    public required string Type { get; set; }
    public bool NewLine { get; set; }
    public required string LabelZh { get; set; }
    public required string LabelEn { get; set; }
    public bool Required { get; set; }
    public int Unit { get; set; }
    public List<OptionDto>? Options { get; set; }

    public MultiTypeValue? Value { get; set; }

    public MultiTypeValue? PreviousValue { get; set; }

    public AnnotationValue? Annotations { get; set; }
    public ExtendDto? Extends { get; set; }

    public List<FieldDto> Fields { get; set; } = new List<FieldDto>();

    public bool InitReadonly { get; set; } = false;

    public Dictionary<string, object>? Description { get; set; }

    public List<string> RejectReasons { get; set; } = new List<string>();


    // public string? Warning { get; set; }
}

public class ExtendDto : Dictionary<string, object>
{
}

public class OptionDto
{
    public required string LabelZh { get; set; }
    public required string LabelEn { get; set; }
    public required string Value { get; set; }
}
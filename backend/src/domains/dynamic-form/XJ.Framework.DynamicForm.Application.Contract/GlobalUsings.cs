
// global using 指令

// System 命名空间
global using System;
global using System.Collections.Generic;

// XJ.Framework 基础库
global using XJ.Framework.Library.Application.Contract.Interfaces;
global using XJ.Framework.Library.Application.Contract.OperationDtos;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;

// XJ.Framework Rbac 模块
global using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
global using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
global using XJ.Framework.DynamicForm.Domain.Shared.Dtos; 
using XJ.Framework.DynamicForm.Domain.Shared.Enums;

namespace XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;

/// <summary>
/// FormInstance 查询条件
/// </summary>
public class FormInstanceQueryCriteria : BaseQueryCriteria
{
    [Equal] public string FormCode { get; set; } = null!;
    [In] public List<FormInstanceStatus>? Status { get; set; }
    [Equal] public bool? IsObsoleted { get; set; }

    public List<string> Columns { get; set; } = new();
    
    public Dictionary<string, DynamicFormQueryDto> DynamicQueries { get; set; } = new();

    public List<FormDataDynamicQuery> FormDataDynamicQueries { get; set; } = new List<FormDataDynamicQuery>();
}

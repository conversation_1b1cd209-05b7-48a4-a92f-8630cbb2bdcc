using Microsoft.AspNetCore.Mvc;
using System.Collections.Specialized;
using System.Text;
using System.Web;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Files.ApiClient;

public class DynamicFormApplicationApiClient : BaseApplicationApiClient
{
    private readonly string _baseUrl;
    private readonly DynamicFormApiClientHelper _helper;

    public DynamicFormApplicationApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption,
        ILogger<BaseApiClient> logger, IAuthInfoGetter authInfoGetter,
        IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        ICurrentApplicationContext applicationContext, DynamicFormApiClientHelper helper,
        IOptions<JsonOptions> jsonOptions) : base(httpClient, logger,
        authInfoGetter, contextContainer, currentUserContext, applicationContext, jsonOptions)
    {
        _helper = helper;
        _baseUrl = endpointOption.Value["DynamicForm"]!.Url.TrimEnd('/');
    }

    public async Task<FormDefinitionDto> GetDifferenceFormInstanceAsync(string businessId, string version)
    {
        var url = $"{_baseUrl}/FormInstance/app/instance/diff/{businessId}/{version}";

        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }
    public async Task<FormDefinitionDto> GetNamedVersionFormInstanceAsync(string businessId, string version)
    {
        var url = $"{_baseUrl}/FormInstance/app/instance/{businessId}/{version}";
        var formDefinition = await InternalGetAsync<FormDefinitionDto>(url);
        return formDefinition;
    }
}

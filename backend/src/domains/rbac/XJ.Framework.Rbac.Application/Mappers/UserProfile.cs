using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using XJ.Framework.Library.Application.Mappers;
using XJ.Framework.Library.Domain.Shared.Dtos;


/// <summary>
/// User Profile
/// </summary>
public class UserProfile : Profile
{
    public UserProfile()
    {
        CreateMap<UserEntity, UserDto>();
        CreateMap<UserOperationDto, UserEntity>();

        CreateMap<UserDto, UserProfileDto>()
            .ForMember(
                dest => dest.Avatar,
                opt => opt.MapFrom(src => MapperHelper.ParseFileDto(src.Avatar))
            );
    }
}
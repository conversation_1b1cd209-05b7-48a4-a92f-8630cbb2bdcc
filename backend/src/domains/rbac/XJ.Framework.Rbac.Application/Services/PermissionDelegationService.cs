
using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// PermissionDelegation 服务实现
/// </summary>
public sealed class PermissionDelegationService :
    BaseEditableAppService<long, PermissionDelegationEntity, PermissionDelegationDto, PermissionDelegationOperationDto, IPermissionDelegationRepository, PermissionDelegationQueryCriteria>,
    IPermissionDelegationService
{
    public PermissionDelegationService(IPermissionDelegationRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// UserPosition 服务实现
/// </summary>
public sealed class UserPositionService :
    BaseEditableAppService<long, UserPositionEntity, UserPositionDto, UserPositionOperationDto, IUserPositionRepository,
        UserPositionQueryCriteria>,
    IUserPositionService
{
    private readonly IOrganizationService _organizationService;
    private readonly IPositionRepository _positionRepository;
    private readonly ICurrentUserContext _currentUserContext;

    public UserPositionService(IUserPositionRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext,
        IOrganizationService organizationService, IPositionRepository positionRepository) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _organizationService = organizationService;
        _positionRepository = positionRepository;
        _currentUserContext = currentUserContext;
    }

    public async Task<bool> SetUserPositions(long userId, List<UserPositionOperationDto> userPositions)
    {
        var originals = await Repository.GetListAsync(q => q.UserId == userId);
        // 使用positionid 和organizationid 比较原始数据和新数据 需要比较出新增数据、删除数据、修改数据(更新isprimary、starttime endtime、status)
        var originalsDict = originals.ToDictionary(q => (q.PositionId, q.OrganizationId));
        var newDict = userPositions.ToDictionary(q => (q.PositionId, q.OrganizationId));
        var addList = new List<UserPositionEntity>();
        var updateList = new List<UserPositionEntity>();
        var deleteList = new List<UserPositionEntity>();
        foreach (var item in newDict)
        {
            if (originalsDict.TryGetValue(item.Key, out var original))
            {
                // 更新
                original.IsPrimary = item.Value.IsPrimary;
                original.StartTime = item.Value.StartTime;
                original.EndTime = item.Value.EndTime;
                original.Status = item.Value.Status;
                updateList.Add(original);
            }
            else
            {
                // 新增
                var newEntity = Mapper.Map<UserPositionEntity>(item.Value);
                newEntity.UserId = userId;
                newEntity.Key = KeyGenerator.GenerateKey();
                addList.Add(newEntity);
            }
        }

        foreach (var item in originalsDict)
        {
            if (!newDict.ContainsKey(item.Key))
            {
                // 删除
                deleteList.Add(item.Value);
            }
        }

        if (addList.Count > 0)
        {
            await Repository.InsertAsync(addList);
        }

        if (updateList.Count > 0)
        {
            await Repository.UpdateAsync(updateList);
        }

        if (deleteList.Count > 0)
        {
            await Repository.DeleteAsync(deleteList);
        }


        return true;
    }

    public async Task<List<long>> GetPositionUsersAsync(string positionCode, string organizationCode)
    {
        var position = await _positionRepository.GetAsync(q => q.Code.ToLower().Equals(positionCode.ToLower()));

        position.NullCheck();

        var organizations = await _organizationService.GetOrganizationChildrenAsync(organizationCode, true);

        var userIds = (await Repository.LoadAsync(q =>
            q.Status == UserPositionStatus.Active
            &&
            (
                (!q.StartTime.HasValue && !q.EndTime.HasValue)
                ||
                (q.StartTime.HasValue && q.StartTime.Value <= DateTime.Now && q.EndTime.HasValue &&
                 q.EndTime >= DateTime.Now)
            )
            &&
            organizations.Any(o => o.Key.Equals(q.OrganizationId))
            &&
            q.PositionId == position!.Key
        )).Select(q => q.UserId).ToList();
        return userIds;
    }

    public async Task<List<long>> GetPositionUsersAsync(string positionCode)
    {
        var position = await _positionRepository.GetAsync(q => q.Code.ToLower().Equals(positionCode.ToLower()));

        position.NullCheck();

        var userIds = (await Repository.LoadAsync(q =>
            q.Status == UserPositionStatus.Active
            &&
            (
                (!q.StartTime.HasValue && !q.EndTime.HasValue)
                ||
                (q.StartTime.HasValue && q.StartTime.Value <= DateTime.Now && q.EndTime.HasValue &&
                 q.EndTime >= DateTime.Now)
            )
            &&
            q.PositionId == position!.Key
        )).Select(q => q.UserId).ToList();
        return userIds;
    }

    /// <summary>
    /// 根据userId查询绑定所有的岗位code
    /// </summary>
    /// <param name="userId"></param>
    /// <returns>岗位code列表</returns>
    public async Task<List<PositionDto>> GetUserPositionsAsync()
    {
        return await GetUserPositionsAsync(_currentUserContext.GetCurrentUserId()!.Value);
    }

    public async Task<List<PositionDto>> GetUserPositionsAsync(long userId)
    {
        // 查询用户所有岗位关系
        var originals = await Repository.GetListAsync(q => q.UserId == userId);
        var positionIds = originals.Select(x => x.PositionId).Distinct().ToList();
        if (positionIds.Count == 0)
            return new List<PositionDto>();
        // 查询所有岗位实体
        var positions = await _positionRepository.GetListAsync(p => positionIds.Contains(p.Key));
        // 提取所有岗位code
        return positions.Select(q => Mapper.Map<PositionDto>(q)).ToList();
        ;
    }
}
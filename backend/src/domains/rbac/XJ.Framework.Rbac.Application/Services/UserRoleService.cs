using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// UserRole 服务实现
/// </summary>
public sealed class UserRoleService :
    BaseEditableAppService<long, UserRoleEntity, UserRoleDto, UserRoleOperationDto, IUserRoleRepository,
        UserRoleQueryCriteria>,
    IUserRoleService
{
    private readonly ICurrentUserContext _currentUserContext;
    public UserRoleService(IUserRoleRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _currentUserContext = currentUserContext;
    }

    public async Task<bool> IsAdminUser(long userId)
    {
        return await Repository.HasRoleAsync(userId, "ADMIN");
    }

    public async Task<bool> SetUserRoles(long userId, List<UserRoleOperationDto> userRoles)
    {
        var originals = await Repository.GetListAsync(q => q.UserId == userId);
        // 比较原始数据和新数据 通过二者的RoleId 需要比较出新增数据、删除数据
        var originalRoleIds = originals.Select(x => x.RoleId).ToList();
        var newRoleIds = userRoles.Select(x => x.RoleId).ToList();
        var addRoleIds = newRoleIds.Except(originalRoleIds).ToList();
        var removeRoleIds = originalRoleIds.Except(newRoleIds).ToList();

        // 删除原有的角色
        var deletedEntities = new List<UserRoleEntity>();
        foreach (var removeRoleId in removeRoleIds)
        {
            var userRole = originals.FirstOrDefault(x => x.RoleId == removeRoleId);
            if (userRole != null)
            {
                deletedEntities.Add(userRole);
            }
        }

        if (deletedEntities.Any())
        {
            await Repository.DeleteAsync(deletedEntities);
        }

        // 添加新的角色
        var addedEntities = new List<UserRoleEntity>();
        foreach (var addRoleId in addRoleIds)
        {
            var userRole = new UserRoleEntity
            {
                Key = IdGenerator.NextId(),
                UserId = userId,
                RoleId = addRoleId
            };
            addedEntities.Add(userRole);
        }

        if (addedEntities.Any())
        {
            await Repository.InsertAsync(addedEntities);
        }


        return true;
    }

    public async Task<List<string>> GetUserRoleCodesAsync(long userId)
    {
        return await Repository.GetUserRoleCodesAsync(userId);
    }

    public async Task<List<string>> GetUserRoleCodesAsync()
    {
        return await GetUserRoleCodesAsync(_currentUserContext.GetCurrentUserId()!.Value);
    }

    public async Task<List<long>> GetRoleUsersAsync(string roleCode, params RoleType[] roleTypes)
    {
        return await Repository.GetRoleUserIdsAsync(roleCode, roleTypes);
    }
}

using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// UserPasswordHistory 服务实现
/// </summary>
public sealed class UserPasswordHistoryService :
    BaseEditableAppService<long, UserPasswordHistoryEntity, UserPasswordHistoryDto, UserPasswordHistoryOperationDto, IUserPasswordHistoryRepository, UserPasswordHistoryQueryCriteria>,
    IUserPasswordHistoryService
{
    public UserPasswordHistoryService(IUserPasswordHistoryRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 
using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// UserOrganization 服务实现
/// </summary>
public sealed class UserOrganizationService :
    BaseEditableAppService<long, UserOrganizationEntity, UserOrganizationDto, UserOrganizationOperationDto,
        IUserOrganizationRepository, UserOrganizationQueryCriteria>,
    IUserOrganizationService
{
    private readonly IOrganizationRepository _organizationRepository;

    public UserOrganizationService(IUserOrganizationRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext,
        IOrganizationRepository organizationRepository) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _organizationRepository = organizationRepository;
    }

    public async Task<bool> SetUserOrganizations(long userId, List<UserOrganizationOperationDto> userOrganizations)
    {
        var originals = await Repository.GetListAsync(q => q.UserId == userId);

        // 使用userid 和organizationid 比较原始数据和新数据 需要比较出新增数据、删除数据、修改数据（设置IsPrimary）
        var originalsDict = originals.ToDictionary(x => (x.OrganizationId));
        var newDict = userOrganizations.ToDictionary(x => (x.OrganizationId));
        var toAdd = new List<UserOrganizationEntity>();
        var toDelete = new List<UserOrganizationEntity>();
        var toUpdate = new List<UserOrganizationEntity>();
        foreach (var original in originals)
        {
            if (newDict.TryGetValue((original.OrganizationId), out var newItem))
            {
                original.UserId = userId;
                // 更新
                original.IsPrimary = newItem.IsPrimary;
                toUpdate.Add(original);
            }
            else
            {
                // 删除
                toDelete.Add(original);
            }
        }

        foreach (var newItem in userOrganizations)
        {
            if (!originalsDict.ContainsKey((newItem.OrganizationId)))
            {
                // 新增
                var entity = Mapper.Map<UserOrganizationEntity>(newItem);
                entity.Key = KeyGenerator.GenerateKey();
                entity.UserId = userId;
                toAdd.Add(entity);
            }
        }

        // 批量删除
        if (toDelete.Any())
        {
            await Repository.DeleteAsync(toDelete);
        }

        // 批量新增
        if (toAdd.Any())
        {
            await Repository.InsertAsync(toAdd);
        }

        // 批量更新
        if (toUpdate.Any())
        {
            await Repository.UpdateAsync(toUpdate);
        }

        return true;
    }

    public async Task<List<string>> GetUserOrganizationCodesAsync(long userId)
    {
        var userOrganizations = await Repository.GetListAsync(q => q.UserId == userId);

        var organizationIds = userOrganizations.Select(x => x.OrganizationId).ToList();

        if (!organizationIds.Any())
        {
            return new List<string>();
        }

        return (await _organizationRepository.GetListAsync(q =>
            organizationIds.Contains(q.Key)
        )).Select(q => q.Code).ToList();
    }
}

using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// Organization 服务实现
/// </summary>
public sealed class OrganizationService :
    BaseEditableAppService<long, OrganizationEntity, OrganizationDto, OrganizationOperationDto, IOrganizationRepository,
        OrganizationQueryCriteria>,
    IOrganizationService
{
    public OrganizationService(IOrganizationRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }

    public async new Task<bool> CreateAsync(OrganizationOperationDto dto)
    {
        OrganizationDto? parent = null;
        if (dto.ParentId != null)
        {
            parent = await GetByIdAsync(dto.ParentId.Value);
        }

        var entity = (await GetEntityAsync(dto))!;
        entity.Key = KeyGenerator.GenerateKey();
        entity.Path = parent?.Path + entity.Code + "/";
        entity.NamePath = parent?.NamePath + "/" + entity.Name;

        return await Repository.InsertAsync(entity);
    }

    public async new Task<bool> UpdateAsync(long id, OrganizationOperationDto operationDto)
    {
        var entity = await Repository.GetAsync(q => q.Key.Equals(id));

        entity.NullCheck();

        // 将DTO的值映射到现有实体上
        Mapper.Map(operationDto, entity);

        // 确保Key不变
        entity!.Key = id;


        OrganizationDto? parent = null;
        if (operationDto.ParentId != null)
        {
            parent = await GetByIdAsync(operationDto.ParentId.Value);
        }

        entity.Path = parent?.Path + entity.Code + "/";
        entity.NamePath = parent?.NamePath + "/" + entity.Name;

        return await Repository.UpdateAsync(entity);
    }

    public async Task<List<OrganizationDto>> GetOrganizationChildrenAsync(string organizationCode,
        bool includeSelf = false)
    {
        var result = await Repository.GetOrganizationChildrenAsync(organizationCode, includeSelf);

        return Mapper.Map<List<OrganizationDto>>(result);
    }
}

// 系统库

using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using System.Linq;
using System.Threading.Tasks;

// Microsoft 依赖
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using System.ComponentModel.DataAnnotations;
using System.Security.Authentication;
using XJ.Framework.Library.Application.Contract;
using XJ.Framework.Library.Cache.Abstraction;

// 基础设施层
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain;

// RBAC 应用层

// RBAC 领域层
using XJ.Framework.Rbac.Domain.Entities;
using XJ.Framework.Rbac.Domain.Repositories.Interfaces;

// RBAC 共享层
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using XJ.Framework.Library.Application.Contract.Interfaces;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// Token服务实现
/// </summary>
public class TokenService : ITokenService
{
    private readonly ILogger<TokenService> _logger;
    private readonly IConfiguration _configuration;
    private readonly ITokenRepository _tokenRepository;
    private readonly IUserRepository _userRepository;
    private readonly IUserRoleRepository _userRoleRepository;
    private readonly IPermissionRepository _permissionRepository;
    private readonly IUnitOfWork _unitOfWork;
    private readonly ICache _cache;
    private readonly IAuthInfoGetter _authInfoGetter;

    /// <summary>
    /// 构造函数
    /// </summary>
    public TokenService(
        ILogger<TokenService> logger,
        IConfiguration configuration,
        ITokenRepository tokenRepository,
        IUserRepository userRepository,
        IUserRoleRepository userRoleRepository,
        IPermissionRepository permissionRepository,
        IUnitOfWork unitOfWork,
        ICache cache,
        IAuthInfoGetter authInfoGetter)
    {
        _logger = logger;
        _configuration = configuration;
        _tokenRepository = tokenRepository;
        _userRepository = userRepository;
        _userRoleRepository = userRoleRepository;
        _permissionRepository = permissionRepository;
        _unitOfWork = unitOfWork;
        _cache = cache;
        _authInfoGetter = authInfoGetter;
    }

    /// <summary>
    /// 生成访问令牌
    /// </summary>
    public async Task<string> GenerateAccessTokenAsync(long userId, string username
        // , IEnumerable<string> roles,
        // IEnumerable<string> permissions
        , string clientIp, string deviceId,
        string deviceInfo
    )
    {
        var claims = new List<Claim>
        {
            new(JwtRegisteredClaimNames.Sub, userId.ToString()),
            new(JwtRegisteredClaimNames.UniqueName, username),
            new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString())
        };

        // // 添加角色声明
        // foreach (var role in roles)
        // {
        //     claims.Add(new Claim(ClaimTypes.Role, role));
        // }
        //
        // // 添加权限声明
        // foreach (var permission in permissions)
        // {
        //     claims.Add(new Claim("permission", permission));
        // }

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]!));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var utcNow = DateTime.UtcNow;
        var expires = utcNow.AddMinutes(Convert.ToDouble(_configuration["Jwt:AccessTokenExpirationMinutes"]));

        var token = new JwtSecurityToken(
            issuer: _configuration["Jwt:Issuer"],
            audience: _configuration["Jwt:Audience"],
            claims: claims,
            notBefore: utcNow,
            expires: expires,
            signingCredentials: credentials
        );

        var tokenString = new JwtSecurityTokenHandler().WriteToken(token);


        var jti = claims.FirstOrDefault(c => c.Type == JwtRegisteredClaimNames.Jti)?.Value;
        if (!string.IsNullOrEmpty(jti))
        {
            var accessTokenEntity = new TokenEntity
            {
                Key = IdGenerator.NextId(),
                UserId = userId,
                Token = jti, // 存储jti而不是完整token
                IpAddress = clientIp,
                DeviceId = deviceId,
                UserAgent = deviceInfo,
                TokenType = TokenType.AccessToken,
                ExpireTime = expires,
                CreateTime = DateTimeOffset.UtcNow,
            };

            await _tokenRepository.InsertAsync(accessTokenEntity);
        }

        return await Task.FromResult(tokenString);
    }

    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    public async Task<string> GenerateRefreshTokenAsync(long userId, string clientIp, string deviceId,
        string deviceInfo)
    {
        var token = Guid.NewGuid().ToString("N");
        var expireTime =
            DateTimeOffset.UtcNow.AddDays(Convert.ToDouble(_configuration["Jwt:RefreshTokenExpirationDays"]));

        // 注意：在 RefreshTokenAsync 方法中，我们已经删除了旧的刷新令牌，
        // 所以这里不需要再删除设备令牌，避免删除刚刚创建的令牌

        try
        {
            // 检查用户的活跃令牌数量
            var tokenCount = await _tokenRepository.GetUserActiveTokenCountAsync(userId, TokenType.Refresh);
            var maxTokens = Convert.ToInt32(_configuration["Jwt:MaxRefreshTokensPerUser"]);

            if (tokenCount >= maxTokens)
            {
                await _tokenRepository.DeleteOldestTokenAsync(userId, TokenType.Refresh);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查令牌数量时发生错误: {Message}", ex.Message);
            // 继续执行，不要因为检查失败而中断整个流程
        }

        var refreshToken = new TokenEntity
        {
            Key = IdGenerator.NextId(),
            UserId = userId,
            Token = token,
            TokenType = TokenType.Refresh,
            ExpireTime = expireTime,
            CreateTime = DateTimeOffset.UtcNow,
            IpAddress = clientIp,
            DeviceId = deviceId,
            UserAgent = deviceInfo
        };

        await _tokenRepository.InsertAsync(refreshToken);
        return token;
    }

    /// <summary>
    /// 验证访问令牌
    /// </summary>
    public async Task<TokenValidationResultDto> ValidateAccessTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(_configuration["Jwt:Key"]!);

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidIssuer = _configuration["Jwt:Issuer"],
                ValidateAudience = true,
                ValidAudience = _configuration["Jwt:Audience"],
                ValidateLifetime = true,
                // 修改：添加时间偏移容差
                ClockSkew = TimeSpan.FromMinutes(Convert.ToInt32(_configuration["Jwt:ClockSkewMinutes"] ?? "5")),
            };

            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);

            // 使用IAuthInfoGetter获取tokenId
            var authInfo = await _authInfoGetter.GetAuthInfoAsync();
            var jti = authInfo.tokenId!;

            var accessToken = await _tokenRepository.FindByTokenAsync(jti, TokenType.AccessToken);
            if (accessToken == null)
            {
                return new TokenValidationResultDto { IsValid = false };
            }

            if (accessToken.ExpireTime <= DateTimeOffset.UtcNow)
            {
                await _tokenRepository.DeleteAsync(accessToken);
                return new TokenValidationResultDto { IsValid = false };
            }

            await _tokenRepository.UpdateLastUseTimeAsync(token, TokenType.Refresh);

            return await Task.FromResult(new TokenValidationResultDto
            {
                IsValid = true,
                UserId = long.Parse(principal.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "0"),
                Username = principal.FindFirst(ClaimTypes.Name)?.Value
            });
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "访问令牌验证失败");
            return new TokenValidationResultDto { IsValid = false, ErrorMessage = ex.Message };
        }
    }

    /// <summary>
    /// 验证刷新令牌
    /// </summary>
    public async Task<TokenValidationResultDto> ValidateRefreshTokenAsync(string token, string deviceId)
    {
        var refreshToken = await _tokenRepository.FindByTokenAsync(token, TokenType.Refresh);
        if (refreshToken == null)
        {
            return new TokenValidationResultDto { IsValid = false };
        }

        if (refreshToken.ExpireTime <= DateTimeOffset.UtcNow)
        {
            await _tokenRepository.DeleteAsync(refreshToken);
            return new TokenValidationResultDto { IsValid = false };
        }

        await _tokenRepository.UpdateLastUseTimeAsync(token, TokenType.Refresh);

        return new TokenValidationResultDto
        {
            IsValid = true,
            UserId = refreshToken.UserId,
            ClientIp = refreshToken.IpAddress,
            DeviceInfo = refreshToken.UserAgent,
            DeviceId = refreshToken.DeviceId
        };
    }

    /// <summary>
    /// 撤销访问令牌
    /// </summary>
    public async Task<bool> RevokeAccessTokenAsync(string token)
    {
        var authInfo = await _authInfoGetter.GetAuthInfoAsync();
        var jti = authInfo.tokenId!;

        var accessToken = await _tokenRepository.FindByTokenAsync(jti, TokenType.AccessToken);
        if (accessToken == null)
        {
            return false;
        }

        await _tokenRepository.DeleteAsync(accessToken);
        return true;
    }

    /// <summary>
    /// 撤销刷新令牌
    /// </summary>
    public async Task<bool> RevokeRefreshTokenAsync(string token)
    {
        var refreshToken = await _tokenRepository.FindByTokenAsync(token, TokenType.Refresh);
        if (refreshToken == null)
        {
            return false;
        }

        await _tokenRepository.DeleteAsync(refreshToken);
        return true;
    }

    /// <summary>
    /// 获取用户活跃令牌列表
    /// </summary>
    public async Task<List<ActiveTokenDto>> GetUserActiveTokensAsync(long userId)
    {
        var tokens = await _tokenRepository.GetUserActiveTokensAsync(userId);
        return tokens.Select(token => new ActiveTokenDto
        {
            Id = token.Key.ToString(),
            TokenType = token.TokenType.ToString(),
            UserId = token.UserId,
            CreatedAt = token.CreateTime,
            LastUsedAt = token.LastUseTime,
            ExpiresAt = token.ExpireTime,
            DeviceInfo = token.DeviceId,
            ClientIp = token.IpAddress
        }).ToList();
    }

    /// <summary>
    /// 撤销用户所有令牌
    /// </summary>
    public async Task<bool> RevokeAllUserTokensAsync(long userId)
    {
        var tokens = await _tokenRepository.GetUserActiveTokensAsync(userId);

        tokens.ForEach(tokenEntity =>
        {
            var key = string.Format(CacheKeys.RemoveUserAllKey, tokenEntity.Token, tokenEntity.DeviceId);
            _cache.RemoveByPatternAsync(key);
        });

        return await _tokenRepository.DeleteUserTokensAsync(userId);
    }

    /// <summary>
    /// 清理过期令牌
    /// </summary>
    public async Task<bool> CleanExpiredTokensAsync()
    {
        var count = await _tokenRepository.CleanExpiredTokensAsync();
        _logger.LogInformation("已清理 {Count} 个过期令牌", count);
        return true;
    }

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    public async Task<TokenResponseDto> RefreshTokenAsync(string refreshToken, string deviceId)
    {
        await _unitOfWork.BeginTransactionAsync();
        try
        {
            // 1. 验证刷新令牌
            var validationResult = await ValidateRefreshTokenAsync(refreshToken, deviceId);
            if (!validationResult.IsValid || !validationResult.UserId.HasValue)
            {
                await _unitOfWork.CommitAsync();
                throw new AuthenticationException(
                    "刷新令牌无效或已过期/ Refresh token is invalid or expired");
            }

            if (deviceId != validationResult.DeviceId)
            {
                // 先删除旧的刷新令牌
                await _tokenRepository.DeleteTokenByValueAsync(refreshToken, TokenType.Refresh, true);
                await _unitOfWork.CommitAsync();
                throw new AuthenticationException("设备ID不匹配/ Device ID mismatch");
            }

            // 2. 获取用户信息和权限
            var user = await _userRepository.GetAsync(validationResult.UserId.Value);
            if (user == null)
            {
                throw new Library.Common.Abstraction.Extensions.ValidationException("用户不存在/ User does not exist");
            }

            // var roles = await _userRoleRepository.GetUserRoleCodesAsync(user.Key);
            // var permissions = await _permissionRepository.GetUserPermissionCodesAsync(user.Key);


            // 3. 生成新的访问令牌
            var newAccessToken = await GenerateAccessTokenAsync(user.Key, user.Username
                , validationResult.ClientIp ?? "",
                validationResult.DeviceId ?? "",
                validationResult.DeviceInfo ?? ""
                // , roles, permissions
            );

            // 5. 使用直接SQL删除旧的刷新令牌，避免实体跟踪冲突
            // 注意：不再调用RevokeRefreshTokenAsync方法，而是直接使用SQL删除
            if (!string.IsNullOrEmpty(validationResult.DeviceId))
            {
                // 先删除旧的刷新令牌
                await _tokenRepository.DeleteTokenByValueAsync(refreshToken, TokenType.Refresh);
            }

            // 4. 生成新的刷新令牌
            var newRefreshToken = await GenerateRefreshTokenAsync(user.Key,
                validationResult.ClientIp ?? "",
                validationResult.DeviceId ?? "",
                validationResult.DeviceInfo ?? ""
            );

            await _unitOfWork.CommitAsync();
            return new TokenResponseDto
            {
                AccessToken = newAccessToken,
                RefreshToken = newRefreshToken,
                TokenType = "Bearer",
                ExpiresIn = Convert.ToInt32(_configuration["Jwt:AccessTokenExpirationMinutes"]) * 60
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "刷新令牌时发生错误: {Message}", ex.Message);
            throw;
        }
    }

    /// <summary>
    /// 生成邮箱验证令牌
    /// </summary>
    public async Task<string> GenerateEmailTokenAsync(long userId, int minutes)
    {
        // var token = Guid.NewGuid().ToString("N");
        var random = new Random();
        var token = random.Next(100000, 1000000).ToString();
        var expireTime = DateTimeOffset.UtcNow.AddMinutes(minutes); // 24小时有效期

        var emailToken = new TokenEntity
        {
            Key = IdGenerator.NextId(),
            UserId = userId,
            Token = token,
            TokenType = TokenType.EmailVerification,
            ExpireTime = expireTime,
            CreateTime = DateTimeOffset.UtcNow
        };

        await _tokenRepository.InsertAsync(emailToken);
        return token;
    }

    /// <summary>
    /// 验证邮箱验证令牌
    /// </summary>
    public async Task<bool> ValidateEmailTokenAsync(long userId, string token)
    {
        var emailToken = await _tokenRepository.FindByTokenAsync(token, TokenType.EmailVerification);
        if (emailToken == null || emailToken.UserId != userId)
        {
            return false;
        }

        if (emailToken.ExpireTime <= DateTimeOffset.UtcNow)
        {
            await _tokenRepository.DeleteAsync(emailToken);
            return false;
        }

        await _tokenRepository.DeleteAsync(emailToken);
        return true;
    }

    /// <summary>
    /// 生成手机验证令牌
    /// </summary>
    public async Task<string> GenerateMobileTokenAsync(long userId, int minutes)
    {
        // 生成6位数字验证码
        var random = new Random();
        var code = random.Next(100000, 1000000).ToString();
        var expireTime = DateTimeOffset.UtcNow.AddMinutes(minutes); // 10分钟有效期

        var mobileToken = new TokenEntity
        {
            Key = IdGenerator.NextId(),
            UserId = userId,
            Token = code,
            TokenType = TokenType.MobileVerification,
            ExpireTime = expireTime,
            CreateTime = DateTimeOffset.UtcNow
        };

        await _tokenRepository.InsertAsync(mobileToken);
        return code;
    }

    /// <summary>
    /// 验证手机验证令牌
    /// </summary>
    public async Task<bool> ValidateMobileTokenAsync(long userId, string token)
    {
        var mobileToken = await _tokenRepository.FindByTokenAsync(token, TokenType.MobileVerification);
        if (mobileToken == null || mobileToken.UserId != userId)
        {
            return false;
        }

        if (mobileToken.ExpireTime <= DateTimeOffset.UtcNow)
        {
            await _tokenRepository.DeleteAsync(mobileToken);
            return false;
        }

        await _tokenRepository.DeleteAsync(mobileToken);
        return true;
    }


    /// <summary>
    /// 根据访问令牌获取令牌信息
    /// </summary>
    public async Task<TokenDto?> GetTokenByAccessTokenAsync(string accessToken)
    {
        try
        {
            // 验证访问令牌
            var validationResult = await ValidateAccessTokenAsync(accessToken.Split(" ").Last());
            if (!validationResult.IsValid || !validationResult.UserId.HasValue)
            {
                return null;
            }

            // 获取用户的刷新令牌
            var refreshTokens =
                await _tokenRepository.GetUserActiveTokensAsync(validationResult.UserId.Value, TokenType.Refresh);
            if (refreshTokens == null || refreshTokens.Count == 0)
            {
                return null;
            }

            // 返回最新的刷新令牌
            var latestToken = refreshTokens.OrderByDescending(t => t.LastUseTime ?? t.CreateTime).FirstOrDefault();
            if (latestToken == null)
            {
                return null;
            }

            return new TokenDto
            {
                Key = latestToken.Key,
                UserId = latestToken.UserId,
                AccessToken = accessToken,
                RefreshToken = latestToken.Token,
                DeviceId = latestToken.DeviceId,
                DeviceInfo = latestToken.UserAgent,
                ClientIp = latestToken.IpAddress,
                CreatedTime = latestToken.CreateTime,
                ExpiredTime = latestToken.ExpireTime,
                IsRevoked = false, // TokenEntity 可能没有 IsRevoked 属性，这里设置为默认值
                RevokedTime = null // TokenEntity 可能没有 RevokeTime 属性，这里设置为默认值
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取令牌信息时发生错误: {Message}", ex.Message);
            return null;
        }
    }
}

using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Linq.Expressions;
using System.Security.Authentication;
using XJ.Framework.Library.Application.Contract;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Application.Contract;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Rbac.Application.Services;

public class RbacAuthProvider : IAuthProvider
{
    private readonly ILogger<RbacAuthProvider> _logger;
    private readonly IPermissionRepository _permissionRepository;
    private readonly IUserService _userService;
    private readonly ITokenService _tokenService;
    private readonly IUserRoleService _userRoleService;
    private readonly IPermissionService _permissionService;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly IUserExtService _userExtService;
    private readonly IAuthInfoGetter _authInfoGetter;
    private readonly IMapper _mapper;
    private readonly ICache _cache;
    private readonly IUnitOfWork _unitOfWork;


    public RbacAuthProvider(ILogger<RbacAuthProvider> logger, IPermissionRepository permissionRepository,
        IUserService userService, ITokenService tokenService, IUserRoleService userRoleService,
        ICurrentUserContext currentUserContext,
        IPermissionService permissionService, IAuthInfoGetter authInfoGetter, IMapper mapper,
        IUserExtService userExtService, ICache cache, IUnitOfWork unitOfWork)
    {
        _logger = logger;
        _permissionRepository = permissionRepository;
        _userService = userService;
        _tokenService = tokenService;
        _userRoleService = userRoleService;
        _currentUserContext = currentUserContext;
        _permissionService = permissionService;
        _authInfoGetter = authInfoGetter;
        _mapper = mapper;
        _userExtService = userExtService;
        _cache = cache;
        _unitOfWork = unitOfWork;
    }

    public async Task<bool> ValidateApiPermissionAsync(
        string permissionCode,
        HttpMethod? httpMethod = null,
        string? path = null,
        string? appCode = null)
    {
        return await ValidatePermissionAsync(PermissionType.Api, permissionCode, httpMethod, path, appCode);
    }

    public async Task<bool> ValidateButtonPermissionAsync(
        string permissionCode,
        HttpMethod? httpMethod = null,
        string? path = null,
        string? appCode = null)
    {
        return await ValidatePermissionAsync(PermissionType.Button, permissionCode, httpMethod, path, appCode);
    }

    private async Task<bool> ValidatePermissionAsync(PermissionType permissionType,
        string permissionCode,
        HttpMethod? httpMethod = null,
        string? path = null,
        string? appCode = null)
    {
        return await _permissionService.ValidatePermissionAsync(_currentUserContext.GetCurrentUserId()!.Value,
            permissionType,
            permissionCode, httpMethod, path, appCode);
    }

    public async Task<UserProfileDto?> GetUserProfileAsync()
    {
        var (schema, accessToken, deviceId, deviceInfo, tokenId, exp, sub, uniqueName) =
            await _authInfoGetter.GetAuthInfoAsync();

        if (string.IsNullOrEmpty(accessToken))
            throw new AuthenticationException("访问凭证不能为空/Access token cannot be empty");
        if (string.IsNullOrEmpty(deviceId))
            throw new AuthenticationException("设备ID不能为空/Device ID cannot be empty");

        if (string.IsNullOrEmpty(deviceId) || string.IsNullOrEmpty(tokenId))
        {
            throw new AuthenticationException("jti or deviceId is null");
        }

        var key = string.Format(CacheKeys.UserProfileKey, tokenId, deviceId);

        var validationResult = await _tokenService.ValidateAccessTokenAsync(accessToken);
        if (!validationResult.IsValid)
        {
            await _cache.RemoveAsync(key);
            throw new AuthenticationException(
                $"访问凭证无效:{validationResult.ErrorMessage}/Access token is invalid: {validationResult.ErrorMessage}");
        }


        if (!await ValidateUserDeviceIdAsync(deviceId, accessToken))
        {
            await _cache.RemoveAsync(key);
            await _tokenService.RevokeAllUserTokensAsync(validationResult.UserId!.Value);

            await _unitOfWork.CommitAsync();
            throw new AuthenticationException("设备ID不匹配，可能存在令牌盗用/ Device ID mismatch, token may have been stolen.");
        }

        var dto = await _userService.GetByIdAsync(validationResult.UserId!.Value);
        var userExtDto = await _userExtService.GetUserExtAsync(validationResult.UserId.Value);
        var userProfileDto = _mapper.Map<UserProfileDto>(dto);
        userProfileDto.Gender = userExtDto.Gender;
        userProfileDto.Country = userExtDto.Country;
        userProfileDto.Unit = userExtDto.Unit;
        userProfileDto.ContactAddress = userExtDto.ContactAddress;
        userProfileDto.Telephone = userExtDto.Telephone;
        return userProfileDto;
    }

    public async Task<bool> ValidateUserDeviceIdAsync(string deviceId, string accessToken)
    {
        if (!string.IsNullOrEmpty(deviceId))
        {
            // 获取用户的设备ID（这里需要根据实际情况修改，可能需要从数据库或缓存中获取）
            var userDeviceId = await GetUserDeviceIdAsync(accessToken);

            // 如果设备ID不匹配，则拒绝访问
            if (!string.IsNullOrEmpty(userDeviceId) && userDeviceId != deviceId)
            {
                _logger.LogWarning(
                    "设备ID不匹配，可能存在令牌盗用。 预期设备ID: {ExpectedDeviceId}, 实际设备ID: {ActualDeviceId}", userDeviceId, deviceId);
                return false;
            }
        }

        return true;
    }

    private async Task<string?> GetUserDeviceIdAsync(string accessToken)
    {
        try
        {
            // 从令牌中获取设备ID
            var token = await _tokenService.GetTokenByAccessTokenAsync(accessToken);
            if (token == null)
            {
                return null;
            }

            return token.DeviceId;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取用户设备ID时发生错误: {Message}", ex.Message);
            return null;
        }
    }
}

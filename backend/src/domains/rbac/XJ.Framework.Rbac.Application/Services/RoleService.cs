using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// Role 服务实现
/// </summary>
public sealed class RoleService :
    BaseEditableAppService<long, RoleEntity, RoleDto, RoleOperationDto, IRoleRepository, RoleQueryCriteria>,
    IRoleService
{
    public RoleService(IRoleRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }

    public async new Task<bool> DeleteAsync(long id)
    {
        var entity = await Repository.GetAsync(id);
        if (entity!.Type == RoleType.System)
        {
            throw new ValidationException("系统角色不允许删除");
        }

        return await base.DeleteAsync(id);
    }

    public async Task<RoleDto?> GetByCodeAsync(string defaultRoleCode)
    {
        var entity = await Repository.GetAsync(q => q.Code.ToUpper().Equals(defaultRoleCode.ToUpper()));
        return await GetDtoAsync(entity);
    }

    public async new Task<bool> UpdateAsync(long id, RoleOperationDto dto)
    {
        var entity = await Repository.GetAsync(id);
        if (entity!.Type == RoleType.System)
        {
            if (dto.Status == CommonStatus.Disabled)
                throw new ValidationException("系统角色不允许禁用");

            if (!dto.Code.Equals(entity.Code))
                throw new ValidationException("系统角色不允许修改编码");

            if (!dto.Type.Equals(entity.Type))
                throw new ValidationException("系统角色不允许修改角色类型");
        }

        return await base.UpdateAsync(id, dto);
    }

    public async new Task<bool> CreateAsync(RoleOperationDto dto)
    {
        var finder = await Repository.AnyAsync(q => q.Code.ToUpper().Equals(dto.Code.ToUpper()));
        if (finder)
        {
            throw new ValidationException("角色编码已存在");
        }

        return await base.CreateAsync(dto);
    }
}
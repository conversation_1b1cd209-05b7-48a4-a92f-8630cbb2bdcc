
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// DataPermissionCache 控制器
/// </summary>

public class DataPermissionCacheController : BaseEditableAppController<long, DataPermissionCacheDto, DataPermissionCacheOperationDto, IDataPermissionCacheService, DataPermissionCacheQueryCriteria>
{
    public DataPermissionCacheController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    /*
    
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(DataPermissionCacheOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<DataPermissionCacheDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, DataPermissionCacheOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, DataPermissionCacheDto>> GetPageAsync([FromQuery] PagedQueryCriteria<DataPermissionCacheQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<DataPermissionCacheDto>> GetListAsync([FromQuery] DataPermissionCacheQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
    */
}

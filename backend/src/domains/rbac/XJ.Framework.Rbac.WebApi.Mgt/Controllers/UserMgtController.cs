using Microsoft.AspNetCore.Authorization;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.WebApi.Attributes;
using XJ.Framework.Library.WebApi.Middlewares;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// 用户相关
/// </summary>
public class
    UserController : BaseEditableAppController<long, UserDto, UserOperationDto, IUserService, UserQueryCriteria>
{
    private readonly IAuthService _authService;
    private readonly IAuthInfoGetter _authInfoGetter;
    private readonly ICurrentUserContext _currentUserContext;

    public UserController(IServiceProvider serviceProvider, IAuthService authService, IAuthInfoGetter authInfoGetter,
        ICurrentUserContext currentUserContext) :
        base(serviceProvider)
    {
        _authService = authService;
        _authInfoGetter = authInfoGetter;
        _currentUserContext = currentUserContext;
    }

    [ApplicationPermission]
    [HttpPost("login")]
    public async Task<LoginResultDto> LoginAsync([FromBody] MockLoginRequestDto request)
    {
        return await _authService.CreateUserLoginResultAsync(
            request.AppCode,
            request.ClientIp!,
            request.DeviceId!,
            request.DeviceInfo!,
            request.Username
        );
    }

    /// <summary>
    /// 插入用户
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(UserOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [UnitOfWork]
    [HttpPost("create")]
    public async Task<long> CreateUserAsync(UserCreateOperationDto dto)
    {
        return await Service.CreateUserAsync(dto);
    }

    [UnitOfWork]
    [HttpPut("{id}/reset-password")]
    public async Task<string> ResetPasswordAsync(long id)
    {
        return await Service.ResetPasswordAsync(id);
    }
    
    [UnitOfWork]
    [HttpPost("batch-create")]
    public async Task<Dictionary<string, long>> CreateUserAsync(List<UserCreateOperationDto> dtos)
    {
        var accountKeys = new Dictionary<string, long>();
        await dtos.ForEachAsync(async dto =>
        {
            try
            {
                var key = await Service.CreateUserAsync(dto);
                accountKeys.Add(dto.Username, key);
            }
            catch (Exception)
            {
                accountKeys.Add(dto.Username, -1);
            }
        });
        return accountKeys;
    }

    /// <summary>
    /// 根据ID获取用户
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:long}")]
    [PublicPermission(true)]
    public async Task<UserDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据ID更新用户
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, UserOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    /// <summary>
    /// 根据ID禁用用户
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}/disable")]
    public async Task<bool> DisableAsync(long id)
    {
        return await _authService.UpdateUserStatusAsync(id, UserStatus.Disabled);
    }

    /// <summary>
    /// 根据ID启用用户
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}/enable")]
    public async Task<bool> EnableAsync(long id)
    {
        return await _authService.UpdateUserStatusAsync(id, UserStatus.Enabled);
    }

    /// <summary>
    /// 分页查询用户列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<PageDtoData<long, UserDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<UserQueryCriteria> criteria)
    {
        var users = await Service.GetPageAsync(criteria);
        return users;
    }

    /// <summary>
    /// 获取指定通用角色下的所有用户
    /// </summary>
    /// <param name="roleCode"></param>
    /// <returns></returns>
    [HttpGet("role-in-common/{roleCode}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<List<UserDto>> GetUsersByRoleCodeAsync(string roleCode)
    {
        var users = await Service.GetUsersByRoleCodeAsync(roleCode, RoleType.General);
        return users;
    }
    /// <summary>
    /// 获取指定通用角色下的所有用户
    /// </summary>
    /// <param name="roleCode"></param>
    /// <returns></returns>
    [HttpGet("app/role-in-common/{roleCode}")]
    [ApplicationPermission]
    [IgnoreLogging]
    public async Task<List<UserDto>> GetAppUsersByRoleCodeAsync(string roleCode)
    {
        var users = await Service.GetUsersByRoleCodeAsync(roleCode, RoleType.General);
        return users;
    }

    /// <summary>
    /// 获取指定组织下指定岗位的所有用户
    /// </summary>
    /// <param name="positionCode"></param>
    /// <param name="organizationCode"></param>
    /// <returns></returns>
    [HttpGet("position-in-organization/{positionCode}/{organizationCode}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<List<UserDto>> GetUsersByPositionCodeAsync(string positionCode, string organizationCode)
    {
        var users = await Service.GetUsersByPositionCodeAsync(positionCode, organizationCode);
        return users;
    }

    /// <summary>
    /// 获取用户管理的指定岗位的所有用户
    /// </summary>
    /// <param name="positionCode"></param>
    /// <param name="userId"></param>
    /// <returns></returns>
    [HttpGet("position-in-user-managed/{positionCode}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<List<OrganizationUserDto>> GetManagedPositionUsersAsync(string positionCode)
    {
        var users = await Service.GetManagedPositionUsersAsync(positionCode,
            _currentUserContext.GetCurrentUserId()!.Value);
        return users;
    }


    /// <summary>
    /// 获取指定组织下指定岗位的所有用户
    /// </summary>
    /// <param name="positionCode"></param>
    /// <param name="organizationCode"></param>
    /// <returns></returns>
    [HttpGet("position/{positionCode}")]
    [PublicPermission(true)]
    [IgnoreLogging]
    public async Task<List<UserDto>> GetUsersByPositionCodeAsync(string positionCode)
    {
        var users = await Service.GetUsersByPositionCodeAsync(positionCode);
        return users;
    }

    /// <summary>
    /// 查询用户列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<UserDto>> GetListAsync([FromQuery] UserQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}

using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// UserPasswordHistory 控制器
/// </summary>
public class UserPasswordHistoryController : BaseEditableAppController<long, UserPasswordHistoryDto,
    UserPasswordHistoryOperationDto, IUserPasswordHistoryService, UserPasswordHistoryQueryCriteria>
{
    public UserPasswordHistoryController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    /*
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(UserPasswordHistoryOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<UserPasswordHistoryDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, UserPasswordHistoryOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, UserPasswordHistoryDto>> GetPageAsync([FromQuery] PagedQueryCriteria<UserPasswordHistoryQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<UserPasswordHistoryDto>> GetListAsync([FromQuery] UserPasswordHistoryQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
    */
}
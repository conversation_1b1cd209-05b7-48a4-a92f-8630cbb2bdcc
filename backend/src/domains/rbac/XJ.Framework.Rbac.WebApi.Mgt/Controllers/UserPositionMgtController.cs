
using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// 用户岗位相关
/// </summary>

public class UserPositionController : BaseEditableAppController<long, UserPositionDto, UserPositionOperationDto, IUserPositionService, UserPositionQueryCriteria>
{
    public UserPositionController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    
    /// <summary>
    /// 更新用户岗位
    /// </summary>
    /// <param name="userId">用户id</param>
    /// <param name="userPositions"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{userId:long}")]
    public async Task<bool> UpdateAsync(long userId, [FromBody] List<UserPositionOperationDto> userPositions)
    {
        return await Service.SetUserPositions(userId, userPositions);
    }


    /// <summary>
    /// 查询用户岗位
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission(true)]
    public async Task<IEnumerable<UserPositionDto>> GetListAsync([FromQuery] UserPositionQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}

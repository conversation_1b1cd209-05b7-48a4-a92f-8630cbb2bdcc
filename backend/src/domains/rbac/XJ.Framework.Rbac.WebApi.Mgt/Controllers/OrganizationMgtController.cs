using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// 组织相关
/// </summary>
// [PublicPermission]
public class OrganizationController : BaseEditableAppController<long, OrganizationDto, OrganizationOperationDto,
    IOrganizationService, OrganizationQueryCriteria>
{
    public OrganizationController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    /// <summary>
    /// 新增组织
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    // [PublicPermission]
    public async Task<bool> InsertAsync(OrganizationOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    /// <summary>
    /// 根据ID获取组织
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:long}")]
    public async Task<OrganizationDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据ID更新组织
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, OrganizationOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    /// <summary>
    /// 根据ID删除组织
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    /// <summary>
    /// 分页查询组织列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<PageDtoData<long, OrganizationDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<OrganizationQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    /// <summary>
    /// 查询组织列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<OrganizationDto>> GetListAsync([FromQuery] OrganizationQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// 角色权限相关
/// </summary>
public class RolePermissionController : BaseEditableAppController<long, RolePermissionDto, RolePermissionOperationDto,
    IRolePermissionService, RolePermissionQueryCriteria>
{
    public RolePermissionController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 设置角色权限
    /// </summary>
    /// <param name="roleId"></param>
    /// <param name="rolePermissions"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{roleId:long}")]
    public async Task<bool> SetPermissionsAsync(long roleId, List<RolePermissionOperationDto> rolePermissions)
    {
        return await Service.SetPermissions(roleId, rolePermissions);
    }


    /// <summary>
    /// 查询角色权限
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<RolePermissionDto>> GetListAsync([FromQuery] RolePermissionQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
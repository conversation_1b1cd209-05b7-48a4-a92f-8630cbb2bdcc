using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Application.Services;
using XJ.Framework.Library.WebApi.Services;
using XJ.Framework.Messaging.ApiClient;
using XJ.Framework.Rbac.ApiClient;
using XJ.Framework.Rbac.Application.Services;

namespace XJ.Framework.Rbac.WebApi;

public class RbacWebApiMgtWrapper : WebApiMgtWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<RbacApplicationWrapper, RbacInfrastructureWrapper>(
                configuration);
        services.AddHttpClient<UserApiClient>();
        
        services.AddHttpClient<MessagingApplicationApiClient>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}


// global using 指令

// System 命名空间

global using System.Collections.Generic;
global using System.ComponentModel.DataAnnotations;
global using System.ComponentModel.DataAnnotations.Schema;
global using System.Linq.Expressions;
global using System.Security.Cryptography;
global using System.Text;
global using System.Threading.Tasks;
global using XJ.Framework.Library.Domain.Attributes;
global using XJ.Framework.Library.Domain.Entities;
global using XJ.Framework.Library.Domain.Repositories.Interfaces;
global using XJ.Framework.Library.Domain.Shared.Enums;

global using XJ.Framework.Rbac.Domain.Entities;
global using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Domain;

/// <summary>
/// 密码帮助类
/// </summary>
public static class PasswordHelper
{
    private const string AesKey = "cK7jN~P*MYX%h0.@";
    
    public const  string FixedSalt = "%JoN#&kkEbcZVYt$";

    /// <summary>
    /// 生成密码盐
    /// </summary>
    /// <param name="length">盐长度，默认16位</param>
    /// <returns>密码盐</returns>
    public static string GenerateSalt(int length = 16)
    {
        byte[] randomBytes = new byte[length];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }

    /// <summary>
    /// 生成密码哈希
    /// </summary>
    /// <param name="password">密码明文</param>
    /// <param name="salt">密码盐</param>
    /// <returns>密码哈希</returns>
    public static string HashPassword(string password, string salt)
    {
        if (string.IsNullOrEmpty(password))
        {
            throw new ArgumentNullException(nameof(password));
        }

        if (string.IsNullOrEmpty(salt))
        {
            throw new ArgumentNullException(nameof(salt));
        }

        // 将密码和盐拼接
        var passwordWithSalt = $"{password}{salt}";
        var passwordBytes = Encoding.UTF8.GetBytes(passwordWithSalt);

        // 使用SHA256进行哈希
        using var sha256 = SHA256.Create();
        var hashBytes = sha256.ComputeHash(passwordBytes);
        return Convert.ToBase64String(hashBytes);
    }

    public static bool VerifyMd5Password(string password, string salt, string hash)
    {
        var computedHash = ExecutePassword(salt, password);
        return computedHash == hash;
    }

    private static string ExecutePassword(string fixSalt, string password)
    {
        var passwordAndSaltBytes = System.Text.Encoding.UTF8.GetBytes($"{password}{fixSalt}");
        using var md5 = System.Security.Cryptography.MD5.Create();
        var hashBytes = md5.ComputeHash(passwordAndSaltBytes);
        return Convert.ToBase64String(hashBytes);
    }

    /// <summary>
    /// 验证密码
    /// </summary>
    /// <param name="password">密码明文</param>
    /// <param name="salt">密码盐</param>
    /// <param name="hash">密码哈希</param>
    /// <returns>是否验证通过</returns>
    public static bool VerifyPassword(string password, string salt, string hash)
    {
        if (string.IsNullOrEmpty(password))
        {
            throw new ArgumentNullException(nameof(password));
        }

        if (string.IsNullOrEmpty(salt))
        {
            throw new ArgumentNullException(nameof(salt));
        }

        if (string.IsNullOrEmpty(hash))
        {
            throw new ArgumentNullException(nameof(hash));
        }

        var computedHash = HashPassword(password, salt);
        return computedHash == hash;
    }

    /// <summary>
    /// 生成随机密码
    /// </summary>
    /// <param name="length">密码长度，默认12位</param>
    /// <returns>随机密码</returns>
    public static string GenerateRandomPassword(int length = 12)
    {
        const string lowerChars = "abcdefghijklmnopqrstuvwxyz";
        const string upperChars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        const string numberChars = "0123456789";
        const string specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";

        var allChars = lowerChars + upperChars + numberChars + specialChars;
        var result = new StringBuilder();

        // 确保至少包含一个小写字母、一个大写字母、一个数字和一个特殊字符
        result.Append(lowerChars[RandomNumberGenerator.GetInt32(lowerChars.Length)]);
        result.Append(upperChars[RandomNumberGenerator.GetInt32(upperChars.Length)]);
        result.Append(numberChars[RandomNumberGenerator.GetInt32(numberChars.Length)]);
        result.Append(specialChars[RandomNumberGenerator.GetInt32(specialChars.Length)]);

        // 生成剩余的随机字符
        for (int i = 4; i < length; i++)
        {
            result.Append(allChars[RandomNumberGenerator.GetInt32(allChars.Length)]);
        }

        // 打乱字符顺序
        var shuffled = result.ToString().ToCharArray();
        for (int i = shuffled.Length - 1; i > 0; i--)
        {
            int j = RandomNumberGenerator.GetInt32(i + 1);
            (shuffled[i], shuffled[j]) = (shuffled[j], shuffled[i]);
        }

        return new string(shuffled);
    }

    /// <summary>
    /// 验证密码 是否包含数字、大小写字母、特殊字符、长度6-20位
    /// </summary>
    /// <param name="password"></param>
    /// <returns></returns>
    public static bool IsValidPassword(string password)
    {
        if (string.IsNullOrEmpty(password) || password.Length < 6 || password.Length > 20)
            return false;
        bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;
        foreach (var c in password)
        {
            if (char.IsUpper(c)) hasUpper = true;
            else if (char.IsLower(c)) hasLower = true;
            else if (char.IsDigit(c)) hasDigit = true;
            else hasSpecial = true;
        }

        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    public static string DecryptAES(string encryptedBase64)
    {
        byte[] keyBytes = Encoding.UTF8.GetBytes(AesKey);
        byte[] iv = new byte[16]; // 全0 IV
        byte[] cipherText = Convert.FromBase64String(encryptedBase64);

        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = keyBytes;
            aesAlg.IV = iv;
            aesAlg.Mode = CipherMode.CBC;
            aesAlg.Padding = PaddingMode.PKCS7;

            using (var decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV))
            using (var msDecrypt = new MemoryStream(cipherText))
            using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
            using (var srDecrypt = new StreamReader(csDecrypt, Encoding.UTF8))
            {
                return srDecrypt.ReadToEnd();
            }
        }
    }
}

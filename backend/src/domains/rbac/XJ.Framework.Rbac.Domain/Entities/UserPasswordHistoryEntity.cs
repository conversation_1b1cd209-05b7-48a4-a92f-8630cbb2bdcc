namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 用户密码历史 实体
/// </summary>
[Table("user_password_histories", Schema = "r")]
[SoftDeleteIndex("IX_UserPasswordHistories_UserId", nameof(UserId))]
public class UserPasswordHistoryEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Column("user_id")]
    public required long UserId { get; set; }

    /// <summary>
    /// 密码哈希
    /// </summary>
    [Column("password_hash")]
    [StringLength(200)]
    public required string PasswordHash { get; set; } = null!;

    /// <summary>
    /// 密码盐
    /// </summary>
    [Column("password_salt")]
    [StringLength(100)]
    public required string PasswordSalt { get; set; } = null!;

} 
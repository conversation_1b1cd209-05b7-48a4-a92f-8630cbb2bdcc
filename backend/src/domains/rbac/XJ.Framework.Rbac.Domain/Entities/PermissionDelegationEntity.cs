namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 权限委托实体
/// </summary>
[Table("permission_delegations", Schema = "r")]
[SoftDeleteIndex("IX_permission_delegations_delegator", nameof(DelegatorId), nameof(Status), nameof(StartTime),
    nameof(EndTime))]
[SoftDeleteIndex("IX_permission_delegations_delegatee", nameof(DelegatorId), nameof(Status), nameof(StartTime),
    nameof(EndTime))]
public class PermissionDelegationEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 委托人ID
    /// </summary>
    /// <remarks>
    /// 发起权限委托的用户ID
    /// </remarks>
    [Column("delegator_id")]
    public required long DelegatorId { get; set; }

    /// <summary>
    /// 被委托人ID
    /// </summary>
    /// <remarks>
    /// 接受权限委托的用户ID
    /// </remarks>
    [Column("delegatee_id")]
    public required long DelegateeId { get; set; }

    /// <summary>
    /// 委托角色ID
    /// </summary>
    /// <remarks>
    /// 被委托的角色ID，表示委托该角色的所有权限
    /// </remarks>
    [Column("role_id")]
    public required long RoleId { get; set; }

    /// <summary>
    /// 组织ID
    /// </summary>
    /// <remarks>
    /// 可选的组织范围限制，如果指定则委托的权限仅在该组织范围内有效
    /// </remarks>
    [Column("organization_id")]
    public long? OrganizationId { get; set; }

    /// <summary>
    /// 岗位ID
    /// </summary>
    /// <remarks>
    /// 可选的岗位范围限制，如果指定则委托的权限仅在该岗位范围内有效
    /// </remarks>
    [Column("position_id")]
    public long? PositionId { get; set; }

    /// <summary>
    /// 委托开始时间
    /// </summary>
    /// <remarks>
    /// 权限委托的生效时间
    /// </remarks>
    [Column("start_time")]
    public required DateTime StartTime { get; set; }

    /// <summary>
    /// 委托结束时间
    /// </summary>
    /// <remarks>
    /// 权限委托的失效时间
    /// </remarks>
    [Column("end_time")]
    public required DateTime EndTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    /// <remarks>
    /// 委托状态：
    /// 1: 生效
    /// 0: 失效
    /// </remarks>
    [Column("status")]
    public required CommonStatus Status { get; set; }

    /// <summary>
    /// 备注说明
    /// </summary>
    /// <remarks>
    /// 关于此次权限委托的补充说明信息
    /// </remarks>
    [Column("remark")]
    [StringLength(1000)]
    public string? Remark { get; set; }
}
namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 用户角色 实体
/// </summary>
[Table("user_roles", Schema = "r")]
[SoftDeleteIndex("IX_UserRoles_UserId_RoleId_OrgId_PosId", nameof(UserId), nameof(RoleId), nameof(OrganizationId),
    nameof(PositionId), IsUnique = true)]
[SoftDeleteIndex("IX_UserRoles_RoleId", nameof(RoleId))]
[SoftDeleteIndex("IX_UserRoles_OrgId", nameof(OrganizationId))]
[SoftDeleteIndex("IX_UserRoles_PosId", nameof(PositionId))]
public class UserRoleEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Column("user_id")]
    public required long UserId { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    [Column("role_id")]
    public required long RoleId { get; set; }

    /// <summary>
    /// 组织ID（岗位角色必填）
    /// </summary>
    [Column("organization_id")]
    public long? OrganizationId { get; set; }

    /// <summary>
    /// 岗位ID（岗位角色必填）
    /// </summary>
    [Column("position_id")]
    public long? PositionId { get; set; }
}
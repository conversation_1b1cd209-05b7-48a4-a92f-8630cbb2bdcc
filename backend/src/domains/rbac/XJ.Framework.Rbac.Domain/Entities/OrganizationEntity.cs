namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 组织 实体
/// </summary>
[Table("organizations", Schema = "r")]
[SoftDeleteIndex("IX_Organizations_Code", nameof(Code), IsUnique = true)]
[SoftDeleteIndex("IX_Organizations_ParentId", nameof(ParentId))]
[SoftDeleteIndex("IX_Organizations_Path", nameof(Path))]
public class OrganizationEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 父级组织ID
    /// </summary>
    [Column("parent_id")]
    public long? ParentId { get; set; }

    /// <summary>
    /// 组织编码
    /// </summary>
    [Column("code")]
    [StringLength(100)]
    public required string Code { get; set; } = null!;

    /// <summary>
    /// 组织名称
    /// </summary>
    [Column("name")]
    [StringLength(200)]
    public required string Name { get; set; } = null!;

    /// <summary>
    /// 名称路径
    /// </summary>
    [Column("name_path")]
    [StringLength(2000)]
    public required string NamePath { get; set; } = null!;

    /// <summary>
    /// 组织层级
    /// </summary>
    [Column("level")]
    public required int Level { get; set; }

    /// <summary>
    /// 组织路径（格式：/1/2/3/）
    /// </summary>
    [Column("path")]
    [StringLength(2000)]
    public required string Path { get; set; } = null!;

    /// <summary>
    /// 同级排序
    /// </summary>
    [Column("sort_order")]
    public required int SortOrder { get; set; }

    /// <summary>
    /// 状态（1-启用，0-禁用）
    /// </summary>
    [Column("status")]
    public required CommonStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    [Column("description")]
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// 组织类型（1-内部组织，2-外部组织）
    /// </summary>
    [Column("org_type")]
    public required OrganizationType OrgType { get; set; }
}
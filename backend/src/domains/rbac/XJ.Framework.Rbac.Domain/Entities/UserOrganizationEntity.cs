namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 用户组织 实体
/// </summary>
[Table("user_organizations", Schema = "r")]
[SoftDeleteIndex("IX_UserOrganizations_UserId_OrgId", nameof(UserId), nameof(OrganizationId), IsUnique = true)]
[SoftDeleteIndex("ix_tokens_token", nameof(OrganizationId))]
public class UserOrganizationEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Column("user_id")]
    public required long UserId { get; set; }

    /// <summary>
    /// 组织ID
    /// </summary>
    [Column("organization_id")]
    public required long OrganizationId { get; set; }

    /// <summary>
    /// 是否主组织
    /// </summary>
    [Column("is_primary")]
    public required bool IsPrimary { get; set; }
}
namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 角色权限 实体
/// </summary>
[Table("role_permissions", Schema = "r")]
[SoftDeleteIndex("UQ_role_permissions", nameof(RoleId), nameof(PermissionId), IsUnique = true)]
[SoftDeleteIndex("IX_role_permissions_permission", nameof(PermissionId))]
public class RolePermissionEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// ??ID
    /// </summary>
    [Column("role_id")]
    public required long RoleId { get; set; }

    /// <summary>
    /// ??ID
    /// </summary>
    [Column("permission_id")]
    public required long PermissionId { get; set; }
}
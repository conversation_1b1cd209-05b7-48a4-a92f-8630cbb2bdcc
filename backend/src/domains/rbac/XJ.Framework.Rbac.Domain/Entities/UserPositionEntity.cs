namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 用户岗位 实体
/// </summary>
[Table("user_positions", Schema = "r")]
[SoftDeleteIndex("IX_UserPositions_UserId_PosId_OrgId", nameof(UserId), nameof(PositionId), nameof(OrganizationId),
    IsUnique = true)]
[SoftDeleteIndex("IX_UserPositions_PosId", nameof(PositionId))]
[SoftDeleteIndex("IX_UserPositions_OrgId", nameof(OrganizationId))]
public class UserPositionEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    [Column("user_id")]
    public required long UserId { get; set; }

    /// <summary>
    /// 岗位ID
    /// </summary>
    [Column("position_id")]
    public required long PositionId { get; set; }

    /// <summary>
    /// 组织ID
    /// </summary>
    [Column("organization_id")]
    public required long OrganizationId { get; set; }

    /// <summary>
    /// 是否主岗位
    /// </summary>
    [Column("is_primary")]
    public required bool IsPrimary { get; set; }

    /// <summary>
    /// 任职开始时间
    /// </summary>
    [Column("start_time")]
    public DateTime? StartTime { get; set; }

    /// <summary>
    /// 任职结束时间
    /// </summary>
    [Column("end_time")]
    public DateTime? EndTime { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    /// <remarks>
    /// Active: 在任
    /// Inactive: 离任
    /// </remarks>
    [Column("status")]
    public required UserPositionStatus Status { get; set; }
}
namespace XJ.Framework.Rbac.Domain.Repositories.Interfaces;

/// <summary>
/// 令牌仓储接口
/// </summary>
public interface ITokenRepository : IEditableRepository<long, TokenEntity>
{
    /// <summary>
    /// 根据令牌值查找
    /// </summary>
    /// <param name="token">令牌字符串</param>
    /// <param name="tokenType">令牌类型</param>
    /// <returns>令牌实体</returns>
    Task<TokenEntity?> FindByTokenAsync(string token, TokenType tokenType);

    /// <summary>
    /// 获取用户的所有活跃令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tokenType">令牌类型，为null时获取所有类型</param>
    /// <returns>活跃的令牌列表</returns>
    Task<List<TokenEntity>> GetUserActiveTokensAsync(long userId, TokenType? tokenType = null);

    /// <summary>
    /// 删除用户的所有令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tokenType">令牌类型，为null时删除所有类型</param>
    /// <returns>操作是否成功</returns>
    Task<bool> DeleteUserTokensAsync(long userId, TokenType? tokenType = null);

    /// <summary>
    /// 清理过期的令牌
    /// </summary>
    /// <param name="tokenType">令牌类型，为null时清理所有类型</param>
    /// <returns>清理的令牌数量</returns>
    Task<int> CleanExpiredTokensAsync(TokenType? tokenType = null);

    /// <summary>
    /// 获取设备的活跃令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tokenType">令牌类型</param>
    /// <returns>活跃的令牌</returns>
    Task<TokenEntity?> GetDeviceActiveTokenAsync(long userId, string deviceId, TokenType tokenType);

    /// <summary>
    /// 获取用户的活跃令牌数量
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tokenType">令牌类型，为null时统计所有类型</param>
    /// <returns>活跃令牌数量</returns>
    Task<int> GetUserActiveTokenCountAsync(long userId, TokenType? tokenType = null);

    /// <summary>
    /// 删除用户最早的令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tokenType">令牌类型</param>
    /// <returns>操作是否成功</returns>
    Task<bool> DeleteOldestTokenAsync(long userId, TokenType tokenType);

    /// <summary>
    /// 更新令牌最后使用时间
    /// </summary>
    /// <param name="token">令牌字符串</param>
    /// <param name="tokenType">令牌类型</param>
    /// <returns>操作是否成功</returns>
    Task<bool> UpdateLastUseTimeAsync(string token, TokenType tokenType);

    /// <summary>
    /// 获取用户指定类型的最新令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tokenType">令牌类型</param>
    /// <returns>最新的令牌实体</returns>
    Task<TokenEntity?> GetLatestTokenAsync(long userId, TokenType tokenType);

    /// <summary>
    /// 检查用户在指定时间段内的令牌创建次数
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="tokenType">令牌类型</param>
    /// <param name="hours">小时数</param>
    /// <returns>创建次数</returns>
    Task<int> GetTokenCreationCountInPeriodAsync(long userId, TokenType tokenType, int hours);

    /// <summary>
    /// 删除指定设备的令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="deviceId">设备ID</param>
    /// <param name="tokenType">令牌类型</param>
    /// <returns>操作是否成功</returns>
    Task<bool> DeleteDeviceTokensAsync(long userId, string deviceId, TokenType tokenType);

    /// <summary>
    /// 根据令牌值删除令牌
    /// </summary>
    /// <param name="token">令牌字符串</param>
    /// <param name="tokenType">令牌类型</param>
    /// <param name="saveChanges">是否提交</param>
    /// <returns>操作是否成功</returns>
    Task<bool> DeleteTokenByValueAsync(string token, TokenType tokenType, bool saveChanges = false);
}
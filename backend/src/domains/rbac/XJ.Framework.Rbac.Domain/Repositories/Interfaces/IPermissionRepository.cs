using System.Collections.Generic;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Repositories;
using XJ.Framework.Rbac.Domain.Entities;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Domain.Repositories.Interfaces;

/// <summary>
/// Permission 仓储接口
/// </summary>
public interface IPermissionRepository : IAuditRepository<long, PermissionEntity>
{
    /// <summary>
    /// 获取用户在指定应用下的所有权限代码列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="appCode">应用code，如果为null则获取所有权限</param>
    /// <param name="permissionTypes">权限类型</param>
    /// <returns>权限代码列表</returns>
    Task<List<string>> GetUserPermissionCodesAsync(long userId, string? appCode = null,
        params PermissionType[] permissionTypes);
    Task<List<PermissionEntity>> GetUserPermissionsAsync(long userId, string? appCode = null,
        params PermissionType[] permissionTypes);
    
    Task<List<PermissionEntity>> GetPermissionsByRootIdAsync(long rootId, string? appCode = null);

    /// <summary>
    /// 获取角色在指定应用下的所有权限代码列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="appCode">应用code，如果为null则获取所有权限</param>
    /// <returns>权限代码列表</returns>
    Task<List<string>> GetRolePermissionCodesAsync(long roleId, string? appCode = null);

    /// <summary>
    /// 获取多个角色在指定应用下的所有权限代码列表
    /// </summary>
    /// <param name="roleIds">角色ID列表</param>
    /// <param name="appCode">应用code，如果为null则获取所有权限</param>
    /// <returns>权限代码列表</returns>
    Task<List<string>> GetRolesPermissionCodesAsync(IEnumerable<long> roleIds, string? appCode = null);

    /// <summary>
    /// 检查权限代码是否存在
    /// </summary>
    /// <param name="code">权限代码</param>
    /// <param name="appCode">应用code，如果为null则检查所有应用</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(string code, string? appCode = null);

    /// <summary>
    /// 根据权限代码获取权限实体
    /// </summary>
    /// <param name="code">权限代码</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中查找</param>
    /// <returns>权限实体</returns>
    Task<PermissionEntity?> GetByCodeAsync(string code, string? appCode = null);

    /// <summary>
    /// 批量创建权限
    /// </summary>
    /// <param name="permissions">权限列表</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateManyAsync(List<PermissionEntity> permissions);

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <param name="appCode">应用ID，如果为null则在所有应用中检查</param>
    /// <returns>是否拥有权限</returns>
    Task<bool> HasPermissionAsync(long userId, string permissionCode, string? appCode = null);

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionId">权限Id</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中检查</param>
    /// <returns>是否拥有权限</returns>
    Task<bool> HasPermissionAsync(long userId, long permissionId, string? appCode = null);

    /// <summary>
    /// 检查用户是否拥有指定权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionIds">权限Id集合</param>
    /// <param name="appCode">应用code，如果为null则在所有应用中检查</param>
    /// <returns>有权限的Id集合</returns>
    Task<List<long>> HasPermissionAsync(long userId, List<long> permissionIds, string? appCode = null);

    /// <summary>
    /// 获取指定应用的所有权限列表
    /// </summary>
    /// <param name="appCode">应用code</param>
    /// <returns>权限列表</returns>
    Task<List<PermissionEntity>> GetAppPermissionsAsync(string appCode);


    /// <summary>
    /// 根据权限编码列表获取权限列表
    /// </summary>
    /// <param name="codes">权限编码列表</param>
    /// <returns>权限列表</returns>
    Task<List<PermissionEntity>> GetByCodesAsync(IEnumerable<string> codes);

    /// <summary>
    /// 检查权限编码是否存在
    /// </summary>
    /// <param name="code">权限编码</param>
    /// <param name="excludeId">排除的权限ID</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsCodeAsync(string code, long? excludeId = null);
}
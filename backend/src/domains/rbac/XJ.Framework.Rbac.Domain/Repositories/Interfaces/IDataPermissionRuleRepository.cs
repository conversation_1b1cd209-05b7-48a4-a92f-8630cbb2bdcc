namespace XJ.Framework.Rbac.Domain.Repositories.Interfaces;

/// <summary>
/// DataPermissionRule 仓储接口
/// </summary>
public interface IDataPermissionRuleRepository : IAuditRepository<long, DataPermissionRuleEntity>
{
    /// <summary>
    /// 获取指定角色的数据权限规则列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>数据权限规则列表</returns>
    Task<IEnumerable<DataPermissionRuleEntity>> GetRoleRulesAsync(long roleId);

    /// <summary>
    /// 获取多个角色的数据权限规则列表
    /// </summary>
    /// <param name="roleIds">角色ID列表</param>
    /// <returns>数据权限规则列表</returns>
    Task<IEnumerable<DataPermissionRuleEntity>> GetRolesRulesAsync(IEnumerable<long> roleIds);

    /// <summary>
    /// 获取指定实体类型的数据权限规则列表
    /// </summary>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <returns>数据权限规则列表</returns>
    Task<IEnumerable<DataPermissionRuleEntity>> GetEntityRulesAsync(string entityTypeName);

    /// <summary>
    /// 检查数据权限规则是否存在
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <param name="ruleType">规则类型</param>
    /// <returns>是否存在</returns>
    Task<bool> ExistsAsync(long roleId, string entityTypeName, DataPermissionRuleType ruleType);

    /// <summary>
    /// 批量创建数据权限规则
    /// </summary>
    /// <param name="rules">规则列表</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateManyAsync(List<DataPermissionRuleEntity> rules);

    /// <summary>
    /// 删除指定角色的所有数据权限规则
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>删除结果</returns>
    Task<bool> DeleteByRoleAsync(long roleId);

    /// <summary>
    /// 获取用户的数据权限规则列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>数据权限规则列表</returns>
    Task<IEnumerable<DataPermissionRuleEntity>> GetUserRulesAsync(long userId);

    /// <summary>
    /// 构建数据权限过滤表达式
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="userId">用户ID</param>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <returns>过滤表达式</returns>
    Task<Expression<Func<TEntity, bool>>> BuildDataPermissionExpressionAsync<TEntity>(long userId,
        string entityTypeName)
        where TEntity : class;

    /// <summary>
    /// 应用数据权限过滤
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="query">原始查询</param>
    /// <param name="userId">用户ID</param>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <returns>过滤后的查询</returns>
    Task<IQueryable<TEntity>> ApplyDataPermissionFilterAsync<TEntity>(
        IQueryable<TEntity> query,
        long userId,
        string entityTypeName) where TEntity : class;

    /// <summary>
    /// 检查数据访问权限
    /// </summary>
    /// <typeparam name="TEntity">实体类型</typeparam>
    /// <param name="userId">用户ID</param>
    /// <param name="entityId">实体ID</param>
    /// <param name="entityTypeName">实体类型名称</param>
    /// <returns>是否有权限访问</returns>
    Task<bool> CheckDataPermissionAsync<TEntity>(
        long userId,
        long entityId,
        string entityTypeName) where TEntity : class;
}
namespace XJ.Framework.Rbac.Domain.Repositories.Interfaces;

/// <summary>
/// UserRole 仓储接口
/// </summary>
public interface IUserRoleRepository : IAuditRepository<long, UserRoleEntity>
{
    /// <summary>
    /// 获取用户的所有角色代码列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>角色代码列表</returns>
    Task<List<string>> GetUserRoleCodesAsync(long userId);

    /// <summary>
    /// 获取用户的所有角色ID列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>角色ID列表</returns>
    Task<List<long>> GetUserRoleIdsAsync(long userId);

    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <returns>是否具有该角色</returns>
    Task<bool> HasRoleAsync(long userId, long roleId);

    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleCode">角色code</param>
    /// <returns>是否具有该角色</returns>
    Task<bool> HasRoleAsync(long userId, string roleCode);

    /// <summary>
    /// 批量分配角色给用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleIds">角色ID列表</param>
    /// <returns>分配结果</returns>
    Task<bool> AssignRolesAsync(long userId, IEnumerable<long> roleIds);

    /// <summary>
    /// 移除用户的角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleIds">要移除的角色ID列表</param>
    /// <returns>移除结果</returns>
    Task<bool> RemoveRolesAsync(long userId, IEnumerable<long> roleIds);

    /// <summary>
    /// 移除用户的所有角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>移除结果</returns>
    Task<bool> RemoveAllRolesAsync(long userId);

    /// <summary>
    /// 获取角色下的所有用户ID列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>用户ID列表</returns>
    Task<List<long>> GetRoleUserIdsAsync(long roleId);

    /// <summary>
    /// 获取角色下的所有用户ID列表
    /// </summary>
    /// <param name="roleCode">角色code</param>
    /// <param name="roleTypes">角色类型</param>
    /// <returns>用户ID列表</returns>
    Task<List<long>> GetRoleUserIdsAsync(string roleCode, params RoleType[] roleTypes);

    /// <summary>
    /// 获取多个用户的角色ID列表
    /// </summary>
    /// <param name="userIds">用户ID列表</param>
    /// <returns>用户角色ID字典，key为用户ID，value为该用户的角色ID列表</returns>
    Task<Dictionary<long, List<long>>> GetUsersRoleIdsAsync(IEnumerable<long> userIds);
}
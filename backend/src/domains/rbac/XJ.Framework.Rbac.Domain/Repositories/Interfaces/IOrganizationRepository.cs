
namespace XJ.Framework.Rbac.Domain.Repositories.Interfaces;

/// <summary>
/// Organization 仓储接口
/// </summary>

public interface IOrganizationRepository : IAuditRepository<long, OrganizationEntity>
{
    Task<List<OrganizationEntity>> GetOrganizationChildrenAsync(string organizationCode, bool includeSelf);
    Task<Dictionary<string, List<OrganizationEntity>>> GetOrganizationChildrenAsync(List<string> organizationCodes, bool includeSelf);
}

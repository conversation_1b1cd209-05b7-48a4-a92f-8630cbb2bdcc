// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using XJ.Framework.Rbac.EntityFrameworkCore;

#nullable disable

namespace XJ.Framework.Rbac.EntityFrameworkCore.Migrations
{
    [DbContext(typeof(RbacDbContext))]
    partial class RbacDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.DataPermissionCacheEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CacheTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("cache_time")
                        .HasComment("缓存时间");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expire_time")
                        .HasComment("过期时间");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("PermissionScope")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("permission_scope")
                        .HasComment("权限范围");

                    b.Property<string>("ResourceType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("resource_type")
                        .HasComment("资源类型");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint")
                        .HasColumnName("role_id")
                        .HasComment("角色ID");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("ExpireTime")
                        .HasDatabaseName("IX_data_permission_cache_expire")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("data_permission_cache", "r", t =>
                        {
                            t.HasComment("数据权限缓存实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.DataPermissionRuleEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("EntityTypeName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("entity_type_name")
                        .HasComment("实体类型名称");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Remark")
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("remark")
                        .HasComment("备注说明");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint")
                        .HasColumnName("role_id")
                        .HasComment("角色ID");

                    b.Property<int>("RuleType")
                        .HasColumnType("int")
                        .HasColumnName("rule_type")
                        .HasComment("规则类型");

                    b.Property<string>("RuleValue")
                        .HasMaxLength(4000)
                        .HasColumnType("nvarchar(4000)")
                        .HasColumnName("rule_value")
                        .HasComment("规则值");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态");

                    b.HasKey("Key");

                    b.HasIndex("RoleId", "EntityTypeName")
                        .HasDatabaseName("IX_data_permission_rules_role_resource")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("data_permission_rules", "r", t =>
                        {
                            t.HasComment("数据权限规则实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.OrganizationEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("code")
                        .HasComment("组织编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("description")
                        .HasComment("描述");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int>("Level")
                        .HasColumnType("int")
                        .HasColumnName("level")
                        .HasComment("组织层级");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("name")
                        .HasComment("组织名称");

                    b.Property<string>("NamePath")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("name_path")
                        .HasComment("名称路径");

                    b.Property<int>("OrgType")
                        .HasColumnType("int")
                        .HasColumnName("org_type")
                        .HasComment("组织类型（1-内部组织，2-外部组织）");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint")
                        .HasColumnName("parent_id")
                        .HasComment("父级组织ID");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasMaxLength(2000)
                        .HasColumnType("nvarchar(2000)")
                        .HasColumnName("path")
                        .HasComment("组织路径（格式：/1/2/3/）");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order")
                        .HasComment("同级排序");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态（1-启用，0-禁用）");

                    b.HasKey("Key");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Organizations_Code")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("IX_Organizations_ParentId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Path")
                        .HasDatabaseName("IX_Organizations_Path")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("organizations", "r", t =>
                        {
                            t.HasComment("组织 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1L,
                            Code = "cacms",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "中国中医科学院",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)),
                            Level = 1,
                            Name = "中国中医科学院",
                            NamePath = "/中国中医科学院",
                            OrgType = 1,
                            Path = "/1/",
                            SortOrder = 0,
                            Status = 1
                        },
                        new
                        {
                            Key = 2L,
                            Code = "ccebtcm",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "中国中医药循证医学中心",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)),
                            Level = 2,
                            Name = "中国中医药循证医学中心",
                            NamePath = "/中国中医科学院/中国中医药循证医学中心",
                            OrgType = 1,
                            ParentId = 1L,
                            Path = "/1/2/",
                            SortOrder = 0,
                            Status = 1
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.PermissionDelegationEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<long>("DelegateeId")
                        .HasColumnType("bigint")
                        .HasColumnName("delegatee_id")
                        .HasComment("被委托人ID");

                    b.Property<long>("DelegatorId")
                        .HasColumnType("bigint")
                        .HasColumnName("delegator_id")
                        .HasComment("委托人ID");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("end_time")
                        .HasComment("委托结束时间");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long?>("OrganizationId")
                        .HasColumnType("bigint")
                        .HasColumnName("organization_id")
                        .HasComment("组织ID");

                    b.Property<long?>("PositionId")
                        .HasColumnType("bigint")
                        .HasColumnName("position_id")
                        .HasComment("岗位ID");

                    b.Property<string>("Remark")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("remark")
                        .HasComment("备注说明");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint")
                        .HasColumnName("role_id")
                        .HasComment("委托角色ID");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("start_time")
                        .HasComment("委托开始时间");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态");

                    b.HasKey("Key");

                    b.HasIndex("DelegatorId", "Status", "StartTime", "EndTime")
                        .HasDatabaseName("IX_permission_delegations_delegatee")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("permission_delegations", "r", t =>
                        {
                            t.HasComment("权限委托实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.PermissionEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("AppCode")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_code")
                        .HasComment("应用编码");

                    b.Property<string>("AppId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("app_id")
                        .HasComment("应用ID");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("code")
                        .HasComment("权限编码");

                    b.Property<string>("Component")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("component")
                        .HasComment("前端组件");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("description")
                        .HasComment("权限描述");

                    b.Property<string>("Icon")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("icon")
                        .HasComment("图标");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<int?>("Method")
                        .HasMaxLength(40)
                        .HasColumnType("int")
                        .HasColumnName("method")
                        .HasComment("HTTP方法");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("name")
                        .HasComment("权限名称");

                    b.Property<long?>("ParentId")
                        .HasColumnType("bigint")
                        .HasColumnName("parent_id")
                        .HasComment("父级权限ID");

                    b.Property<string>("Path")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("path")
                        .HasComment("路由路径");

                    b.Property<string>("Redirect")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("redirect")
                        .HasComment("重定向地址");

                    b.Property<int>("SortOrder")
                        .HasColumnType("int")
                        .HasColumnName("sort_order")
                        .HasComment("排序号");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("type")
                        .HasComment("权限类型");

                    b.HasKey("Key");

                    b.HasIndex("ParentId")
                        .HasDatabaseName("IX_permissions_parent")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Path")
                        .HasDatabaseName("IX_permissions_path")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Code", "AppCode")
                        .IsUnique()
                        .HasDatabaseName("UQ_permissions_code")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("AppId", "AppCode", "Path")
                        .HasDatabaseName("IX_permissions_app")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("permissions", "r", t =>
                        {
                            t.HasComment("功能权限实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 111L,
                            AppCode = "messaging-mgt",
                            Code = "MessageProvider",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "服务商管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "服务商管理",
                            ParentId = 11L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 112L,
                            AppCode = "messaging-mgt",
                            Code = "MessageAccount",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "账户管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "账户管理",
                            ParentId = 11L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 113L,
                            AppCode = "messaging-mgt",
                            Code = "MessageTemplate",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "模板管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "模板管理",
                            ParentId = 11L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 114L,
                            AppCode = "messaging-mgt",
                            Code = "MessageSendRecords",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "发送记录页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "发送记录",
                            ParentId = 11L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 11L,
                            AppCode = "messaging-mgt",
                            Code = "Messaging",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "消息管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "消息管理",
                            SortOrder = 0,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = 2L,
                            AppCode = "rbac-mgt",
                            Code = "system",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "系统管理模块",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "系统管理",
                            SortOrder = 0,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = 10L,
                            AppCode = "logging-mgt",
                            Code = "system:logging",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "日志管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "日志管理",
                            ParentId = 2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 3L,
                            AppCode = "rbac-mgt",
                            Code = "system:user",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "用户管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "用户管理",
                            ParentId = 2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 4L,
                            AppCode = "rbac-mgt",
                            Code = "system:role",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "角色管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "角色管理",
                            ParentId = 2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 5L,
                            AppCode = "rbac-mgt",
                            Code = "system:organization",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "组织管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "组织管理",
                            ParentId = 2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 7L,
                            AppCode = "rbac-mgt",
                            Code = "system:position",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "岗位管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "岗位管理",
                            ParentId = 2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 8L,
                            AppCode = "rbac-mgt",
                            Code = "system:permission",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "权限管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "权限管理",
                            ParentId = 2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 10001L,
                            AppCode = "files-mgt",
                            Code = "files",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "存储管理模块",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "存储管理",
                            SortOrder = 0,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = 10002L,
                            AppCode = "files-mgt",
                            Code = "files:storage",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "存储管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "存储管理",
                            ParentId = 10001L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 10003L,
                            AppCode = "files-mgt",
                            Code = "files:file-type",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "文件类型管理页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "文件类型管理",
                            ParentId = 10001L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 1939738162445484036L,
                            AppCode = "rbac-mgt",
                            Code = "system:user:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "添加用户按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "添加用户",
                            ParentId = 3L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484037L,
                            AppCode = "rbac-mgt",
                            Code = "system:user:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑用户按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑用户",
                            ParentId = 3L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484038L,
                            AppCode = "rbac-mgt",
                            Code = "system:user:disable",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "禁用用户按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "禁用用户",
                            ParentId = 3L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484039L,
                            AppCode = "rbac-mgt",
                            Code = "system:user:enable",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "启用用户按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "启用用户",
                            ParentId = 3L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484040L,
                            AppCode = "rbac-mgt",
                            Code = "system:user:role",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "绑定角色按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "绑定角色",
                            ParentId = 3L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484041L,
                            AppCode = "rbac-mgt",
                            Code = "system:user:org",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "绑定组织架构按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "绑定组织架构",
                            ParentId = 3L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484042L,
                            AppCode = "rbac-mgt",
                            Code = "system:role:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "添加角色按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "添加角色",
                            ParentId = 4L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484043L,
                            AppCode = "rbac-mgt",
                            Code = "system:role:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑角色按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑角色",
                            ParentId = 4L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484044L,
                            AppCode = "rbac-mgt",
                            Code = "system:role:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除角色按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除角色",
                            ParentId = 4L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484045L,
                            AppCode = "rbac-mgt",
                            Code = "system:role:permission",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "分配角色权限按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "分配角色权限",
                            ParentId = 4L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484046L,
                            AppCode = "rbac-mgt",
                            Code = "system:organization:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "添加组织按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "添加组织",
                            ParentId = 5L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484047L,
                            AppCode = "rbac-mgt",
                            Code = "system:organization:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑组织按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑组织",
                            ParentId = 5L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484048L,
                            AppCode = "rbac-mgt",
                            Code = "system:organization:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除组织按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除组织",
                            ParentId = 5L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484049L,
                            AppCode = "rbac-mgt",
                            Code = "system:position:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "添加岗位按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "添加岗位",
                            ParentId = 7L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484050L,
                            AppCode = "rbac-mgt",
                            Code = "system:position:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑岗位按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑岗位",
                            ParentId = 7L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484051L,
                            AppCode = "rbac-mgt",
                            Code = "system:position:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除岗位按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除岗位",
                            ParentId = 7L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484052L,
                            AppCode = "rbac-mgt",
                            Code = "system:permission:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "添加权限按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "添加权限",
                            ParentId = 8L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484053L,
                            AppCode = "rbac-mgt",
                            Code = "system:permission:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑权限按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑权限",
                            ParentId = 8L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484054L,
                            AppCode = "rbac-mgt",
                            Code = "system:permission:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除权限按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除权限",
                            ParentId = 8L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 9L,
                            Code = "api-permissions-root",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2440), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2440), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "Api权限",
                            SortOrder = 999,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = 10011L,
                            AppCode = "files-mgt",
                            Code = "files:storage:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "新增存储按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "新增存储",
                            ParentId = 10002L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10012L,
                            AppCode = "files-mgt",
                            Code = "files:storage:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑存储按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑存储",
                            ParentId = 10002L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10013L,
                            AppCode = "files-mgt",
                            Code = "files:storage:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除存储按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除存储",
                            ParentId = 10002L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10021L,
                            AppCode = "files-mgt",
                            Code = "files:file-type:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "新增文件类型按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "新增文件类型",
                            ParentId = 10003L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10022L,
                            AppCode = "files-mgt",
                            Code = "files:file-type:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑文件类型按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑文件类型",
                            ParentId = 10003L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10023L,
                            AppCode = "files-mgt",
                            Code = "files:file-type:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除文件类型按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除文件类型",
                            ParentId = 10003L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10031L,
                            AppCode = "files-mgt",
                            Code = "files:file-type:storage:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "新增类型存储关系按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "新增类型存储关系",
                            ParentId = 10003L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10032L,
                            AppCode = "files-mgt",
                            Code = "files:file-type:storage",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "类型存储关系配置按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "类型存储关系配置",
                            ParentId = 10003L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 10033L,
                            AppCode = "files-mgt",
                            Code = "files:file-type:storage:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除类型存储关系按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除类型存储关系",
                            ParentId = 10003L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 20001L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form-management",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "表单管理模块",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "表单管理",
                            SortOrder = 0,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = 20002L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form-management:form-list",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "表单定义页面",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "表单定义",
                            ParentId = 20001L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 1939738162445484055L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form:defined:add",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "新增表单按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "新增表单",
                            ParentId = 20002L,
                            SortOrder = 1,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484056L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form:defined:edit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "编辑表单按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "编辑表单",
                            ParentId = 20002L,
                            SortOrder = 2,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 1939738162445484057L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form:defined:design",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "表单设计按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "表单设计",
                            ParentId = 20002L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 20003L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form-management:design",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "表单设计",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "表单设计",
                            ParentId = 20001L,
                            SortOrder = 3,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 1939738162445484058L,
                            AppCode = "dynamic-form-mgt",
                            Code = "form:defined:delete",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "删除表单按钮",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "删除表单",
                            ParentId = 20002L,
                            SortOrder = 4,
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = -30035L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemAllList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "管理员-全部项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "管理员-全部项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30036L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemEdit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "管理员-项目修改",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "管理员-项目修改",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30001L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingJudgeList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-待判断项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-待判断项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30002L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemApplyEditList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-再修改申请列表",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-再修改申请列表",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30003L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemReturnEditList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-再修改退回列表",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-再修改退回列表",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30004L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemNonTraditionalList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-非传统医学项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-非传统医学项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30005L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingSendNumberList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-待发号项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-待发号项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30006L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingReviewList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-待审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-待审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30007L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectJudge",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-判断项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-判断项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30008L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectReviewEdit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-再修改项目审核",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-再修改项目审核",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30009L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemApprovedList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-已通过项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-已通过项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30010L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectSendNumber",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30011L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingAssignList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-待分配项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-待分配项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30012L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingReviewList2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-待复审项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-待复审项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30013L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemReviewReturnedList2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-已退回项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-已退回项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30014L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemApprovedList2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-已通过项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-已通过项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30015L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemReAssignList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-重新分配项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-重新分配项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30016L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectReview2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30017L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectAssign",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-分配项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-分配项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30018L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingAssignReviewList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-待分审项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-待分审项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30019L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingReviewList3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-待审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-待审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30020L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectAssignReview",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-分审项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-分审项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30021L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectReview3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30022L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemReviewReturnedList3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-已退回项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-已退回项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30023L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemApprovedList3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-已通过项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-已通过项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30024L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingReviewList4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "四审-待审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "四审-待审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30025L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemProjectReview4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "四审-审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "四审-审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30026L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemReviewReturnedList4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "四审-已退回项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "四审-已退回项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30027L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemApprovedList4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "四审-已通过项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "四审-已通过项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30028L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingApprovedList4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "四审-等待上级审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "四审-等待上级审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30029L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingApprovedList3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "三审-等待上级审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "三审-等待上级审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30030L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemPendingApprovedList2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "二审-等待上级审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "二审-等待上级审核项目",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30031L,
                            AppCode = "itmctr-mgt",
                            Code = "projectSystemAllSubmittedList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-审核状态查询",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-审核状态查询",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30032L,
                            AppCode = "itmctr-mgt",
                            Code = "projectUserEditReviewProjectView",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-再修改审核",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-再修改审核",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -30034L,
                            AppCode = "itmctr-mgt",
                            Code = "projectRecall",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "一审-项目召回",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "一审-项目召回",
                            ParentId = -1L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -1L,
                            AppCode = "itmctr-mgt",
                            Code = "projectApproval",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "项目审核",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "项目审核",
                            SortOrder = 0,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = -2L,
                            AppCode = "itmctr-mgt",
                            Code = "project",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "项目中心",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "项目中心",
                            SortOrder = 0,
                            Status = 1,
                            Type = 1
                        },
                        new
                        {
                            Key = -20001L,
                            AppCode = "itmctr-mgt",
                            Code = "projectUserAllList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "我的项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "我的项目",
                            ParentId = -2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -20002L,
                            AppCode = "itmctr-mgt",
                            Code = "projectUserPendingSubmit",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "待提交项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "待提交项目",
                            ParentId = -2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -20003L,
                            AppCode = "itmctr-mgt",
                            Code = "projectUserPendingApproval",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "待审核项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "待审核项目",
                            ParentId = -2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -20004L,
                            AppCode = "itmctr-mgt",
                            Code = "projectUserApprovedList",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "已通过项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "已通过项目",
                            ParentId = -2L,
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = -20005L,
                            AppCode = "itmctr-mgt",
                            Code = "projectUserAdd",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "注册新项目",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "注册新项目",
                            SortOrder = 0,
                            Status = 1,
                            Type = 2
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.PositionEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("code")
                        .HasComment("岗位编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("description")
                        .HasComment("描述");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("name")
                        .HasComment("岗位名称");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint")
                        .HasColumnName("role_id")
                        .HasComment("角色id");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态");

                    b.HasKey("Key");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Positions_Code")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_Positions_RoleId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("positions", "r", t =>
                        {
                            t.HasComment("岗位 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1L,
                            Code = "CHECKER_LEVEL_1",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "总审核员",
                            RoleId = 2L,
                            Status = 1
                        },
                        new
                        {
                            Key = 2L,
                            Code = "CHECKER_LEVEL_2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "高级审核员",
                            RoleId = 3L,
                            Status = 1
                        },
                        new
                        {
                            Key = 3L,
                            Code = "CHECKER_LEVEL_3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "中级审核员",
                            RoleId = 4L,
                            Status = 1
                        },
                        new
                        {
                            Key = 4L,
                            Code = "CHECKER_LEVEL_4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(3170), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "初级审核员",
                            RoleId = 5L,
                            Status = 1
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.RoleEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("code")
                        .HasComment("角色编码");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Description")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("description")
                        .HasComment("描述");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("name")
                        .HasComment("角色名称");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态");

                    b.Property<int>("Type")
                        .HasColumnType("int")
                        .HasColumnName("type")
                        .HasComment("角色类型");

                    b.HasKey("Key");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("IX_Roles_Code")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("roles", "r", t =>
                        {
                            t.HasComment("角色 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1L,
                            Code = "ADMIN",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2030), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "系统管理员角色",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "系统管理员",
                            Status = 1,
                            Type = 3
                        },
                        new
                        {
                            Key = 2L,
                            Code = "CHECKER_LEVEL_1",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "总审核员",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "总审核员",
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 3L,
                            Code = "CHECKER_LEVEL_2",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "高级审核员",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "高级审核员",
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 4L,
                            Code = "CHECKER_LEVEL_3",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "中级审核员",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2040), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "中级审核员",
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 5L,
                            Code = "CHECKER_LEVEL_4",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "初级审核员",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "初级审核员",
                            Status = 1,
                            Type = 2
                        },
                        new
                        {
                            Key = 6L,
                            Code = "RESEARCHER",
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            Description = "研究员",
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2050), new TimeSpan(0, 0, 0, 0, 0)),
                            Name = "研究员",
                            Status = 1,
                            Type = 1
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.RolePermissionEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("PermissionId")
                        .HasColumnType("bigint")
                        .HasColumnName("permission_id")
                        .HasComment("??ID");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint")
                        .HasColumnName("role_id")
                        .HasComment("??ID");

                    b.HasKey("Key");

                    b.HasIndex("PermissionId")
                        .HasDatabaseName("IX_role_permissions_permission")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RoleId", "PermissionId")
                        .IsUnique()
                        .HasDatabaseName("UQ_role_permissions")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("role_permissions", "r", t =>
                        {
                            t.HasComment("角色权限 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1939738162445484059L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 111L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484060L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 112L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484061L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 113L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484062L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 114L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484063L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 11L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484064L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 2L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484065L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484066L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 3L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484067L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 4L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484068L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 5L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484069L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 7L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484070L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 8L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484071L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10001L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484072L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10002L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484073L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10003L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484074L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484036L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484075L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484037L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484076L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484038L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484077L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484039L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484078L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484040L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484079L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484041L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484080L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484042L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484081L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484043L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484082L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484044L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484083L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484045L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484084L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484046L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484085L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484047L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484086L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484048L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484087L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484049L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484088L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484050L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484089L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484051L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484090L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484052L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484091L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484053L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484092L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484054L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484093L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 9L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484094L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10011L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484095L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10012L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484096L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10013L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484097L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10021L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484098L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10022L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484099L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10023L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484100L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10031L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484101L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10032L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484102L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 10033L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484103L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 20001L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484104L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 20002L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484105L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484055L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484106L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484056L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484107L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484057L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484108L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 20003L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484109L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = 1939738162445484058L,
                            RoleId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484110L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -1L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484111L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -1L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484112L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -1L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484113L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -1L,
                            RoleId = 5L
                        },
                        new
                        {
                            Key = 1939738162445484114L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30001L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484115L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30002L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484116L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30003L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484117L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30004L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484118L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30005L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484119L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30006L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484120L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30007L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484121L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30008L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484122L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30009L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484123L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30010L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484124L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30011L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484125L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30012L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484126L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30013L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484127L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30014L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484128L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30015L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484129L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30016L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484130L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30017L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484131L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30018L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484132L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30019L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484133L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30020L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484134L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30021L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484135L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30022L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484136L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30023L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484137L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30024L,
                            RoleId = 5L
                        },
                        new
                        {
                            Key = 1939738162445484138L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30025L,
                            RoleId = 5L
                        },
                        new
                        {
                            Key = 1939738162445484139L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30026L,
                            RoleId = 5L
                        },
                        new
                        {
                            Key = 1939738162445484140L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30027L,
                            RoleId = 5L
                        },
                        new
                        {
                            Key = 1939738162445484141L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30028L,
                            RoleId = 5L
                        },
                        new
                        {
                            Key = 1939738162445484142L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30029L,
                            RoleId = 4L
                        },
                        new
                        {
                            Key = 1939738162445484143L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30030L,
                            RoleId = 3L
                        },
                        new
                        {
                            Key = 1939738162445484144L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -30030L,
                            RoleId = 2L
                        },
                        new
                        {
                            Key = 1939738162445484145L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -2L,
                            RoleId = 6L
                        },
                        new
                        {
                            Key = 1939738162445484146L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -20001L,
                            RoleId = 6L
                        },
                        new
                        {
                            Key = 1939738162445484147L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -20002L,
                            RoleId = 6L
                        },
                        new
                        {
                            Key = 1939738162445484148L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -20003L,
                            RoleId = 6L
                        },
                        new
                        {
                            Key = 1939738162445484149L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -20004L,
                            RoleId = 6L
                        },
                        new
                        {
                            Key = 1939738162445484150L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2190), new TimeSpan(0, 0, 0, 0, 0)),
                            PermissionId = -20005L,
                            RoleId = 6L
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.TokenEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<DateTimeOffset>("CreateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("create_time")
                        .HasComment("创建时间");

                    b.Property<string>("DeviceId")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("device_id")
                        .HasComment("设备标识");

                    b.Property<DateTimeOffset>("ExpireTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("expire_time")
                        .HasComment("过期时间");

                    b.Property<string>("IpAddress")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("ip_address")
                        .HasComment("IP地址");

                    b.Property<DateTimeOffset?>("LastUseTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_use_time")
                        .HasComment("最后使用时间");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)")
                        .HasColumnName("token")
                        .HasComment("令牌值");

                    b.Property<int>("TokenType")
                        .HasColumnType("int")
                        .HasColumnName("token_type")
                        .HasComment("令牌类型");

                    b.Property<string>("UserAgent")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)")
                        .HasColumnName("user_agent")
                        .HasComment("用户代理");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("Token")
                        .HasDatabaseName("ix_tokens_token");

                    b.HasIndex("UserId")
                        .HasDatabaseName("ix_tokens_user_id");

                    b.HasIndex("TokenType", "ExpireTime")
                        .HasDatabaseName("ix_tokens_type_expire");

                    b.ToTable("tokens", "r", t =>
                        {
                            t.HasComment("令牌实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.UserEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("Avatar")
                        .HasMaxLength(400)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("avatar")
                        .HasComment("头像URL");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Email")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("email")
                        .HasComment("邮箱");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit")
                        .HasColumnName("email_confirmed")
                        .HasComment("邮箱是否已验证");

                    b.Property<DateTimeOffset?>("EmailConfirmedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("email_confirmed_time")
                        .HasComment("邮箱验证时间");

                    b.Property<string>("LastLoginIp")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("last_login_ip")
                        .HasComment("最后登录IP");

                    b.Property<DateTimeOffset?>("LastLoginTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_login_time")
                        .HasComment("最后登录时间");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Mobile")
                        .HasMaxLength(40)
                        .HasColumnType("nvarchar(40)")
                        .HasColumnName("mobile")
                        .HasComment("手机号");

                    b.Property<bool>("MobileConfirmed")
                        .HasColumnType("bit")
                        .HasColumnName("mobile_confirmed")
                        .HasComment("手机号是否已验证");

                    b.Property<DateTimeOffset?>("MobileConfirmedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("mobile_confirmed_time")
                        .HasComment("手机号验证时间");

                    b.Property<int>("PasswordErrorCount")
                        .HasColumnType("int")
                        .HasColumnName("password_error_count")
                        .HasComment("密码错误次数");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("password_hash")
                        .HasComment("密码哈希");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("password_salt")
                        .HasComment("密码盐");

                    b.Property<DateTimeOffset?>("PasswordUpdateTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("password_update_time")
                        .HasComment("密码更新时间");

                    b.Property<string>("RealName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("real_name")
                        .HasComment("真实姓名");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("用户状态");

                    b.Property<int>("UserType")
                        .HasColumnType("int")
                        .HasColumnName("user_type")
                        .HasComment("用户类型");

                    b.Property<string>("Username")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("username")
                        .HasComment("用户名");

                    b.HasKey("Key");

                    b.HasIndex("Email")
                        .HasDatabaseName("IX_Users_Email")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Mobile")
                        .HasDatabaseName("IX_Users_Mobile")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("Username")
                        .IsUnique()
                        .HasDatabaseName("IX_Users_Username")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("users", "r", t =>
                        {
                            t.HasComment("用户实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1810), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            EmailConfirmed = true,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1810), new TimeSpan(0, 0, 0, 0, 0)),
                            MobileConfirmed = true,
                            PasswordErrorCount = 0,
                            PasswordHash = "dZ9FWZeXEByDv52pui8tqc01/9UixvSwGE82+mLoQOg=",
                            PasswordSalt = "Obswk9PIZOuGzIyYjxZUkQ==",
                            RealName = "系统管理员",
                            Status = 1,
                            UserType = 3,
                            Username = "xjsupport"
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.UserExtEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("ContactAddress")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(400)")
                        .HasColumnName("contact_address")
                        .HasComment("联系地址");

                    b.Property<string>("Country")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("country")
                        .HasComment("国家");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Gender")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)")
                        .HasColumnName("gender")
                        .HasComment("性别");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("Telephone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasColumnName("telephone")
                        .HasComment("固定电话");

                    b.Property<string>("Unit")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("unit")
                        .HasComment("注册单位名称");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("UserId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserExt_UserId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("user_ext", "r", t =>
                        {
                            t.HasComment("用户扩展信息");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.UserOrganizationEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit")
                        .HasColumnName("is_primary")
                        .HasComment("是否主组织");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("OrganizationId")
                        .HasColumnType("bigint")
                        .HasColumnName("organization_id")
                        .HasComment("组织ID");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("ix_tokens_token")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("UserId", "OrganizationId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserOrganizations_UserId_OrgId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("user_organizations", "r", t =>
                        {
                            t.HasComment("用户组织 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            IsPrimary = true,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(1940), new TimeSpan(0, 0, 0, 0, 0)),
                            OrganizationId = 1L,
                            UserId = 1L
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.UserPasswordHistoryEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)")
                        .HasColumnName("password_hash")
                        .HasComment("密码哈希");

                    b.Property<string>("PasswordSalt")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("password_salt")
                        .HasComment("密码盐");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("UserId")
                        .HasDatabaseName("IX_UserPasswordHistories_UserId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("user_password_histories", "r", t =>
                        {
                            t.HasComment("用户密码历史 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.UserPositionEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("end_time")
                        .HasComment("任职结束时间");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("bit")
                        .HasColumnName("is_primary")
                        .HasComment("是否主岗位");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long>("OrganizationId")
                        .HasColumnType("bigint")
                        .HasColumnName("organization_id")
                        .HasComment("组织ID");

                    b.Property<long>("PositionId")
                        .HasColumnType("bigint")
                        .HasColumnName("position_id")
                        .HasComment("岗位ID");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("start_time")
                        .HasComment("任职开始时间");

                    b.Property<int>("Status")
                        .HasColumnType("int")
                        .HasColumnName("status")
                        .HasComment("状态");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_UserPositions_OrgId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("PositionId")
                        .HasDatabaseName("IX_UserPositions_PosId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("UserId", "PositionId", "OrganizationId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserPositions_UserId_PosId_OrgId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("user_positions", "r", t =>
                        {
                            t.HasComment("用户岗位 实体");
                        });
                });

            modelBuilder.Entity("XJ.Framework.Rbac.Domain.Entities.UserRoleEntity", b =>
                {
                    b.Property<long>("Key")
                        .HasColumnType("bigint")
                        .HasColumnName("id");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("created_by");

                    b.Property<DateTimeOffset>("CreatedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("created_time");

                    b.Property<bool>("Deleted")
                        .HasColumnType("bit")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LastModifiedBy")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasColumnName("last_modified_by");

                    b.Property<DateTimeOffset?>("LastModifiedTime")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("last_modified_time");

                    b.Property<long?>("OrganizationId")
                        .HasColumnType("bigint")
                        .HasColumnName("organization_id")
                        .HasComment("组织ID（岗位角色必填）");

                    b.Property<long?>("PositionId")
                        .HasColumnType("bigint")
                        .HasColumnName("position_id")
                        .HasComment("岗位ID（岗位角色必填）");

                    b.Property<long>("RoleId")
                        .HasColumnType("bigint")
                        .HasColumnName("role_id")
                        .HasComment("角色ID");

                    b.Property<long>("UserId")
                        .HasColumnType("bigint")
                        .HasColumnName("user_id")
                        .HasComment("用户ID");

                    b.HasKey("Key");

                    b.HasIndex("OrganizationId")
                        .HasDatabaseName("IX_UserRoles_OrgId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("PositionId")
                        .HasDatabaseName("IX_UserRoles_PosId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("RoleId")
                        .HasDatabaseName("IX_UserRoles_RoleId")
                        .HasFilter("[is_deleted] = 0");

                    b.HasIndex("UserId", "RoleId", "OrganizationId", "PositionId")
                        .IsUnique()
                        .HasDatabaseName("IX_UserRoles_UserId_RoleId_OrgId_PosId")
                        .HasFilter("[is_deleted] = 0");

                    b.ToTable("user_roles", "r", t =>
                        {
                            t.HasComment("用户角色 实体");
                        });

                    b.HasData(
                        new
                        {
                            Key = 1L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2140), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2140), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleId = 1L,
                            UserId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484032L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleId = 2L,
                            UserId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484033L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleId = 3L,
                            UserId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484034L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleId = 4L,
                            UserId = 1L
                        },
                        new
                        {
                            Key = 1939738162445484035L,
                            CreatedBy = "system",
                            CreatedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            Deleted = false,
                            LastModifiedBy = "system",
                            LastModifiedTime = new DateTimeOffset(new DateTime(2025, 6, 30, 17, 29, 51, 176, DateTimeKind.Unspecified).AddTicks(2160), new TimeSpan(0, 0, 0, 0, 0)),
                            RoleId = 5L,
                            UserId = 1L
                        });
                });
#pragma warning restore 612, 618
        }
    }
}

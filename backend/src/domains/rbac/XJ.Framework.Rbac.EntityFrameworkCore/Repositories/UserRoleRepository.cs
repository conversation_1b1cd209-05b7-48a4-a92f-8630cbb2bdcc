using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.EntityFrameworkCore.Repositories;
using XJ.Framework.Rbac.Domain.Entities;
using XJ.Framework.Rbac.Domain.Repositories;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>
/// UserRole 仓储实现
/// </summary>
public class UserRoleRepository : BaseAuditRepository<RbacDbContext, long, UserRoleEntity>, IUserRoleRepository
{
    private readonly IRoleRepository _roleRepository;

    public UserRoleRepository(IServiceProvider serviceProvider, IRoleRepository roleRepository) : base(serviceProvider)
    {
        _roleRepository = roleRepository;
    }

    /// <summary>
    /// 获取用户的所有角色ID列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>角色ID列表</returns>
    public async Task<List<long>> GetUserRoleIdsAsync(long userId)
    {
        var userRoles = await GetListAsync(ur => ur.UserId == userId);
        return userRoles.Select(ur => ur.RoleId).ToList();
    }

    /// <summary>
    /// 获取用户的所有角色代码列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>角色代码列表</returns>
    public async Task<List<string>> GetUserRoleCodesAsync(long userId)
    {
        var query = await LoadAsync(ur => ur.UserId == userId);
        return await (from ur in query
                join r in DbContext.Set<RoleEntity>() on ur.RoleId equals r.Key
                where r.Status == CommonStatus.Enabled && !r.Deleted && !ur.Deleted
                select r.Code)
            .ToListAsync();
    }

    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleId">角色ID</param>
    /// <returns>是否具有该角色</returns>
    public async Task<bool> HasRoleAsync(long userId, long roleId)
    {
        return await AnyAsync(ur => ur.UserId == userId && ur.RoleId == roleId);
    }


    /// <summary>
    /// 检查用户是否具有指定角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleCode">角色code</param>
    /// <returns>是否具有该角色</returns>
    public async Task<bool> HasRoleAsync(long userId, string roleCode)
    {
        var query = await LoadAsync(ur => ur.UserId == userId);

        return await (from ur in query
                join r in DbContext.Set<RoleEntity>() on ur.RoleId equals r.Key
                where r.Code == roleCode && r.Status == CommonStatus.Enabled && !r.Deleted && !ur.Deleted
                select r.Code)
            .AnyAsync();
    }

    /// <summary>
    /// 批量分配角色给用户
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleIds">角色ID列表</param>
    /// <returns>分配结果</returns>
    public async Task<bool> AssignRolesAsync(long userId, IEnumerable<long> roleIds)
    {
        // 获取已存在的角色关系
        var existingRoleIds = await GetUserRoleIdsAsync(userId);

        // 需要新增的角色ID
        var roleIdsToAdd = roleIds.Except(existingRoleIds);

        // 创建新的用户角色关系
        var userRoles = roleIdsToAdd.Select(roleId => new UserRoleEntity
        {
            Key = IdGenerator.NextId(),
            UserId = userId,
            RoleId = roleId
        }).ToList();

        return await InsertAsync(userRoles);
    }

    /// <summary>
    /// 移除用户的角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="roleIds">要移除的角色ID列表</param>
    /// <returns>移除结果</returns>
    public async Task<bool> RemoveRolesAsync(long userId, IEnumerable<long> roleIds)
    {
        var userRoles = (await GetListAsync(ur => ur.UserId == userId && roleIds.Contains(ur.RoleId))).ToList();
        return await DeleteAsync(userRoles);
    }

    /// <summary>
    /// 移除用户的所有角色
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>移除结果</returns>
    public async Task<bool> RemoveAllRolesAsync(long userId)
    {
        var userRoles = (await GetListAsync(ur => ur.UserId == userId)).ToList();
        return await DeleteAsync(userRoles);
    }

    /// <summary>
    /// 获取角色下的所有用户ID列表
    /// </summary>
    /// <param name="roleId">角色ID</param>
    /// <returns>用户ID列表</returns>
    public async Task<List<long>> GetRoleUserIdsAsync(long roleId)
    {
        var userRoles = await GetListAsync(ur => ur.RoleId == roleId);
        return userRoles.Select(ur => ur.UserId).ToList();
    }

    /// <summary>
    /// 获取角色下的所有用户ID列表
    /// </summary>
    /// <param name="roleCode">角色code</param>
    /// <param name="roleTypes">角色类型</param>
    /// <returns>用户ID列表</returns>
    public async Task<List<long>> GetRoleUserIdsAsync(string roleCode, params RoleType[] roleTypes)
    {
        Expression<Func<RoleEntity, bool>> expr = q => q.Code.ToLower().Equals(roleCode.ToLower());
        if (roleTypes.Length > 0)
        {
            expr = expr.And(q => roleTypes.Contains(q.Type));
        }

        var role = await _roleRepository.GetAsync(expr);
        role.NullCheck();
        var userRoles = await GetListAsync(ur => ur.RoleId == role!.Key);
        return userRoles.Select(ur => ur.UserId).ToList();
    }

    /// <summary>
    /// 获取多个用户的角色ID列表
    /// </summary>
    /// <param name="userIds">用户ID列表</param>
    /// <returns>用户角色ID字典，key为用户ID，value为该用户的角色ID列表</returns>
    public async Task<Dictionary<long, List<long>>> GetUsersRoleIdsAsync(IEnumerable<long> userIds)
    {
        var userRoles = await GetListAsync(ur => userIds.Contains(ur.UserId));
        return userRoles
            .GroupBy(ur => ur.UserId)
            .ToDictionary(
                g => g.Key,
                g => g.Select(ur => ur.RoleId).ToList()
            );
    }
}
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;

namespace XJ.Framework.Rbac.EntityFrameworkCore;

public class RbacDbContext : BaseDbContext
{
    public RbacDbContext(DbContextOptions options, IConfiguration configuration,
        IOptions<DatabaseOption> databaseOptions) : base(options, databaseOptions)
    {
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder.UseSqlServer(DatabaseOptions.Value["Rbac"]!.ConnectionString, (option) =>
        {
            
        });
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}
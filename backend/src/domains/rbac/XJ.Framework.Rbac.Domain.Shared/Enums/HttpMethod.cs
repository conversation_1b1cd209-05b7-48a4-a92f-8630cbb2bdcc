namespace XJ.Framework.Rbac.Domain.Shared.Enums;

/// <summary>
/// HTTP请求方法
/// </summary>
public enum HttpMethod
{
    /// <summary>
    /// GET请求
    /// </summary>
    GET = 1,

    /// <summary>
    /// POST请求
    /// </summary>
    POST = 2,

    /// <summary>
    /// PUT请求
    /// </summary>
    PUT = 3,

    /// <summary>
    /// DELETE请求
    /// </summary>
    DELETE = 4,

    /// <summary>
    /// PATCH请求
    /// </summary>
    PATCH = 5,

    /// <summary>
    /// HEAD请求
    /// </summary>
    HEAD = 6,

    /// <summary>
    /// OPTIONS请求
    /// </summary>
    OPTIONS = 7
}
using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 验证码请求DTO
/// </summary>
public class CaptchaRequestDto
{
    /// <summary>
    /// 验证码类型
    /// </summary>
    [Required(ErrorMessage = "验证码类型不能为空")]
    public string Type { get; set; } = null!;

    /// <summary>
    /// 业务场景（例如：login, register, resetPassword等）
    /// </summary>
    [Required(ErrorMessage = "业务场景不能为空")]
    public string Scene { get; set; } = null!;
} 
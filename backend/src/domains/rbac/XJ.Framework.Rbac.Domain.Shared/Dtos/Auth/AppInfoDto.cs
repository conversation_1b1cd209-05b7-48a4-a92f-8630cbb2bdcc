namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 应用系统信息DTO
/// </summary>
public class AppInfoDto
{
    /// <summary>
    /// 应用系统ID
    /// </summary>
    public long Id { get; set; }

    /// <summary>
    /// 应用系统编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 应用系统名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 应用系统描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 应用系统首页地址
    /// </summary>
    public string? HomeUrl { get; set; }

    /// <summary>
    /// 应用系统图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 排序
    /// </summary>
    public int Sort { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    public int Status { get; set; }
} 
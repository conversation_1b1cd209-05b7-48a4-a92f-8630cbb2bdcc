using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 登录请求DTO
/// </summary>
public class LoginRequestDto
{
    /// <summary>
    /// 用户名
    /// </summary>
    [Required(ErrorMessage = "用户名不能为空")]
    public string Username { get; set; } = null!;

    /// <summary>
    /// 密码
    /// </summary>
    [Required(ErrorMessage = "密码不能为空")]
    public string Password { get; set; } = null!;

    /// <summary>
    /// 客户端IP
    /// </summary>
    public string? ClientIp { get; set; }

    /// <summary>
    /// 设备ID
    /// </summary>
    public string? DeviceId { get; set; }
    /// <summary>
    /// 设备信息
    /// </summary>
    public string? DeviceInfo { get; set; }

    /// <summary>
    /// 验证码ID
    /// </summary>
    public string CaptchaId { get; set; } = null!;

    /// <summary>
    /// 验证码
    /// </summary>
    public string CaptchaCode { get; set; } = null!;

    // /// <summary>
    // /// 应用ID
    // /// </summary>
    // public string? AppId { get; set; }

    /// <summary>
    /// 应用编码
    /// </summary>
    public string? AppCode { get; set; }
} 
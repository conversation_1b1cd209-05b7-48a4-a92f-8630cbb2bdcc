using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

public class DataPermissionRule
{
    /// <summary>
    /// 实体类型全名
    /// </summary>
    public string EntityTypeName { get; set; } = string.Empty;

    /// <summary>
    /// 规则类型
    /// </summary>
    public DataPermissionRuleType RuleType { get; set; }

    /// <summary>
    /// 规则值
    /// </summary>
    public string RuleValue { get; set; } = string.Empty;
}
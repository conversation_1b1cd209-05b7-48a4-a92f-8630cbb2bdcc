using System;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 活跃令牌DTO
/// </summary>
public class ActiveTokenDto
{
    /// <summary>
    /// 令牌ID
    /// </summary>
    public string Id { get; set; } = null!;

    /// <summary>
    /// 令牌类型（access/refresh）
    /// </summary>
    public string TokenType { get; set; } = null!;

    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 客户端IP
    /// </summary>
    public string? ClientIp { get; set; }

    /// <summary>
    /// 设备信息
    /// </summary>
    public string? DeviceInfo { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreatedAt { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    public DateTimeOffset ExpiresAt { get; set; }

    /// <summary>
    /// 最后使用时间
    /// </summary>
    public DateTimeOffset? LastUsedAt { get; set; }

    /// <summary>
    /// 是否已撤销
    /// </summary>
    public bool IsRevoked { get; set; }

    /// <summary>
    /// 撤销时间
    /// </summary>
    public DateTimeOffset? RevokedAt { get; set; }

    /// <summary>
    /// 撤销原因
    /// </summary>
    public string? RevokeReason { get; set; }
} 
namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

public class UpdateProfileRequestDto
{
    public string? RealName { get; set; }

    public FileDto? Avatar { get; set; }

    // 可根据需要添加更多字段
    public string? Gender { get; set; }
    /// <summary>
    /// 国家
    /// </summary>
    public string? Country { get; set; }
    /// <summary>
    /// 注册单位名称
    /// </summary>
    public string? Unit { get; set; }
    /// <summary>
    /// 联系地址
    /// </summary>
    public string? ContactAddress { get; set; }
    /// <summary>
    /// 固定电话
    /// </summary>
    public string? Telephone { get; set; }
}
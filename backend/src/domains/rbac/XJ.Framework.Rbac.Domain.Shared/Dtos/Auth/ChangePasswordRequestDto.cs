using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 修改密码请求DTO
/// </summary>
public class ChangePasswordRequestDto
{
    // /// <summary>
    // /// 用户名
    // /// </summary>
    // [Required(ErrorMessage = "用户名不能为空")]
    // public string UserName { get; set; } = null!;
    
    /// <summary>
    /// 旧密码
    /// </summary>
    [Required(ErrorMessage = "旧密码不能为空")]
    public string OldPassword { get; set; } = null!;

    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "新密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string NewPassword { get; set; } = null!;

    /// <summary>
    /// 确认新密码
    /// </summary>
    [Required(ErrorMessage = "确认密码不能为空")]
    [Compare("NewPassword", ErrorMessage = "两次输入的密码不一致")]
    public string ConfirmNewPassword { get; set; } = null!;

    /// <summary>
    /// 验证码ID
    /// </summary>
    public string? CaptchaId { get; set; }

    /// <summary>
    /// 验证码
    /// </summary>
    public string? CaptchaCode { get; set; }
} 
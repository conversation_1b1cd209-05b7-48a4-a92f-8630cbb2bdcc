namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 令牌响应DTO
/// </summary>
public class TokenResponseDto
{
    /// <summary>
    /// 访问令牌
    /// </summary>
    public string AccessToken { get; set; } = null!;

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string RefreshToken { get; set; } = null!;

    /// <summary>
    /// 令牌类型
    /// </summary>
    public string TokenType { get; set; } = "Bearer";

    /// <summary>
    /// 过期时间(秒)
    /// </summary>
    public int ExpiresIn { get; set; }

    /// <summary>
    /// 范围
    /// </summary>
    public string? Scope { get; set; }
} 
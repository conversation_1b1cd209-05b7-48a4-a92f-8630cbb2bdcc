using System.Collections.Generic;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 登录结果
/// </summary>
public class LoginResultDto
{
    /// <summary>
    /// 访问令牌
    /// </summary>
    public string AccessToken { get; set; } = null!;

    /// <summary>
    /// 刷新令牌
    /// </summary>
    public string RefreshToken { get; set; } = null!;

    /// <summary>
    /// 令牌类型
    /// </summary>
    public string TokenType { get; set; } = "Bearer";

    /// <summary>
    /// 过期时间（秒）
    /// </summary>
    public int ExpiresIn { get; set; }

    /// <summary>
    /// 用户角色列表
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// 用户岗位编码
    /// </summary>
    public List<string> Positions { get; set; } = new();

    /// <summary>
    /// 用户菜单权限列表
    /// </summary>
    public List<string> MenuPermissions { get; set; } = new();

    /// <summary>
    /// 用户按钮权限列表
    /// </summary>
    public List<string> ButtonPermissions { get; set; } = new();

    /// <summary>
    /// 用户所属组织架构列表
    /// </summary>
    public List<string> Organizations { get; set; } = new();
}
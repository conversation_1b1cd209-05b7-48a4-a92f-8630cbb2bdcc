using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 重置密码请求DTO
/// </summary>
public class ResetPasswordRequestDto
{

    /// <summary>
    /// 用户名（通过手机验证码重置时使用）
    /// </summary>
    public string? Username { get; set; }

    // /// <summary>
    // /// 手机号（通过手机验证码重置时使用）
    // /// </summary>
    // public string? PhoneNumber { get; set; }
    //
    // /// <summary>
    // /// 邮箱（通过邮箱验证码重置时使用）
    // /// </summary>
    // public string? Email { get; set; }
    
    /// <summary>
    /// 重置方式（email/phone）
    /// </summary>
    [Required(ErrorMessage = "重置方式不能为空")]
    public string? VerifyMethod { get; set; }

    /// <summary>
    /// 重置密码使用的确认码
    /// </summary>
    public string? Code { get; set; }

    /// <summary>
    /// 新密码
    /// </summary>
    [Required(ErrorMessage = "新密码不能为空")]
    [StringLength(100, MinimumLength = 6, ErrorMessage = "密码长度必须在6-100个字符之间")]
    public string NewPassword { get; set; } = null!;

    /// <summary>
    /// 确认新密码
    /// </summary>
    [Required(ErrorMessage = "确认密码不能为空")]
    [Compare("NewPassword", ErrorMessage = "两次输入的密码不一致")]
    public string ConfirmNewPassword { get; set; } = null!;

}

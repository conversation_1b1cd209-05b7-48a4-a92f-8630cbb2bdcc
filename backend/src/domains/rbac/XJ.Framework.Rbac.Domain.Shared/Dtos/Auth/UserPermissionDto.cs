using System.Collections.Generic;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

/// <summary>
/// 用户权限DTO
/// </summary>
public class UserPermissionDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = null!;

    /// <summary>
    /// 角色列表
    /// </summary>
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// 权限代码列表
    /// </summary>
    public List<PermissionDto> Permissions { get; set; } = new();

    /// <summary>
    /// 按应用分组的权限
    /// </summary>
    public Dictionary<string, List<string>> AppPermissions { get; set; } = new();

    /// <summary>
    /// 可访问的应用列表
    /// </summary>
    public List<AppInfoDto> AccessibleApps { get; set; } = new();

    /// <summary>
    /// 数据权限规则列表
    /// </summary>
    public List<DataPermissionRuleDto> DataPermissionRules { get; set; } = new();
} 
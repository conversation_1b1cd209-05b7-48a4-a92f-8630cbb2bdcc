FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制解决方案文件
COPY ["Common.props", "./"]
COPY ["Common.Secrets.props", "./"]
COPY ["Directory.Build.props", "./"]
COPY ["Directory.Packages.props", "./"]

# 复制项目文件
COPY ["src/domains/rbac/XJ.Framework.Rbac.WebApi/XJ.Framework.Rbac.WebApi.csproj", "src/domains/rbac/XJ.Framework.Rbac.WebApi/"]
COPY ["src/domains/rbac/XJ.Framework.Rbac.Application/XJ.Framework.Rbac.Application.csproj", "src/domains/rbac/XJ.Framework.Rbac.Application/"]
COPY ["src/domains/rbac/XJ.Framework.Rbac.Domain.Shared/XJ.Framework.Rbac.Domain.Shared.csproj", "src/domains/rbac/XJ.Framework.Rbac.Domain.Shared/"]
COPY ["src/shared/XJ.Framework.Library.WebApi/XJ.Framework.Library.WebApi.csproj", "src/shared/XJ.Framework.Library.WebApi/"]
COPY ["src/shared/XJ.Framework.Library.Application.Contract/XJ.Framework.Library.Application.Contract.csproj", "src/shared/XJ.Framework.Library.Application.Contract/"]
COPY ["src/shared/XJ.Framework.Library.Domain.Shared/XJ.Framework.Library.Domain.Shared.csproj", "src/shared/XJ.Framework.Library.Domain.Shared/"]
COPY ["src/core/impls/XJ.Framework.Library.Image/XJ.Framework.Library.Image.csproj", "src/core/impls/XJ.Framework.Library.Image/"]

# 复制配置文件
COPY ["settings/", "settings/"]

# 复制所有源代码
COPY ["src/", "src/"]

# 还原和构建
RUN dotnet restore "src/domains/rbac/XJ.Framework.Rbac.WebApi/XJ.Framework.Rbac.WebApi.csproj"
RUN dotnet build "src/domains/rbac/XJ.Framework.Rbac.WebApi/XJ.Framework.Rbac.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "src/domains/rbac/XJ.Framework.Rbac.WebApi/XJ.Framework.Rbac.WebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
# 复制配置文件到最终镜像
COPY --from=build /src/settings /app/settings
ENTRYPOINT ["dotnet", "XJ.Framework.Rbac.WebApi.dll"]
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Library.WebApi.Options;

namespace XJ.Framework.Rbac.WebApi.Controllers;

[ApiController]
[Route("[controller]")]
[AllowAnonymous]
public class AppController : ControllerBase
{
    private readonly IOptions<ApplicationOption> _applicationOptions;
    private readonly IOptions<DatabaseOption> _databaseOptions;
    private readonly ILogger<AppController> _logger;

    public AppController(IOptions<ApplicationOption> applicationOptions, IOptions<DatabaseOption> databaseOptions,
        ILogger<AppController> logger)
    {
        _applicationOptions = applicationOptions;
        _databaseOptions = databaseOptions;
        _logger = logger;
    }


    [HttpGet]
    public async Task<string> GetSettingAsync()
    {
        var appSettings = _applicationOptions.Value;
        var dbSettings = _databaseOptions.Value;
        var connectionStrings = string.Join(System.Environment.NewLine,
            dbSettings.Select(x => $"{x.Name}: {x.ConnectionString.Substring(0, 10)}"));


        return await Task.FromResult($"appCode:{appSettings.ApplicationCode},connectionStrings:{connectionStrings}");
    }
}
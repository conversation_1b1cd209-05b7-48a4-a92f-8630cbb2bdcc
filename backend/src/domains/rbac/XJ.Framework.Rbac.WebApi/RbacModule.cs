using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Modularity.Interfaces;
using XJ.Framework.Rbac.Application.Services;

namespace XJ.Framework.Rbac.WebApi;

/// <summary>
/// RBAC模块
/// </summary>
public class RbacModule : ModuleBase
{
    public override string ModuleName => "Rbac";
    public override string Version => "1.0.0";
    public override string Description => "Role-Based Access Control module";
    public override bool IsCore => true;

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 初始化RBAC应用层和基础设施层
        services.InitApplication<RbacApplicationWrapper, RbacInfrastructureWrapper>(configuration);
        
        // 注册RBAC特定的服务
        services.AddScoped<IUserService, UserService>();
        services.AddScoped<IRoleService, RoleService>();
        services.AddScoped<IPermissionService, PermissionService>();
        
        // 配置JWT
        services.Configure<JwtOptions>(configuration.GetSection("Jwt"));
    }

    public override void Configure(IApplicationBuilder app)
    {
        // RBAC模块特定的中间件配置
        // app.UseAuthentication();
        // app.UseAuthorization();
    }

    public override Type[] GetProvidedServices()
    {
        return new[]
        {
            typeof(IUserService),
            typeof(IRoleService),
            typeof(IPermissionService)
        };
    }
}

/// <summary>
/// 消息模块
/// </summary>
public class MessagingModule : ModuleBase
{
    public override string ModuleName => "Messaging";
    public override string Version => "1.0.0";
    public override string Description => "Messaging and notification module";
    public override string[] Dependencies => new[] { "Rbac" };

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 初始化消息应用层和基础设施层
        services.InitApplication<MessagingApplicationWrapper, MessagingInfrastructureWrapper>(configuration);
        
        // 注册消息服务
        services.AddScoped<IMessageSendService, MessageSendService>();
        services.AddScoped<IMessageTemplateService, MessageTemplateService>();
        
        // 注册后台服务
        services.AddHostedService<MessageSendWorker>();
    }

    public override Type[] GetProvidedServices()
    {
        return new[]
        {
            typeof(IMessageSendService),
            typeof(IMessageTemplateService)
        };
    }
}

/// <summary>
/// 动态表单模块
/// </summary>
public class DynamicFormModule : ModuleBase
{
    public override string ModuleName => "DynamicForm";
    public override string Version => "1.0.0";
    public override string Description => "Dynamic form management module";
    public override string[] Dependencies => new[] { "Rbac" };

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 初始化动态表单应用层和基础设施层
        services.InitApplication<DynamicFormApplicationWrapper, DynamicFormInfrastructureWrapper>(configuration);
        
        // 注册动态表单服务
        services.AddScoped<IFormService, FormService>();
        services.AddScoped<IFormInstanceService, FormInstanceService>();
    }

    public override Type[] GetProvidedServices()
    {
        return new[]
        {
            typeof(IFormService),
            typeof(IFormInstanceService)
        };
    }
}

/// <summary>
/// 文件模块
/// </summary>
public class FilesModule : ModuleBase
{
    public override string ModuleName => "Files";
    public override string Version => "1.0.0";
    public override string Description => "File management module";
    public override string[] Dependencies => new[] { "Rbac" };

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 初始化文件应用层和基础设施层
        services.InitApplication<FilesApplicationWrapper, FilesInfrastructureWrapper>(configuration);
        
        // 注册文件服务
        services.AddScoped<IFileInfoService, FileInfoService>();
    }

    public override Type[] GetProvidedServices()
    {
        return new[]
        {
            typeof(IFileInfoService)
        };
    }
}

/// <summary>
/// 业务模块 (Itmctr)
/// </summary>
public class ItmctrModule : ModuleBase
{
    public override string ModuleName => "Itmctr";
    public override string Version => "1.0.0";
    public override string Description => "Business logic module for ITMCTR";
    public override string[] Dependencies => new[] { "Rbac", "DynamicForm", "Files" };

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        // 初始化业务应用层和基础设施层
        services.InitApplication<ItmctrApplicationWrapper, ItmctrInfrastructureWrapper>(configuration);
        
        // 注册业务服务
        services.AddScoped<IProjectService, ProjectService>();
        services.AddScoped<IProjectHistoryService, ProjectHistoryService>();
        
        // 注册后台服务
        services.AddHostedService<ProcessProjectToResultService>();
        services.AddHostedService<ProcessExtractProjectConvertService>();
        services.AddHostedService<ProcessSubmitContentCompareService>();
    }

    public override Type[] GetProvidedServices()
    {
        return new[]
        {
            typeof(IProjectService),
            typeof(IProjectHistoryService)
        };
    }
}

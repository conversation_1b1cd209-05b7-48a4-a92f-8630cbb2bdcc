using Microsoft.AspNetCore.Mvc;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Rbac.ApiClient;

public class UserApiClient : BaseApiClient
{
    private readonly string _baseUrl;

    public UserApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption, ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        IOptions<JsonOptions> jsonOptions)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext, jsonOptions)
    {
        _baseUrl = endpointOption.Value["Rbac"]!.Url.TrimEnd('/');
    }

    public async Task<UserProfileDto> GetUserInfoAsync()
    {
        return await InternalGetAsync<UserProfileDto>($"{_baseUrl}/user/user-info");
    }

    public async Task<bool> ValidatePermissionAsync(PermissionType permissionType, string permissionCode,
        HttpMethod? httpMethod, string? path, string? appCode)
    {
        var url = $"{_baseUrl}/permission/validate-permission";

        url += $"?permissionType={permissionType}&permissionCode={permissionCode}";

        if (httpMethod != null)
        {
            url += $"&httpMethod={httpMethod}";
        }

        if (path != null)
        {
            url += $"&path={path}";
        }

        if (appCode != null)
        {
            url += $"&appCode={appCode}";
        }

        return await InternalGetAsync<bool>(url);
    }

    public async Task<List<PositionDto>> GetUserPositionsAsync()
    {
        var url = $"{_baseUrl}/User/user-positions";

        return await InternalGetAsync<List<PositionDto>>(url);
    }

    public async Task<List<string>> GetUserRolesAsync()
    {
        var url = $"{_baseUrl}/User/user-roles";

        return await InternalGetAsync<List<string>>(url);
    }
}

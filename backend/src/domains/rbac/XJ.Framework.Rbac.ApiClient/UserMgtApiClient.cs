using Microsoft.AspNetCore.Mvc;
using System.Text;
using XJ.Framework.Library.Application.Contract.Examples;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Rbac.Application.Contract.OperationDtos;
using XJ.Framework.Rbac.Application.Contract.QueryCriteria;
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Rbac.ApiClient;

public class UserMgtApiClient : BaseApiClient
{
    private readonly string _baseUrl;

    public UserMgtApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption,
        ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        IOptions<JsonOptions> jsonOptions)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext, jsonOptions)
    {
        _baseUrl = endpointOption.Value["RbacMgt"]!.Url.TrimEnd('/');
    }

    public async Task<Dictionary<string, long>> BatchCreateAsync(List<UserCreateOperationDto> users)
    {
        return await InternalPostAsync<Dictionary<string, long>>($"{_baseUrl}/User/batch-create", users);
    }

    public async Task<IEnumerable<UserDto>> GetListAsync(UserQueryCriteria criteria)
    {
        var baseUrl = $"{_baseUrl}/User";
        var nvc = BuildCriteriaNameValueCollection(criteria);
        baseUrl += "?" + nvc.ToUrlParameters(encodeUrl: false, encoding: Encoding.UTF8);
        return await InternalGetAsync<IEnumerable<UserDto>>(baseUrl);
    }

    public async Task<List<UserDto>> GetUsersByCommonRoleCodeAsync(string roleCode)
    {
        return await InternalGetAsync<List<UserDto>>($"{_baseUrl}/user/role-in-common/{roleCode}");
    }

    public async Task<List<OrganizationUserDto>> GetManagedPositionUsersAsync(string positionCode)
    {
        return await InternalGetAsync<List<OrganizationUserDto>>(
            $"{_baseUrl}/user/position-in-user-managed/{positionCode}");
    }
}

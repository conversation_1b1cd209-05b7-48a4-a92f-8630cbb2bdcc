using Microsoft.AspNetCore.Mvc;
using System.Text;
using XJ.Framework.Library.Application.Contract.Examples;
using XJ.Framework.Library.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Rbac.Application.Contract.OperationDtos;
using XJ.Framework.Rbac.Application.Contract.QueryCriteria;
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Rbac.ApiClient;

public class UserMgtApplicationApiClient : BaseApplicationApiClient
{
    private readonly string _baseUrl;


    public UserMgtApplicationApiClient(HttpClient httpClient, ILogger<BaseApiClient> logger,
        IAuthInfoGetter authInfoGetter, IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        ICurrentApplicationContext applicationContext, IOptions<JsonOptions> jsonOptions,
        IOptions<EndpointOption> endpointOption)
        : base(httpClient, logger, authInfoGetter, contextContainer, currentUserContext, applicationContext,
            jsonOptions)
    {
        _baseUrl = endpointOption.Value["RbacMgt"]!.Url.TrimEnd('/');
    }


    public async Task<List<UserDto>> GetAppUsersByCommonRoleCodeAsync(string roleCode)
    {
        return await InternalGetAsync<List<UserDto>>($"{_baseUrl}/user/app/role-in-common/{roleCode}");
    }

}

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// UserOrganization 服务接口
/// </summary>
public interface IUserOrganizationService :
    IAppService<long, UserOrganizationDto, UserOrganizationQueryCriteria>,
    IEditableAppService<long, UserOrganizationOperationDto>
{
    Task<bool> SetUserOrganizations(long userId, List<UserOrganizationOperationDto> userOrganizations);
    Task<List<string>> GetUserOrganizationCodesAsync(long userId);
}
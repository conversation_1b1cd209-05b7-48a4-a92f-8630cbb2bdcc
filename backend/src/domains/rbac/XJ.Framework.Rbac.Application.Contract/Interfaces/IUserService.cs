using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// User 服务接口
/// </summary>
public interface IUserService :
    IAppService<long, UserDto, UserQueryCriteria>,
    IEditableAppService<long, UserOperationDto>
{
    Task<long> CreateUserAsync(UserOperationDto dto);

    Task<long> CreateUserAsync(UserCreateOperationDto dto);

    /// <summary>
    /// 检查用户名是否可用
    /// </summary>
    /// <param name="username">用户名</param>
    /// <returns>是否可用</returns>
    Task<bool> IsUsernameAvailableAsync(string username);

    /// <summary>
    /// 检查邮箱是否可用
    /// </summary>
    /// <param name="email">邮箱</param>
    /// <returns>是否可用</returns>
    Task<bool> IsEmailAvailableAsync(string email);

    /// <summary>
    /// 检查手机号是否可用
    /// </summary>
    /// <param name="phoneNumber">手机号</param>
    /// <returns>是否可用</returns>
    Task<bool> IsPhoneNumberAvailableAsync(string phoneNumber);

    Task<List<UserDto>> GetUsersByRoleCodeAsync(string roleCode, params RoleType[] roleTypes);
    Task<List<UserDto>> GetUsersByPositionCodeAsync(string positionCode, string organizationCode);
    Task<List<OrganizationUserDto>> GetManagedPositionUsersAsync(string positionCode, long userId);

    Task<List<UserDto>> GetUsersByPositionCodeAsync(string positionCode);
    Task<bool> Register(RegisterOperationDto dto);
    Task<string> ResetPasswordAsync(long id);
}

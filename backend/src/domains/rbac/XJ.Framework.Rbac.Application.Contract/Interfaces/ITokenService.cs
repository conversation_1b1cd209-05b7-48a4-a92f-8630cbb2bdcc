using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// Token 服务接口
/// </summary>
public interface ITokenService
{
    /// <summary>
    /// 生成访问令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="username">用户名</param>
    /// <param name="clientIp">客户端ip</param>
    /// <param name="deviceId">设备id</param>
    /// <param name="deviceInfo">设备信息</param>
    /// <returns>访问令牌</returns>
    Task<string> GenerateAccessTokenAsync(long userId, string username, string clientIp, string deviceId,
        string deviceInfo
        // , IEnumerable<string> roles, IEnumerable<string> permissions
    );

    /// <summary>
    /// 生成刷新令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="clientIp">客户端IP</param>
    /// <param name="deviceId">设备Id</param>
    /// <param name="deviceInfo">设备信息</param>
    /// <returns>刷新令牌</returns>
    Task<string> GenerateRefreshTokenAsync(long userId, string clientIp, string deviceId, string deviceInfo);

    /// <summary>
    /// 验证访问令牌
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <returns>验证结果</returns>
    Task<TokenValidationResultDto> ValidateAccessTokenAsync(string token);

    /// <summary>
    /// 验证刷新令牌
    /// </summary>
    /// <param name="token">刷新令牌</param>
    /// <param name="deviceId">设备id</param>
    /// <returns>验证结果</returns>
    Task<TokenValidationResultDto> ValidateRefreshTokenAsync(string token, string deviceId);

    /// <summary>
    /// 撤销访问令牌
    /// </summary>
    /// <param name="token">访问令牌</param>
    /// <returns>撤销结果</returns>
    Task<bool> RevokeAccessTokenAsync(string token);

    /// <summary>
    /// 撤销刷新令牌
    /// </summary>
    /// <param name="token">刷新令牌</param>
    /// <returns>撤销结果</returns>
    Task<bool> RevokeRefreshTokenAsync(string token);

    /// <summary>
    /// 获取用户活跃令牌列表
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>活跃令牌列表</returns>
    Task<List<ActiveTokenDto>> GetUserActiveTokensAsync(long userId);

    /// <summary>
    /// 撤销用户所有令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <returns>撤销结果</returns>
    Task<bool> RevokeAllUserTokensAsync(long userId);

    /// <summary>
    /// 清理过期令牌
    /// </summary>
    /// <returns>清理结果</returns>
    Task<bool> CleanExpiredTokensAsync();

    /// <summary>
    /// 刷新访问令牌
    /// </summary>
    /// <param name="refreshToken">刷新令牌</param>
    /// <param name="deviceId">设备id</param>
    /// <returns>新的令牌响应</returns>
    Task<TokenResponseDto> RefreshTokenAsync(string refreshToken, string deviceId);

    /// <summary>
    /// 生成邮箱验证令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="minutes">有效期（分钟）</param>
    /// <returns>验证令牌</returns>
    Task<string> GenerateEmailTokenAsync(long userId, int minutes);

    /// <summary>
    /// 验证邮箱验证令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="token">验证令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateEmailTokenAsync(long userId, string token);

    /// <summary>
    /// 生成手机验证令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="minutes">有效期（分钟）</param>
    /// <returns>6位数字验证码</returns>
    Task<string> GenerateMobileTokenAsync(long userId, int minutes);

    /// <summary>
    /// 验证手机验证令牌
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="token">验证令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateMobileTokenAsync(long userId, string token);

    /// <summary>
    /// 根据访问令牌获取令牌信息
    /// </summary>
    /// <param name="accessToken">访问令牌</param>
    /// <returns>令牌信息</returns>
    Task<TokenDto?> GetTokenByAccessTokenAsync(string accessToken);
}

using XJ.Framework.Rbac.Domain.Entities;

namespace XJ.Framework.Rbac.Application.Contract.Interfaces;

/// <summary>
/// UserPosition 服务接口
/// </summary>
public interface IUserPositionService :
    IAppService<long, UserPositionDto, UserPositionQueryCriteria>,
    IEditableAppService<long, UserPositionOperationDto>
{
    Task<bool> SetUserPositions(long userId, List<UserPositionOperationDto> userPositions);
    Task<List<long>> GetPositionUsersAsync(string positionCode, string organizationCode);
    Task<List<long>> GetPositionUsersAsync(string positionCode);
    Task<List<PositionDto>> GetUserPositionsAsync();
    Task<List<PositionDto>> GetUserPositionsAsync(long userId);
}
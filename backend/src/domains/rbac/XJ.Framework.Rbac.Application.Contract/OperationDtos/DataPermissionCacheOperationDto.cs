namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// DataPermissionCache 操作 DTO
/// </summary>
public class DataPermissionCacheOperationDto : BaseOperationDto
{
    /// <summary>
    /// 用户ID（关联用户表的主键）
    /// </summary>
    public long UserId { get; set; }

    /// <summary>
    /// 角色ID（关联角色表的主键）
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// 资源类型（需要进行数据权限控制的资源类型名称）
    /// </summary>
    public string ResourceType { get; set; } = null!;

    /// <summary>
    /// 权限范围（用户在该资源类型下的数据访问范围，通常是一个JSON字符串，描述具体的访问规则）
    /// </summary>
    public string? PermissionScope { get; set; }

    /// <summary>
    /// 缓存时间（数据权限规则被缓存的时间点）
    /// </summary>
    public DateTimeOffset CacheTime { get; set; }

    /// <summary>
    /// 过期时间（缓存的过期时间点，超过这个时间需要重新计算权限规则）
    /// </summary>
    public DateTimeOffset ExpireTime { get; set; }

} 

using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// Position 操作 DTO
/// </summary>
public class PositionOperationDto : BaseOperationDto
{
    /// <summary>
    /// 角色id
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// 岗位编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 岗位名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 状态（1-启用，0-禁用）
    /// </summary>
    public CommonStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

} 
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using HttpMethod = XJ.Framework.Rbac.Domain.Shared.Enums.HttpMethod;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// Permission 操作 DTO
/// </summary>
public class PermissionOperationDto : BaseOperationDto
{
    /// <summary>
    /// 应用ID
    /// </summary>
    /// <remarks>
    /// 标识权限所属的应用系统
    /// 为null时表示是公共权限
    /// </remarks>
    public string? AppId { get; set; }

    /// <summary>
    /// 应用编码
    /// </summary>
    /// <remarks>
    /// 应用系统的唯一标识符
    /// 为null时表示是公共权限
    /// </remarks>
    public string? AppCode { get; set; }
    /// <summary>
    /// 父级权限ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 权限编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 权限名称
    /// </summary>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 权限类型：1-目录，2-菜单，3-按钮，4-API接口
    /// </summary>
    public PermissionType Type { get; set; }

    /// <summary>
    /// 路由路径/接口路径
    /// </summary>
    public string? Path { get; set; }

    /// <summary>
    /// 前端组件路径
    /// </summary>
    public string? Component { get; set; }

    /// <summary>
    /// 重定向地址
    /// </summary>
    public string? Redirect { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// HTTP方法：GET, POST, PUT, DELETE等
    /// </summary>
    public HttpMethod? Method { get; set; }

    /// <summary>
    /// 排序号
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 状态：1-启用，0-禁用
    /// </summary>
    public CommonStatus Status { get; set; }

    /// <summary>
    /// 权限描述
    /// </summary>
    public string? Description { get; set; }
}
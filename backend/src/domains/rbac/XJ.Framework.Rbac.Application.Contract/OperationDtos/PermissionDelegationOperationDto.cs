namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// PermissionDelegation 操作 DTO
/// </summary>
public class PermissionDelegationOperationDto : BaseOperationDto
{
    /// <summary>
    /// 委托人ID（发起权限委托的用户ID）
    /// </summary>
    public long DelegatorId { get; set; }

    /// <summary>
    /// 被委托人ID（接受权限委托的用户ID）
    /// </summary>
    public long DelegateeId { get; set; }

    /// <summary>
    /// 委托角色ID（被委托的角色ID，表示委托该角色的所有权限）
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// 组织ID（可选，委托权限仅在该组织范围内有效）
    /// </summary>
    public long? OrganizationId { get; set; }

    /// <summary>
    /// 岗位ID（可选，委托权限仅在该岗位范围内有效）
    /// </summary>
    public long? PositionId { get; set; }

    /// <summary>
    /// 委托开始时间（权限委托的生效时间）
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 委托结束时间（权限委托的失效时间）
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 状态：1-生效，0-失效
    /// </summary>
    public byte Status { get; set; }

    /// <summary>
    /// 备注说明（关于此次权限委托的补充说明信息）
    /// </summary>
    public string? Remark { get; set; }

} 

using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// Organization 操作 DTO
/// </summary>
public class OrganizationOperationDto : BaseOperationDto
{
    /// <summary>
    /// 父级组织ID
    /// </summary>
    public long? ParentId { get; set; }

    /// <summary>
    /// 组织编码
    /// </summary>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 组织名称
    /// </summary>
    public string Name { get; set; } = null!;
    public string NamePath { get; set; } = null!;

    /// <summary>
    /// 组织层级
    /// </summary>
    public int Level { get; set; }

    /// <summary>
    /// 组织路径（格式：/1/2/3/）
    /// </summary>
    public string Path { get; set; } = null!;

    /// <summary>
    /// 同级排序
    /// </summary>
    public int SortOrder { get; set; }

    /// <summary>
    /// 状态（1-启用，0-禁用）
    /// </summary>
    public CommonStatus Status { get; set; }

    /// <summary>
    /// 描述
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// 组织类型（1-内部组织，2-外部组织）
    /// </summary>
    public OrganizationType OrgType { get; set; }

} 
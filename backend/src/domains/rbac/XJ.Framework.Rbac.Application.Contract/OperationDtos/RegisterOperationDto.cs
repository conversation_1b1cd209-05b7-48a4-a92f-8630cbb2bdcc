using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos
{
    public class RegisterOperationDto
    {
        /// <summary>
        /// 用户名
        /// </summary>
        public string Username { get; set; } = null!;
        /// <summary>
        /// 密码
        /// </summary>
        public string Password { get; set; } = null!;
        /// <summary>
        /// 确认密码
        /// </summary>
        public string RePassword { get; set; } = null!;
        /// <summary>
        /// 邮箱
        /// </summary>
        public string Email { get; set; } = null!;
        /// <summary>
        /// 姓名
        /// </summary>
        public string RealName { get; set; } = null!;
        /// <summary>
        /// 性别
        /// </summary>
        public string Gender { get; set; } = null!;
        /// <summary>
        /// 国家
        /// </summary>
        public string Country { get; set; } = null!;
        /// <summary>
        /// 注册单位名称
        /// </summary>
        public string Unit { get; set; } = null!;
        /// <summary>
        /// 联系地址
        /// </summary>
        public string ContactAddress { get; set; } = null!;
        /// <summary>
        /// 手机号
        /// </summary>
        public string Mobile { get; set; } = null!;
        /// <summary>
        /// 固定电话
        /// </summary>
        public string Telephone { get; set; } = null!;

        public string CaptchaId { get; set; } = null!;
        
        public string CaptchaCode { get; set; } = null!;
    }
}

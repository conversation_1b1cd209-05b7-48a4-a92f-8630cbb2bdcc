using XJ.Framework.Messaging.WebApi.Mgt.Workers;

namespace XJ.Framework.Messaging.WebApi;

public class MessagingWebApiMgtWrapper : WebApiMgtWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<MessagingApplicationWrapper, MessagingInfrastructureWrapper>(configuration);
        services.AddHttpClient<UserApiClient>();

        // 注册后台Worker
        services.AddHostedService<MessageSendWorker>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }
    public override void AddFilters(MvcOptions mvcOptions)
    {

    }
}

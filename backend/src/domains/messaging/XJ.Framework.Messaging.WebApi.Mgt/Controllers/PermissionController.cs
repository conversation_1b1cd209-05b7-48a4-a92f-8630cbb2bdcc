using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Logging.WebApi.Messaging.Controllers;

[ApiController]
[Route("[controller]")]
public class PermissionController : ControllerBase
{
    private readonly ApiExplorer _apiExplorer;

    public PermissionController(ApiExplorer apiExplorer)
    {
        _apiExplorer = apiExplorer;
    }

    /// <summary>
    /// 获取所有API终结点
    /// </summary>
    /// <returns></returns>
    [HttpGet("api-permissions")]
    [RequirePermission("api-permissions")]
    public async Task<IEnumerable<ApiEndpointInfo>> GetApiPermissionsAsync()
    {
        return await Task.FromResult(_apiExplorer.GetAllApiEndpoints());
    }
}

using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.Infrastructure.ApiClient.Base;
using XJ.Framework.Library.Infrastructure.ApiClient.Options;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.ApiClient;

public class MessagingApplicationApiClient : BaseApplicationApiClient
{
    private readonly string _baseUrl;

    public MessagingApplicationApiClient(HttpClient httpClient, IOptions<EndpointOption> endpointOption,
        ILogger<BaseApiClient> logger, IAuthInfoGetter authInfoGetter,
        IContextContainer contextContainer, ICurrentUserContext currentUserContext,
        ICurrentApplicationContext applicationContext, IOptions<JsonOptions> jsonOptions) : base(httpClient, logger,
        authInfoGetter, contextContainer, currentUserContext, applicationContext, jsonOptions)
    {
        _baseUrl = endpointOption.Value["Messaging"]!.Url.TrimEnd('/');
    }

    public async Task<MessageSendResultDto> SendAsync(MessageSendRequestDto messageSendDto)
    {
        var url = $"{_baseUrl}/MessageSend/send";
        return await InternalPostAsync<MessageSendResultDto>(url, messageSendDto);
    }

    public async Task<MessageSendResultDto?> GetSendStatusAsync(long sendId)
    {
        var url = $"{_baseUrl}/MessageSend/status/{sendId}";
        return await InternalGetAsync<MessageSendResultDto>(url);
    }
}


using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.Application.Contract.Interfaces;

/// <summary>
/// MessageAccountProvider 服务接口
/// </summary>
public interface IMessageAccountProviderService :
    IAppService<long, MessageAccountProviderDto, MessageAccountProviderQueryCriteria>,
    IEditableAppService<long, MessageAccountProviderOperationDto>
{
    /// <summary>
    /// 根据账户编码获取账户-服务商配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <returns>账户-服务商配置列表</returns>
    Task<List<MessageAccountProviderDto>> GetByAccountCodeAsync(string accountCode);

    /// <summary>
    /// 根据服务商编码获取账户-服务商配置
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置列表</returns>
    Task<List<MessageAccountProviderDto>> GetByProviderCodeAsync(string providerCode);

    /// <summary>
    /// 根据账户编码和服务商编码获取配置
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>账户-服务商配置</returns>
    Task<MessageAccountProviderDto?> GetByAccountAndProviderAsync(string accountCode, string providerCode);

    /// <summary>
    /// 创建账户-服务商关联
    /// </summary>
    /// <param name="request">关联请求</param>
    /// <returns>创建结果</returns>
    Task<bool> CreateAccountProviderAsync(CreateAccountProviderRequestDto request);

    /// <summary>
    /// 更新账户-服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="request">更新请求</param>
    /// <returns>更新结果</returns>
    Task<bool> UpdateAccountProviderAsync(long id, UpdateAccountProviderRequestDto request);

    /// <summary>
    /// 启用/禁用账户-服务商配置
    /// </summary>
    /// <param name="id">配置ID</param>
    /// <param name="isEnabled">是否启用</param>
    /// <returns>操作结果</returns>
    Task<bool> SetEnabledAsync(long id, bool isEnabled);

    /// <summary>
    /// 删除账户-服务商关联
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>删除结果</returns>
    Task<bool> RemoveAccountProviderAsync(string accountCode, string providerCode);

    /// <summary>
    /// 获取账户的可用服务商列表（有剩余额度的）
    /// </summary>
    /// <param name="accountCode">账户编码</param>
    /// <param name="messageType">消息类型</param>
    /// <returns>可用服务商列表</returns>
    Task<List<MessageAccountProviderDto>> GetAvailableProvidersAsync(string accountCode, string messageType);
}

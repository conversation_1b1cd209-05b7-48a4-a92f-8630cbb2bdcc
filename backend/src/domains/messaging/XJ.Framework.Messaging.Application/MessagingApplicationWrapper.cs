using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Application.Providers;
using XJ.Framework.Messaging.Application.Services;

namespace XJ.Framework.Messaging.Application;

public class MessagingApplicationWrapper : ApplicationWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        // 注册消息Provider
        // 为AliyunSmsProvider注册专用的HttpClient
        services.AddHttpClient<AliyunSmsProvider>(client =>
        {
            // 设置默认请求头，不设置全局Timeout
            // 超时时间通过CancellationToken在每个请求中单独控制
            client.DefaultRequestHeaders.Add("User-Agent", "AliyunSmsProvider/1.0");
        });

        services.AddScoped<EmailProvider>();

        // 注册Provider工厂
        services.AddScoped<IMessageProviderFactory, MessageProviderFactory>();
    }
}

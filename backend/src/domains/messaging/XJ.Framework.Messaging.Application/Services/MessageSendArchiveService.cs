
namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageSendArchive 服务实现
/// </summary>
public sealed class MessageSendArchiveService :
    BaseEditableAppService<long, MessageSendArchiveEntity, MessageSendArchiveDto, MessageSendArchiveOperationDto, IMessageSendArchiveRepository, MessageSendArchiveQueryCriteria>,
    IMessageSendArchiveService
{
    public MessageSendArchiveService(IMessageSendArchiveRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 
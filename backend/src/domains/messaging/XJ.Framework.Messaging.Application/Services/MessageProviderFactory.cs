using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Messaging.Application.Contract.Interfaces;
using XJ.Framework.Messaging.Domain.Shared.Enums;

namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// 消息Provider工厂实现
/// </summary>
public class MessageProviderFactory : IMessageProviderFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<MessageType, Dictionary<string, Type>> _providerTypes;

    public MessageProviderFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
        _providerTypes = new Dictionary<MessageType, Dictionary<string, Type>>();

        // 注册Provider类型
        RegisterProviders();
    }

    /// <summary>
    /// 根据消息类型获取Provider
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <param name="providerName">服务商实例名称，如CommonEmail、AliyunSms</param>
    /// <returns>Provider实例</returns>
    public IMessageProvider? GetProvider(MessageType messageType, string providerName)
    {
        if (!_providerTypes.TryGetValue(messageType, out var providerType))
            return null;

        if (!providerType.TryGetValue(providerName, out var providerInstanceType))
            return null;

        return _serviceProvider.GetService(providerInstanceType) as IMessageProvider;
    }

    /// <summary>
    /// 根据消息类型字符串获取Provider
    /// </summary>
    /// <param name="messageTypeString">消息类型字符串（sms、email等）</param>
    /// <param name="providerName">服务商实例名称，如CommonEmail、AliyunSms</param>
    /// <returns>Provider实例</returns>
    public IMessageProvider? GetProvider(string messageTypeString, string providerName)
    {
        if (string.IsNullOrWhiteSpace(messageTypeString))
            return null;

        if (!MessageTypeExtensions.TryParseFromString(messageTypeString, out var messageType))
            return null;

        return GetProvider(messageType, providerName);
    }

    public Dictionary<string, List<string>> GetProviderNames()
    {
        return _providerTypes.Select(type =>
                    new KeyValuePair<string, List<string>>(type.Key.ToStringValue(), type.Value.Keys.ToList()))
                .ToDictionary(k => k.Key, v => v.Value)
            ;
    }

    /// <summary>
    /// 获取所有支持的消息类型
    /// </summary>
    /// <returns>消息类型列表</returns>
    public IEnumerable<MessageType> GetSupportedMessageTypes()
    {
        return _providerTypes.Keys;
    }

    /// <summary>
    /// 注册Provider类型
    /// </summary>
    private void RegisterProviders()
    {
        // 通过反射自动发现所有IMessageProvider实现

        var providerTypes =
            // AppDomain.CurrentDomain.GetAssemblies()
            // .SelectMany(assembly => assembly.GetTypes())
            typeof(MessageProviderFactory).Assembly.GetTypes()
                .Where(type => typeof(IMessageProvider).IsAssignableFrom(type) &&
                               !type.IsInterface &&
                               !type.IsAbstract)
                .ToList();

        foreach (var providerType in providerTypes)
        {
            try
            {
                // 创建临时实例获取ProviderType
                // 使用 ActivatorUtilities 来创建实例，它会自动从服务提供者中解析依赖（包括日志）
                if (ActivatorUtilities.CreateInstance(_serviceProvider, providerType) is IMessageProvider tempInstance)
                {
                    if (!_providerTypes.ContainsKey(tempInstance.SupportedMessageType))
                        _providerTypes.Add(tempInstance.SupportedMessageType, new Dictionary<string, Type>());

                    _providerTypes[tempInstance.SupportedMessageType][tempInstance.ProviderInstanceName] = providerType;
                }
            }
            catch
            {
                // 忽略创建失败的Provider
            }
        }
    }
}


namespace XJ.Framework.Messaging.Application.Services;

/// <summary>
/// MessageAccountRecharge 服务实现
/// </summary>
public sealed class MessageAccountRechargeService :
    BaseEditableAppService<long, MessageAccountRechargeEntity, MessageAccountRechargeDto, MessageAccountRechargeOperationDto, IMessageAccountRechargeRepository, MessageAccountRechargeQueryCriteria>,
    IMessageAccountRechargeService
{
    public MessageAccountRechargeService(IMessageAccountRechargeRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 
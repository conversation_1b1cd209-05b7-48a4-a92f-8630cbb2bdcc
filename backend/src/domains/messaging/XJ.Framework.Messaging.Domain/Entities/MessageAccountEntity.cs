using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Messaging.Domain.Entities;

/// <summary>
/// MessageAccount 实体
/// </summary>
[Table("message_account", Schema = "m")]
[SoftDeleteIndex("UQ_MessageAccount_AccountCode_AppCode", nameof(AccountCode), nameof(AppCode), IsUnique = true)]
public class MessageAccountEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 账户编码
    /// </summary>
    [Column("account_code")]
    [StringLength(100)]
    public required string AccountCode { get; set; } = null!;

    /// <summary>
    /// 应用编码
    /// </summary>
    [Column("app_code")]
    [StringLength(100)]
    public required string AppCode { get; set; } = null!;

    /// <summary>
    /// 账户名称
    /// </summary>
    [Column("account_name")]
    [StringLength(200)]
    public required string AccountName { get; set; } = null!;

    /// <summary>
    /// 是否启用
    /// </summary>
    [Column("is_enabled")]
    public required bool IsEnabled { get; set; }
}

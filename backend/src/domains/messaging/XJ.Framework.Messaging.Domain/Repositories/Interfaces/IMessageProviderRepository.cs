
namespace XJ.Framework.Messaging.Domain.Repositories.Interfaces;

/// <summary>
/// MessageProvider 仓储接口
/// </summary>

public interface IMessageProviderRepository : IAuditRepository<long, MessageProviderEntity>
{
    /// <summary>
    /// 根据服务商编码获取服务商
    /// </summary>
    /// <param name="providerCode">服务商编码</param>
    /// <returns>服务商实体</returns>
    Task<MessageProviderEntity?> GetByCodeAsync(string providerCode);

    /// <summary>
    /// 根据服务商类型获取服务商列表
    /// </summary>
    /// <param name="providerType">服务商类型</param>
    /// <returns>服务商列表</returns>
    Task<List<MessageProviderEntity>> GetByTypeAsync(string providerType);

    /// <summary>
    /// 获取所有启用的服务商
    /// </summary>
    /// <returns>启用的服务商列表</returns>
    Task<List<MessageProviderEntity>> GetEnabledProvidersAsync();
}


using Microsoft.EntityFrameworkCore;

namespace XJ.Framework.Messaging.EntityFrameworkCore.Repositories;


/// <summary>
/// MessageTemplate 仓储实现
/// </summary>
public class MessageTemplateRepository : BaseSoftDeleteRepository<MessagingDbContext, long, MessageTemplateEntity>, IMessageTemplateRepository
{
    public MessageTemplateRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 根据模板编码和应用编码获取模板
    /// </summary>
    /// <param name="templateCode">模板编码</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板实体</returns>
    public async Task<MessageTemplateEntity?> GetByCodeAsync(string templateCode, string appCode)
    {
        return await DbSet
            .FirstOrDefaultAsync(x => x.TemplateCode == templateCode && x.AppCode == appCode && x.IsEnabled && !x.Deleted);
    }

    /// <summary>
    /// 根据应用编码获取模板列表
    /// </summary>
    /// <param name="appCode">应用编码</param>
    /// <returns>模板列表</returns>
    public async Task<List<MessageTemplateEntity>> GetByAppCodeAsync(string appCode)
    {
        return await DbSet
            .Where(x => x.AppCode == appCode && !x.Deleted)
            .OrderBy(x => x.TemplateCode)
            .ToListAsync();
    }
}

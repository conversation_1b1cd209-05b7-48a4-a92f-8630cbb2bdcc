using System.ComponentModel;

namespace XJ.Framework.Messaging.Domain.Shared.Enums;

/// <summary>
/// 消息类型枚举
/// </summary>
public enum MessageType
{
    /// <summary>
    /// 短信
    /// </summary>
    [Description("短信")]
    Sms = 1,

    /// <summary>
    /// 邮件
    /// </summary>
    [Description("邮件")]
    Email = 2
}

/// <summary>
/// 消息类型扩展方法
/// </summary>
public static class MessageTypeExtensions
{
    /// <summary>
    /// 获取消息类型的字符串值
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>字符串值</returns>
    public static string ToStringValue(this MessageType messageType)
    {
        return messageType switch
        {
            MessageType.Sms => "sms",
            MessageType.Email => "email",
            _ => throw new ArgumentOutOfRangeException(nameof(messageType), messageType, "不支持的消息类型")
        };
    }

    /// <summary>
    /// 从字符串解析消息类型
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <returns>消息类型</returns>
    public static MessageType ParseFromString(string value)
    {
        return value?.ToLower() switch
        {
            "sms" => MessageType.Sms,
            "email" => MessageType.Email,
            _ => throw new ArgumentException($"不支持的消息类型: {value}", nameof(value))
        };
    }

    /// <summary>
    /// 尝试从字符串解析消息类型
    /// </summary>
    /// <param name="value">字符串值</param>
    /// <param name="messageType">解析结果</param>
    /// <returns>是否解析成功</returns>
    public static bool TryParseFromString(string? value, out MessageType messageType)
    {
        messageType = default;
        
        if (string.IsNullOrWhiteSpace(value))
            return false;

        try
        {
            messageType = ParseFromString(value);
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 获取所有支持的消息类型字符串
    /// </summary>
    /// <returns>消息类型字符串列表</returns>
    public static List<string> GetAllStringValues()
    {
        return Enum.GetValues<MessageType>()
            .Select(x => x.ToStringValue())
            .ToList();
    }

    /// <summary>
    /// 获取消息类型的描述
    /// </summary>
    /// <param name="messageType">消息类型</param>
    /// <returns>描述</returns>
    public static string GetDescription(this MessageType messageType)
    {
        var field = messageType.GetType().GetField(messageType.ToString());
        var attribute = field?.GetCustomAttributes(typeof(DescriptionAttribute), false)
            .FirstOrDefault() as DescriptionAttribute;
        return attribute?.Description ?? messageType.ToString();
    }
}

namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// MessageTemplate DTO
/// </summary>
public class MessageTemplateDto : BaseDto<long>
{
    /// <summary>
    /// 模板编码
    /// </summary>
    public string TemplateCode { get; set; } = null!;

    /// <summary>
    /// 应用编码
    /// </summary>
    public string AppCode { get; set; } = null!;

    /// <summary>
    /// 模板名称
    /// </summary>
    public string TemplateName { get; set; } = null!;

    /// <summary>
    /// 模板类型
    /// </summary>
    public string TemplateType { get; set; } = null!;

    /// <summary>
    /// 模板消息标题
    /// </summary>
    public string? Title { get; set; }

    /// <summary>
    /// 模板内容
    /// </summary>
    public string Content { get; set; } = null!;

    /// <summary>
    /// 变量定义
    /// </summary>
    public string? Variable { get; set; }

    /// <summary>
    /// 服务商编码
    /// </summary>
    public string? ProviderCode { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; }

    public DateTimeOffset CreatedTime { get; set; }
}

using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// 更新账户-服务商配置请求DTO
/// </summary>
public class UpdateAccountProviderRequestDto
{
    /// <summary>
    /// 优先级（数值越大优先级越高）
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "优先级必须大于等于0")]
    public int Priority { get; set; } = 0;

    /// <summary>
    /// 总额度
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "总额度必须大于等于0")]
    public int QuotaTotal { get; set; } = 0;

    /// <summary>
    /// 服务商配置（JSON格式）
    /// </summary>
    [StringLength(2000, ErrorMessage = "服务商配置长度不能超过2000个字符")]
    public string? ProviderConfig { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }

    /// <summary>
    /// 是否启用
    /// </summary>
    public bool IsEnabled { get; set; } = true;
}

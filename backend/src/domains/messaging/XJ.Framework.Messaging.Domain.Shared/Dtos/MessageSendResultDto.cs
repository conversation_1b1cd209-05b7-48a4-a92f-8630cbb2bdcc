using XJ.Framework.Library.Application.Contract.OperationDtos;

namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// 消息发送结果 DTO
/// </summary>
public class MessageSendResultDto
{
    /// <summary>
    /// 发送记录ID
    /// </summary>
    public long SendId { get; set; }

    /// <summary>
    /// 是否成功提交发送任务
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 消息内容（如果成功）
    /// </summary>
    public string? Message { get; set; }

    /// <summary>
    /// 错误信息（如果失败）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 发送状态
    /// 0: 待发送
    /// 1: 发送中
    /// 2: 发送成功
    /// 3: 发送失败
    /// </summary>
    public int Status { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTimeOffset CreateTime { get; set; }
}

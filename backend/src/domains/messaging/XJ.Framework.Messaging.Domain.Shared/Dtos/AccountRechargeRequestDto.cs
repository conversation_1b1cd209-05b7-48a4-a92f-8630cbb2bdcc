using System.ComponentModel.DataAnnotations;

namespace XJ.Framework.Messaging.Domain.Shared.Dtos;

/// <summary>
/// 账户-服务商充值请求DTO
/// </summary>
public class AccountProviderRechargeRequestDto
{
    /// <summary>
    /// 账户编码
    /// </summary>
    [Required(ErrorMessage = "账户编码不能为空")]
    [StringLength(100, ErrorMessage = "账户编码长度不能超过100个字符")]
    public string AccountCode { get; set; } = null!;

    /// <summary>
    /// 服务商编码
    /// </summary>
    [Required(ErrorMessage = "服务商编码不能为空")]
    [StringLength(100, ErrorMessage = "服务商编码长度不能超过100个字符")]
    public string ProviderCode { get; set; } = null!;

    /// <summary>
    /// 充值数量
    /// </summary>
    [Required(ErrorMessage = "充值数量不能为空")]
    [Range(1, int.MaxValue, ErrorMessage = "充值数量必须大于0")]
    public int Amount { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [StringLength(500, ErrorMessage = "备注长度不能超过500个字符")]
    public string? Remark { get; set; }
}

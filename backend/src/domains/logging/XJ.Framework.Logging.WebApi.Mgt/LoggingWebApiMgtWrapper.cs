namespace XJ.Framework.Logging.WebApi;

public class LoggingWebApiMgtWrapper : WebApiMgtWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<LoggingApplicationWrapper, LoggingInfrastructureWrapper>(configuration);
        services.AddHttpClient<UserApiClient>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }
    public override void AddFilters(MvcOptions mvcOptions)
    {
        
    }
} 

namespace XJ.Framework.Logging.EntityFrameworkCore;

public class LoggingInfrastructureWrapper : InfrastructureWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddDbContext<LoggingDbContext>(
            optionsAction: (serviceProvider, contextOptions) =>
            {
                var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
                if (env.IsDevelopment())
                {
                    contextOptions.EnableSensitiveDataLogging();
                }

                contextOptions.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            },
            contextLifetime:
            ServiceLifetime.Scoped
        );

        services.AddScoped<IUnitOfWork, UnitOfWork<LoggingDbContext>>();
    }
} 

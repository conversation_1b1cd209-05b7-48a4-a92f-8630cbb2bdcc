# XJ Framework 简化代理架构

## 概述

基于您的需求，我们设计了一个简化的代理架构方案，核心特点：

1. **基于现有ApiClient** - 将现有的ApiClient作为远程服务的代理实现
2. **简单的服务代理** - 通过`IServiceProxy<T>`统一本地和远程调用
3. **路由前缀支持** - 为api和api.mgt站点配置不同的路由前缀
4. **最小化改动** - 保持现有架构，只需添加代理层

## 核心组件

### 1. 服务代理接口

```csharp
/// <summary>
/// 服务代理接口 - 支持本地和远程调用切换
/// </summary>
public interface IServiceProxy<TService> where TService : class
{
    TService GetService();
    Task<TService> GetServiceAsync();
}

/// <summary>
/// 服务代理工厂
/// </summary>
public interface IServiceProxyFactory
{
    IServiceProxy<TService> CreateProxy<TService>() where TService : class;
}
```

### 2. 服务实现模式

#### 统一服务接口
```csharp
public interface IUserService
{
    Task<UserDto?> GetUserAsync(int userId);
    Task<PagedResult<UserDto>> GetUsersAsync(int pageIndex = 1, int pageSize = 20);
    Task<UserDto> CreateUserAsync(CreateUserDto createUserDto);
    // ... 其他方法
}
```

#### 本地服务实现
```csharp
public class LocalUserService : IUserService
{
    // 直接调用本地业务逻辑、Repository等
    public async Task<UserDto?> GetUserAsync(int userId)
    {
        // 调用本地服务逻辑
        return await _userRepository.GetByIdAsync(userId);
    }
}
```

#### 远程服务实现（基于现有ApiClient）
```csharp
public class RemoteUserService : IUserService
{
    private readonly UserApiClient _userApiClient;

    public async Task<UserDto?> GetUserAsync(int userId)
    {
        // 调用现有的ApiClient
        var result = await _userApiClient.GetUserAsync(userId);
        return MapToUserDto(result);
    }
}
```

### 3. 模块注册与路由前缀

```csharp
// 注册模块时指定路由前缀
services.AddModule<RbacWebApiWrapper>(configuration, "rbac");
services.AddModule<RbacWebApiMgtWrapper>(configuration, "rbac-mgt");
```

访问路径：
- RBAC API: `/rbac/api/User/GetUser`
- RBAC MGT API: `/rbac-mgt/api/User/GetUser`

## 使用方式

### 1. 在控制器中使用服务代理

```csharp
[ApiController]
[Route("api/[controller]")]
public class UserController : ControllerBase
{
    private readonly IUnifiedService _unifiedService;

    public async Task<ActionResult<UserDto?>> GetUser(int userId)
    {
        // 获取服务代理（自动选择本地或远程）
        var userServiceProxy = _unifiedService.GetServiceProxy<IUserService>();
        var userService = await userServiceProxy.GetServiceAsync();
        
        // 调用服务方法
        var user = await userService.GetUserAsync(userId);
        return Ok(user);
    }
}
```

### 2. 配置文件控制调用模式

```json
{
  "ServiceProxy": {
    "DefaultCallMode": "Auto",
    "ServiceConfigs": {
      "IUserService": {
        "CallMode": "Local",  // Local, Remote, Auto
        "EnableCache": false
      }
    }
  }
}
```

### 3. 模块配置

```json
{
  "Modules": {
    "EnabledModules": {
      "Rbac": true,        // 启用本地RBAC模块
      "RbacMgt": true,     // 启用本地RBAC管理模块
      "Messaging": false   // 不启用消息模块（将使用远程调用）
    }
  }
}
```

## 部署场景

### 场景1：开发环境（全本地）

**配置**：
```json
{
  "Modules": {
    "EnabledModules": {
      "Rbac": true,
      "RbacMgt": true,
      "Messaging": true,
      "MessagingMgt": true,
      "DynamicForm": true,
      "DynamicFormMgt": true,
      "Files": true,
      "FilesMgt": true,
      "Itmctr": true,
      "ItmctrMgt": true
    }
  },
  "ServiceProxy": {
    "DefaultCallMode": "Local"
  }
}
```

**特点**：
- 所有模块在同一进程中运行
- 服务调用直接使用本地实现
- 调试方便，性能最佳

### 场景2：微服务环境（业务模块本地，基础模块远程）

**配置**：
```json
{
  "Modules": {
    "EnabledModules": {
      "Rbac": false,
      "Messaging": false,
      "DynamicForm": false,
      "Files": false,
      "Itmctr": true,
      "ItmctrMgt": true
    }
  },
  "ServiceProxy": {
    "DefaultCallMode": "Remote",
    "ServiceConfigs": {
      "IUserService": {
        "CallMode": "Remote"
      }
    }
  }
}
```

**特点**：
- 只包含业务模块
- 基础服务通过现有ApiClient远程调用
- 独立部署，易于扩展

### 场景3：混合部署

**配置**：
```json
{
  "Modules": {
    "EnabledModules": {
      "Rbac": true,        // 核心模块本地
      "Messaging": false,  // 非核心模块远程
      "Itmctr": true
    }
  },
  "ServiceProxy": {
    "DefaultCallMode": "Auto"  // 自动选择
  }
}
```

## 实现步骤

### 1. 定义统一服务接口

为每个domain定义统一的服务接口，例如：
- `IUserService` - 用户服务
- `IMessageService` - 消息服务
- `IFormService` - 表单服务
- `IFileInfoService` - 文件服务

### 2. 实现本地服务

创建本地服务实现，直接调用现有的业务逻辑：
```csharp
public class LocalUserService : IUserService
{
    private readonly IUserRepository _userRepository;
    // 实现具体的业务逻辑
}
```

### 3. 实现远程服务（基于现有ApiClient）

创建远程服务实现，封装现有的ApiClient：
```csharp
public class RemoteUserService : IUserService
{
    private readonly UserApiClient _userApiClient;
    
    public async Task<UserDto?> GetUserAsync(int userId)
    {
        var result = await _userApiClient.GetUserAsync(userId);
        return MapToUserDto(result);
    }
}
```

### 4. 注册服务实现

在UnifiedWebApiWrapper中根据模块配置注册对应的实现：
```csharp
if (IsModuleEnabled(moduleConfig, "Rbac"))
{
    services.AddScoped<IUserService, LocalUserService>();
}
else
{
    services.AddScoped<IUserService, RemoteUserService>();
}
```

### 5. 使用服务代理

在需要调用其他服务的地方使用服务代理：
```csharp
var userServiceProxy = _unifiedService.GetServiceProxy<IUserService>();
var userService = await userServiceProxy.GetServiceAsync();
var user = await userService.GetUserAsync(userId);
```

## 优势

1. **最小化改动** - 现有的ApiClient和业务逻辑无需修改
2. **灵活部署** - 通过配置文件即可切换本地/远程调用
3. **路由隔离** - api和api.mgt通过前缀避免路由冲突
4. **统一接口** - 业务代码无需关心调用方式
5. **渐进迁移** - 可以逐步从单体迁移到微服务

## 总结

这个简化的代理架构方案完美解决了您的需求：
- ✅ 基于现有ApiClient实现远程调用
- ✅ 支持本地和远程调用的灵活切换
- ✅ 为api和api.mgt配置不同的路由前缀
- ✅ 最小化代码改动
- ✅ 保持现有架构的优势

通过这个方案，您可以轻松地将现有的单体应用改造为支持灵活部署的模块化架构。

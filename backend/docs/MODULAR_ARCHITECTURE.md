# XJ Framework 模块化架构设计

## 概述

XJ Framework 采用模块化架构设计，支持灵活的部署方式，包括单体应用、微服务和混合部署模式。该架构基于现有的 WebApiWrapper 模式进行扩展，提供了优雅的服务间调用机制。

## 核心特性

### 1. 模块化设计
- **模块接口**: 基于 `IModule` 接口的标准化模块定义
- **依赖管理**: 支持模块间依赖关系声明和验证
- **动态加载**: 支持运行时动态加载和卸载模块
- **配置驱动**: 通过配置文件控制模块的启用和配置

### 2. 灵活部署
- **单体模式**: 所有模块在同一进程中运行，适合开发和小型部署
- **微服务模式**: 每个模块独立部署，适合大规模分布式系统
- **混合模式**: 部分模块本地运行，部分模块远程调用

### 3. 服务调用抽象
- **统一接口**: 通过 `IServiceCaller<T>` 提供统一的服务调用接口
- **自动切换**: 根据配置自动选择本地调用或远程调用
- **动态代理**: 基于 DispatchProxy 实现透明的远程服务调用
- **拦截器支持**: 支持日志、性能监控、重试等横切关注点

## 架构组件

### 核心接口

```csharp
// 模块接口
public interface IModule
{
    string ModuleName { get; }
    string Version { get; }
    string[] Dependencies { get; }
    void ConfigureServices(IServiceCollection services, IConfiguration configuration);
    Type[] GetProvidedServices();
}

// 服务调用器接口
public interface IServiceCaller<TService> where TService : class
{
    TService GetService();
    Task<TService> GetServiceAsync();
}

// 模块注册中心接口
public interface IModuleRegistry
{
    void RegisterModule(IModule module);
    IEnumerable<IModule> GetAllModules();
    ServiceProviderInfo? GetServiceProvider(Type serviceType);
}
```

### 服务代理机制

```csharp
// 动态代理工厂
public interface IServiceProxyFactory
{
    Task<TService> CreateProxyAsync<TService>(ServiceProviderInfo providerInfo) where TService : class;
}

// 拦截器接口
public interface IServiceProxyInterceptor
{
    int Order { get; }
    Task<bool> BeforeInvokeAsync(ServiceProxyContext context);
    Task AfterInvokeAsync(ServiceProxyContext context);
    Task<bool> OnExceptionAsync(ServiceProxyContext context, Exception exception);
}
```

## 部署模式

### 1. 单体部署 (Monolith)

所有模块在同一个进程中运行，适合：
- 开发环境
- 小型应用
- 简单部署场景

**配置示例**:
```json
{
  "Gateway": {
    "EnabledModules": ["rbac", "messaging", "dynamicform", "files", "itmctr"],
    "DeploymentMode": "Monolith"
  },
  "ServiceCall": {
    "Mode": "Local"
  }
}
```

**构建命令**:
```powershell
.\build-gateway.ps1 -DeploymentMode Monolith
```

### 2. 微服务部署 (Microservices)

每个模块独立部署为单独的服务，适合：
- 大规模应用
- 高可用性要求
- 独立扩展需求

**配置示例**:
```json
{
  "Gateway": {
    "EnabledModules": ["itmctr"],
    "DeploymentMode": "Microservices"
  },
  "ServiceCall": {
    "Mode": "Remote"
  },
  "Endpoint": [
    {
      "Name": "Rbac",
      "Url": "http://rbac-service:8080"
    }
  ]
}
```

**构建命令**:
```powershell
.\build-gateway.ps1 -DeploymentMode Microservices -IncludeModules @("Itmctr")
```

### 3. 混合部署 (Hybrid)

部分模块本地运行，部分模块远程调用，适合：
- 渐进式微服务化
- 性能优化场景
- 复杂业务需求

**配置示例**:
```json
{
  "Gateway": {
    "EnabledModules": ["rbac", "itmctr"],
    "DeploymentMode": "Hybrid"
  },
  "ServiceCall": {
    "Mode": "Auto"
  }
}
```

## 使用指南

### 1. 创建新模块

```csharp
public class MyModule : ModuleBase
{
    public override string ModuleName => "MyModule";
    public override string Version => "1.0.0";
    public override string[] Dependencies => new[] { "Rbac" };

    public override void ConfigureServices(IServiceCollection services, IConfiguration configuration)
    {
        services.AddScoped<IMyService, MyService>();
    }

    public override Type[] GetProvidedServices()
    {
        return new[] { typeof(IMyService) };
    }
}
```

### 2. 服务调用

```csharp
// 在服务中注入服务调用器
public class MyController : ControllerBase
{
    private readonly IServiceCaller<IUserService> _userServiceCaller;

    public MyController(IServiceCaller<IUserService> userServiceCaller)
    {
        _userServiceCaller = userServiceCaller;
    }

    public async Task<IActionResult> GetUser(int id)
    {
        var userService = await _userServiceCaller.GetServiceAsync();
        var user = await userService.GetUserAsync(id);
        return Ok(user);
    }
}
```

### 3. 扩展方法使用

```csharp
// 在 Startup 中配置模块化
public void ConfigureServices(IServiceCollection services)
{
    services.AddModularity(Configuration);
    services.AddModule<RbacModule>(Configuration);
    services.AddModule<MessagingModule>(Configuration);
}

// 在服务中直接获取模块化服务
public class MyService
{
    private readonly IServiceProvider _serviceProvider;

    public async Task DoSomethingAsync()
    {
        var userService = await _serviceProvider.GetModularServiceAsync<IUserService>();
        // 使用服务...
    }
}
```

## 最佳实践

### 1. 模块设计原则
- **单一职责**: 每个模块专注于特定的业务领域
- **松耦合**: 模块间通过接口交互，避免直接依赖
- **高内聚**: 模块内部组件紧密协作
- **可测试**: 支持单元测试和集成测试

### 2. 服务接口设计
- **异步优先**: 所有服务接口优先使用异步方法
- **DTO传输**: 使用DTO对象进行跨模块数据传输
- **错误处理**: 定义清晰的异常处理策略
- **版本控制**: 考虑接口的向后兼容性

### 3. 配置管理
- **环境隔离**: 不同环境使用不同的配置文件
- **敏感信息**: 使用 User Secrets 或环境变量管理敏感配置
- **配置验证**: 在启动时验证配置的完整性

### 4. 监控和诊断
- **健康检查**: 实现模块级别的健康检查
- **日志记录**: 使用结构化日志记录关键操作
- **性能监控**: 监控服务调用的性能指标
- **分布式追踪**: 在微服务模式下实现分布式追踪

## 迁移指南

### 从现有架构迁移

1. **保持现有WebApiWrapper**: 现有的WebApiWrapper可以继续使用
2. **逐步模块化**: 可以逐个模块进行改造
3. **配置兼容**: 新架构兼容现有的配置格式
4. **渐进部署**: 支持从单体到微服务的渐进式迁移

### 迁移步骤

1. **创建模块类**: 为每个domain创建对应的Module类
2. **注册服务**: 在模块中注册提供的服务
3. **更新调用方式**: 使用ServiceCaller替换直接的服务调用
4. **配置部署模式**: 根据需求选择合适的部署模式
5. **测试验证**: 确保功能正常且性能满足要求

## 总结

XJ Framework的模块化架构提供了灵活、可扩展的解决方案，支持从单体应用到微服务的平滑演进。通过统一的服务调用抽象和配置驱动的部署模式，开发团队可以根据实际需求选择最适合的架构方案。

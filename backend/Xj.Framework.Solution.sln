
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35919.96
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{E7D3F41C-D0A9-4CA1-BA63-C87EDA1050E8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{67C4869E-4BEF-4F9B-A28D-1E08A440A620}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "solution items", "solution items", "{4B4EB4CC-FB42-42EE-8C36-A1AD4C53A528}"
	ProjectSection(SolutionItems) = preProject
		Common.props = Common.props
		Common.Secrets.props = Common.Secrets.props
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "core", "core", "{9F638471-879A-4602-A417-18B18BCCF6DE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "abstractions", "abstractions", "{006461A9-5699-4AE3-AA06-9A587887CB34}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "impls", "impls", "{4433EF75-7336-4345-A937-F165D96A4B63}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Cache.Abstraction", "src\core\abstractions\XJ.Framework.Library.Cache.Abstraction\XJ.Framework.Library.Cache.Abstraction.csproj", "{C7E65EC3-3396-427A-91C6-FE425F24E2D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Common.Abstraction", "src\core\abstractions\XJ.Framework.Library.Common.Abstraction\XJ.Framework.Library.Common.Abstraction.csproj", "{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Logging.Abstraction", "src\core\abstractions\XJ.Framework.Library.Logging.Abstraction\XJ.Framework.Library.Logging.Abstraction.csproj", "{5B36390A-FE9F-4170-9286-50060F759E4D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Excel.Abstraction", "src\core\abstractions\XJ.Framework.Library.Excel.Abstraction\XJ.Framework.Library.Excel.Abstraction.csproj", "{3E14E364-C511-4A51-AB3D-97113244E55B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Queue.Abstraction", "src\core\abstractions\XJ.Framework.Library.Queue.Abstraction\XJ.Framework.Library.Queue.Abstraction.csproj", "{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "shared", "shared", "{E854C305-B084-4C8F-8B90-DC048598BE32}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.WebApi", "src\shared\XJ.Framework.Library.WebApi\XJ.Framework.Library.WebApi.csproj", "{251C9FAB-8367-4810-99AF-C9CCC00A6676}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Domain", "src\shared\XJ.Framework.Library.Domain\XJ.Framework.Library.Domain.csproj", "{44BB7D43-77C4-4585-93E6-B74DC362D0D0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Application", "src\shared\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj", "{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.EntityFrameworkCore", "src\shared\XJ.Framework.Library.EntityFrameworkCore\XJ.Framework.Library.EntityFrameworkCore.csproj", "{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Domain.Shared", "src\shared\XJ.Framework.Library.Domain.Shared\XJ.Framework.Library.Domain.Shared.csproj", "{7F4FBDD4-996D-485A-84F3-24929AD7EA64}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Application.Contract", "src\shared\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj", "{B4CA04CF-A45F-444A-B122-5821B353FF0C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "domains", "domains", "{33A64402-D445-4FC5-94AF-2CC93E1F246A}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "workflow", "workflow", "{2485B8A1-7868-430E-BD97-2B20A651A83C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "dynamic-form", "dynamic-form", "{F4C1998D-10FE-4C8B-B628-0F3C88B80817}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "rbac", "rbac", "{CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.AsyncProcess", "src\core\impls\XJ.Framework.Library.AsyncProcess\XJ.Framework.Library.AsyncProcess.csproj", "{A63788C5-6C95-466F-875F-B07A24535FB3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Common", "src\core\impls\XJ.Framework.Library.Common\XJ.Framework.Library.Common.csproj", "{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Logging.Database", "src\core\impls\XJ.Framework.Library.Logging.Database\XJ.Framework.Library.Logging.Database.csproj", "{44E3EC37-8309-4415-A947-E1D86D699F5B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Queue.RabbitMq", "src\core\impls\XJ.Framework.Library.Queue.RabbitMq\XJ.Framework.Library.Queue.RabbitMq.csproj", "{B8020CCC-EBC8-4634-8972-7921FD2ADF00}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Excel.NPOI", "src\core\impls\XJ.Framework.Library.Excel.NPOI\XJ.Framework.Library.Excel.NPOI.csproj", "{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Image", "src\core\impls\XJ.Framework.Library.Image\XJ.Framework.Library.Image.csproj", "{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.WebApi.Mgt", "src\shared\XJ.Framework.Library.WebApi.Mgt\XJ.Framework.Library.WebApi.Mgt.csproj", "{74E3475F-598D-44A2-863F-EE1C98A94747}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "messaging", "messaging", "{19511181-670B-4494-AF8C-27508344DF2C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "dotnet-cli", "dotnet-cli", "{D9AC39F5-618F-49D3-8F2B-4FB12B20C669}"
	ProjectSection(SolutionItems) = preProject
		dotnet-cli\Directory.Build.props = dotnet-cli\Directory.Build.props
		dotnet-cli\install.sh = dotnet-cli\install.sh
		dotnet-cli\README.md = dotnet-cli\README.md
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Tool.Cli", "dotnet-cli\XJ.Framework.Tool.Cli\XJ.Framework.Tool.Cli.csproj", "{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Infrastructure.ApiClient", "src\shared\XJ.Framework.Library.Infrastructure.ApiClient\XJ.Framework.Library.Infrastructure.ApiClient.csproj", "{D5857E78-C4E5-42DD-B878-1582A7D76536}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.ApiClient", "src\domains\rbac\XJ.Framework.Rbac.ApiClient\XJ.Framework.Rbac.ApiClient.csproj", "{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.EventBus.Abstraction", "src\core\abstractions\XJ.Framework.Library.EventBus.Abstraction\XJ.Framework.Library.EventBus.Abstraction.csproj", "{E2115F64-1B86-4AF3-8C04-D1367B19940A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.DependencyInjection.Abstraction", "src\core\abstractions\XJ.Framework.Library.DependencyInjection.Abstraction\XJ.Framework.Library.DependencyInjection.Abstraction.csproj", "{EED08377-3393-478E-AEC5-FE293A2B5072}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.WebApi", "src\domains\rbac\XJ.Framework.Rbac.WebApi\XJ.Framework.Rbac.WebApi.csproj", "{25B09CC5-534D-453E-88BE-3BB57219C87F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.Domain.Shared", "src\domains\rbac\XJ.Framework.Rbac.Domain.Shared\XJ.Framework.Rbac.Domain.Shared.csproj", "{37A682A3-6280-430E-A53C-65CF92EE3F37}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.Domain", "src\domains\rbac\XJ.Framework.Rbac.Domain\XJ.Framework.Rbac.Domain.csproj", "{B3A278A9-DD6C-4F27-A312-263DD5CE9954}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.WebApi.Mgt", "src\domains\rbac\XJ.Framework.Rbac.WebApi.Mgt\XJ.Framework.Rbac.WebApi.Mgt.csproj", "{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.EntityFrameworkCore", "src\domains\rbac\XJ.Framework.Rbac.EntityFrameworkCore\XJ.Framework.Rbac.EntityFrameworkCore.csproj", "{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.Application", "src\domains\rbac\XJ.Framework.Rbac.Application\XJ.Framework.Rbac.Application.csproj", "{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Rbac.Application.Contract", "src\domains\rbac\XJ.Framework.Rbac.Application.Contract\XJ.Framework.Rbac.Application.Contract.csproj", "{1D6923B4-3A61-4A66-86A4-78EDDC33814C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "settings", "settings", "{CB7DE409-01CA-4064-9226-F4A868996546}"
	ProjectSection(SolutionItems) = preProject
		settings\application.Development.json = settings\application.Development.json
		settings\application.DockerDev.json = settings\application.DockerDev.json
		settings\application.Production.json = settings\application.Production.json
		settings\application.SIT.json = settings\application.SIT.json
		settings\cache.Development.json = settings\cache.Development.json
		settings\cache.DockerDev.json = settings\cache.DockerDev.json
		settings\cache.json = settings\cache.json
		settings\cache.Production.json = settings\cache.Production.json
		settings\cache.SIT.json = settings\cache.SIT.json
		settings\database.Development.json = settings\database.Development.json
		settings\database.DockerDev.json = settings\database.DockerDev.json
		settings\endpoint.Development.json = settings\endpoint.Development.json
		settings\endpoint.DockerDev.json = settings\endpoint.DockerDev.json
		settings\endpoint.Production.json = settings\endpoint.Production.json
		settings\endpoint.SIT.json = settings\endpoint.SIT.json
		settings\logging.Development.json = settings\logging.Development.json
		settings\logging.DockerDev.json = settings\logging.DockerDev.json
		settings\logging.Production.json = settings\logging.Production.json
		settings\logging.SIT.json = settings\logging.SIT.json
		settings\secretKey.Development.json = settings\secretKey.Development.json
		settings\secretKey.DockerDev.json = settings\secretKey.DockerDev.json
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "feature", "feature", "{F7412AE4-9186-4039-9A2B-0D825EE7F0D8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "files", "files", "{8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.Domain.Shared", "src\domains\files\XJ.Framework.Files.Domain.Shared\XJ.Framework.Files.Domain.Shared.csproj", "{CD1440E9-26DD-41F0-A6AD-4C685874444D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.Application.Contract", "src\domains\files\XJ.Framework.Files.Application.Contract\XJ.Framework.Files.Application.Contract.csproj", "{7029AB24-285C-40DA-8EFF-6C68F3CC8295}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.Application", "src\domains\files\XJ.Framework.Files.Application\XJ.Framework.Files.Application.csproj", "{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.EntityFrameworkCore", "src\domains\files\XJ.Framework.Files.EntityFrameworkCore\XJ.Framework.Files.EntityFrameworkCore.csproj", "{D81DF3D2-1AE6-4445-8647-5E190C3589F9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.Domain", "src\domains\files\XJ.Framework.Files.Domain\XJ.Framework.Files.Domain.csproj", "{C7596C4F-DB55-4716-9380-F9F6BC10AD33}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.WebApi", "src\domains\files\XJ.Framework.Files.WebApi\XJ.Framework.Files.WebApi.csproj", "{CE4B9DE7-734D-41D6-B015-326C9E077BB6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.WebApi.Mgt", "src\domains\files\XJ.Framework.Files.WebApi.Mgt\XJ.Framework.Files.WebApi.Mgt.csproj", "{09CC9881-FA45-49DC-B1C5-BB16330E4C92}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "docker-compose", "docker-compose", "{54E5CCB5-8F1E-409E-B929-767A378378E1}"
	ProjectSection(SolutionItems) = preProject
		..\docker-compose-quickly.cmd = ..\docker-compose-quickly.cmd
		..\docker-compose-quickly.sh = ..\docker-compose-quickly.sh
		..\docker-compose.cmd = ..\docker-compose.cmd
		..\docker-compose.sh = ..\docker-compose.sh
		..\docker-compose.yml = ..\docker-compose.yml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.Domain.Shared", "src\domains\dynamic-form\XJ.Framework.DynamicForm.Domain.Shared\XJ.Framework.DynamicForm.Domain.Shared.csproj", "{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.WebApi", "src\domains\dynamic-form\XJ.Framework.DynamicForm.WebApi\XJ.Framework.DynamicForm.WebApi.csproj", "{B99B6C63-F1AA-47ED-8B26-8CC88905536B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.Domain", "src\domains\dynamic-form\XJ.Framework.DynamicForm.Domain\XJ.Framework.DynamicForm.Domain.csproj", "{9EE1883D-A2D6-44D1-A773-00FA793D6C15}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.WebApi.Mgt", "src\domains\dynamic-form\XJ.Framework.DynamicForm.WebApi.Mgt\XJ.Framework.DynamicForm.WebApi.Mgt.csproj", "{471401A2-E83C-4B43-9F0B-E582FD034BE0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.EntityFrameworkCore", "src\domains\dynamic-form\XJ.Framework.DynamicForm.EntityFrameworkCore\XJ.Framework.DynamicForm.EntityFrameworkCore.csproj", "{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.Application", "src\domains\dynamic-form\XJ.Framework.DynamicForm.Application\XJ.Framework.DynamicForm.Application.csproj", "{F9118BA4-C399-4049-AF39-6E978CB5411C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.Application.Contract", "src\domains\dynamic-form\XJ.Framework.DynamicForm.Application.Contract\XJ.Framework.DynamicForm.Application.Contract.csproj", "{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "itmctr", "itmctr", "{C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.Application.Contract", "src\domains\itmctr\XJ.Framework.Itmctr.Application.Contract\XJ.Framework.Itmctr.Application.Contract.csproj", "{1E2C554C-641E-4D53-865A-818F3DD8E2B4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.Domain.Shared", "src\domains\itmctr\XJ.Framework.Itmctr.Domain.Shared\XJ.Framework.Itmctr.Domain.Shared.csproj", "{2663721C-B547-4A79-BD7C-73630C3AD074}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.WebApi", "src\domains\itmctr\XJ.Framework.Itmctr.WebApi\XJ.Framework.Itmctr.WebApi.csproj", "{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.Application", "src\domains\itmctr\XJ.Framework.Itmctr.Application\XJ.Framework.Itmctr.Application.csproj", "{B93C2054-5064-410C-9981-4DC43FB10C31}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.Domain", "src\domains\itmctr\XJ.Framework.Itmctr.Domain\XJ.Framework.Itmctr.Domain.csproj", "{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.WebApi.Mgt", "src\domains\itmctr\XJ.Framework.Itmctr.WebApi.Mgt\XJ.Framework.Itmctr.WebApi.Mgt.csproj", "{C7CD0225-5576-40FC-860A-F28FCE8E418F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Itmctr.EntityFrameworkCore", "src\domains\itmctr\XJ.Framework.Itmctr.EntityFrameworkCore\XJ.Framework.Itmctr.EntityFrameworkCore.csproj", "{27F542FB-67CC-4DAC-944D-081E87BC824B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Files.ApiClient", "src\domains\files\XJ.Framework.Files.ApiClient\XJ.Framework.Files.ApiClient.csproj", "{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.DynamicForm.ApiClient", "src\domains\dynamic-form\XJ.Framework.DynamicForm.ApiClient\XJ.Framework.DynamicForm.ApiClient.csproj", "{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "logging", "logging", "{3B463BE9-C312-4EA5-B505-F8CBB0111257}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Logging.EntityFrameworkCore", "src\domains\logging\XJ.Framework.Logging.EntityFrameworkCore\XJ.Framework.Logging.EntityFrameworkCore.csproj", "{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Logging.Application", "src\domains\logging\XJ.Framework.Logging.Application\XJ.Framework.Logging.Application.csproj", "{24247038-FFC3-4327-A553-08F147DEE2E7}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Logging.WebApi.Mgt", "src\domains\logging\XJ.Framework.Logging.WebApi.Mgt\XJ.Framework.Logging.WebApi.Mgt.csproj", "{208A0554-3534-4CFF-91CB-4B00E7FBC567}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Logging.Application.Contract", "src\domains\logging\XJ.Framework.Logging.Application.Contract\XJ.Framework.Logging.Application.Contract.csproj", "{B918A11F-BFFB-4CCD-89FD-3505DF65513D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Logging.Domain.Shared", "src\domains\logging\XJ.Framework.Logging.Domain.Shared\XJ.Framework.Logging.Domain.Shared.csproj", "{ACE65369-0B03-4906-B17F-368A29E42559}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Logging.Domain", "src\domains\logging\XJ.Framework.Logging.Domain\XJ.Framework.Logging.Domain.csproj", "{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.Cache", "src\core\impls\XJ.Framework.Library.Cache\XJ.Framework.Library.Cache.csproj", "{A3306660-F615-46D6-8D2C-9BF90A3D696F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.WebApi", "src\domains\messaging\XJ.Framework.Messaging.WebApi\XJ.Framework.Messaging.WebApi.csproj", "{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.Domain", "src\domains\messaging\XJ.Framework.Messaging.Domain\XJ.Framework.Messaging.Domain.csproj", "{4F165CFE-46DA-4954-9571-B778F669FFB0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.Application", "src\domains\messaging\XJ.Framework.Messaging.Application\XJ.Framework.Messaging.Application.csproj", "{C200FA70-EF37-427E-B168-09D067789B72}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.Domain.Shared", "src\domains\messaging\XJ.Framework.Messaging.Domain.Shared\XJ.Framework.Messaging.Domain.Shared.csproj", "{B37E32DA-C637-428D-A863-6C4CBB27ED9A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.Application.Contract", "src\domains\messaging\XJ.Framework.Messaging.Application.Contract\XJ.Framework.Messaging.Application.Contract.csproj", "{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.EntityFrameworkCore", "src\domains\messaging\XJ.Framework.Messaging.EntityFrameworkCore\XJ.Framework.Messaging.EntityFrameworkCore.csproj", "{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.WebApi.Mgt", "src\domains\messaging\XJ.Framework.Messaging.WebApi.Mgt\XJ.Framework.Messaging.WebApi.Mgt.csproj", "{8B465D25-D0B2-4143-BD72-142AF72B2168}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Messaging.ApiClient", "src\domains\messaging\XJ.Framework.Messaging.ApiClient\XJ.Framework.Messaging.ApiClient.csproj", "{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.DistributedLock", "src\core\abstractions\XJ.Framework.Library.DistributedLock\XJ.Framework.Library.DistributedLock.csproj", "{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "XJ.Framework.Library.DistributedLock.Cache", "src\core\impls\XJ.Framework.Library.DistributedLock.Cache\XJ.Framework.Library.DistributedLock.Cache.csproj", "{F7041D17-6554-4C43-8E61-1209B1B5CF8A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Quickly|Any CPU = Quickly|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5}.Release|Any CPU.Build.0 = Release|Any CPU
		{5B36390A-FE9F-4170-9286-50060F759E4D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5B36390A-FE9F-4170-9286-50060F759E4D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5B36390A-FE9F-4170-9286-50060F759E4D}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{5B36390A-FE9F-4170-9286-50060F759E4D}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{5B36390A-FE9F-4170-9286-50060F759E4D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5B36390A-FE9F-4170-9286-50060F759E4D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3E14E364-C511-4A51-AB3D-97113244E55B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3E14E364-C511-4A51-AB3D-97113244E55B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3E14E364-C511-4A51-AB3D-97113244E55B}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{3E14E364-C511-4A51-AB3D-97113244E55B}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{3E14E364-C511-4A51-AB3D-97113244E55B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3E14E364-C511-4A51-AB3D-97113244E55B}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8}.Release|Any CPU.Build.0 = Release|Any CPU
		{251C9FAB-8367-4810-99AF-C9CCC00A6676}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{251C9FAB-8367-4810-99AF-C9CCC00A6676}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{251C9FAB-8367-4810-99AF-C9CCC00A6676}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{251C9FAB-8367-4810-99AF-C9CCC00A6676}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{251C9FAB-8367-4810-99AF-C9CCC00A6676}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{251C9FAB-8367-4810-99AF-C9CCC00A6676}.Release|Any CPU.Build.0 = Release|Any CPU
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F}.Release|Any CPU.Build.0 = Release|Any CPU
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436}.Release|Any CPU.Build.0 = Release|Any CPU
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64}.Release|Any CPU.Build.0 = Release|Any CPU
		{B4CA04CF-A45F-444A-B122-5821B353FF0C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B4CA04CF-A45F-444A-B122-5821B353FF0C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B4CA04CF-A45F-444A-B122-5821B353FF0C}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B4CA04CF-A45F-444A-B122-5821B353FF0C}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B4CA04CF-A45F-444A-B122-5821B353FF0C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B4CA04CF-A45F-444A-B122-5821B353FF0C}.Release|Any CPU.Build.0 = Release|Any CPU
		{A63788C5-6C95-466F-875F-B07A24535FB3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A63788C5-6C95-466F-875F-B07A24535FB3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A63788C5-6C95-466F-875F-B07A24535FB3}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{A63788C5-6C95-466F-875F-B07A24535FB3}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{A63788C5-6C95-466F-875F-B07A24535FB3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A63788C5-6C95-466F-875F-B07A24535FB3}.Release|Any CPU.Build.0 = Release|Any CPU
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2}.Release|Any CPU.Build.0 = Release|Any CPU
		{44E3EC37-8309-4415-A947-E1D86D699F5B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44E3EC37-8309-4415-A947-E1D86D699F5B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44E3EC37-8309-4415-A947-E1D86D699F5B}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{44E3EC37-8309-4415-A947-E1D86D699F5B}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{44E3EC37-8309-4415-A947-E1D86D699F5B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44E3EC37-8309-4415-A947-E1D86D699F5B}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98}.Release|Any CPU.Build.0 = Release|Any CPU
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A}.Release|Any CPU.Build.0 = Release|Any CPU
		{74E3475F-598D-44A2-863F-EE1C98A94747}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{74E3475F-598D-44A2-863F-EE1C98A94747}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{74E3475F-598D-44A2-863F-EE1C98A94747}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{74E3475F-598D-44A2-863F-EE1C98A94747}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{74E3475F-598D-44A2-863F-EE1C98A94747}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{74E3475F-598D-44A2-863F-EE1C98A94747}.Release|Any CPU.Build.0 = Release|Any CPU
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90}.Release|Any CPU.Build.0 = Release|Any CPU
		{D5857E78-C4E5-42DD-B878-1582A7D76536}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D5857E78-C4E5-42DD-B878-1582A7D76536}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D5857E78-C4E5-42DD-B878-1582A7D76536}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{D5857E78-C4E5-42DD-B878-1582A7D76536}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{D5857E78-C4E5-42DD-B878-1582A7D76536}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D5857E78-C4E5-42DD-B878-1582A7D76536}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317}.Release|Any CPU.Build.0 = Release|Any CPU
		{E2115F64-1B86-4AF3-8C04-D1367B19940A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E2115F64-1B86-4AF3-8C04-D1367B19940A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E2115F64-1B86-4AF3-8C04-D1367B19940A}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{E2115F64-1B86-4AF3-8C04-D1367B19940A}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{E2115F64-1B86-4AF3-8C04-D1367B19940A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E2115F64-1B86-4AF3-8C04-D1367B19940A}.Release|Any CPU.Build.0 = Release|Any CPU
		{EED08377-3393-478E-AEC5-FE293A2B5072}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EED08377-3393-478E-AEC5-FE293A2B5072}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EED08377-3393-478E-AEC5-FE293A2B5072}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{EED08377-3393-478E-AEC5-FE293A2B5072}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{EED08377-3393-478E-AEC5-FE293A2B5072}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EED08377-3393-478E-AEC5-FE293A2B5072}.Release|Any CPU.Build.0 = Release|Any CPU
		{25B09CC5-534D-453E-88BE-3BB57219C87F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{25B09CC5-534D-453E-88BE-3BB57219C87F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{25B09CC5-534D-453E-88BE-3BB57219C87F}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{25B09CC5-534D-453E-88BE-3BB57219C87F}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{25B09CC5-534D-453E-88BE-3BB57219C87F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{25B09CC5-534D-453E-88BE-3BB57219C87F}.Release|Any CPU.Build.0 = Release|Any CPU
		{37A682A3-6280-430E-A53C-65CF92EE3F37}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{37A682A3-6280-430E-A53C-65CF92EE3F37}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{37A682A3-6280-430E-A53C-65CF92EE3F37}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{37A682A3-6280-430E-A53C-65CF92EE3F37}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{37A682A3-6280-430E-A53C-65CF92EE3F37}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{37A682A3-6280-430E-A53C-65CF92EE3F37}.Release|Any CPU.Build.0 = Release|Any CPU
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954}.Release|Any CPU.Build.0 = Release|Any CPU
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D}.Release|Any CPU.Build.0 = Release|Any CPU
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C}.Release|Any CPU.Build.0 = Release|Any CPU
		{CD1440E9-26DD-41F0-A6AD-4C685874444D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CD1440E9-26DD-41F0-A6AD-4C685874444D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CD1440E9-26DD-41F0-A6AD-4C685874444D}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{CD1440E9-26DD-41F0-A6AD-4C685874444D}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{CD1440E9-26DD-41F0-A6AD-4C685874444D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CD1440E9-26DD-41F0-A6AD-4C685874444D}.Release|Any CPU.Build.0 = Release|Any CPU
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295}.Release|Any CPU.Build.0 = Release|Any CPU
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C}.Release|Any CPU.Build.0 = Release|Any CPU
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6}.Release|Any CPU.Build.0 = Release|Any CPU
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92}.Release|Any CPU.Build.0 = Release|Any CPU
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052}.Release|Any CPU.Build.0 = Release|Any CPU
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B}.Release|Any CPU.Build.0 = Release|Any CPU
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15}.Release|Any CPU.Build.0 = Release|Any CPU
		{471401A2-E83C-4B43-9F0B-E582FD034BE0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{471401A2-E83C-4B43-9F0B-E582FD034BE0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{471401A2-E83C-4B43-9F0B-E582FD034BE0}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{471401A2-E83C-4B43-9F0B-E582FD034BE0}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{471401A2-E83C-4B43-9F0B-E582FD034BE0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{471401A2-E83C-4B43-9F0B-E582FD034BE0}.Release|Any CPU.Build.0 = Release|Any CPU
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4}.Release|Any CPU.Build.0 = Release|Any CPU
		{F9118BA4-C399-4049-AF39-6E978CB5411C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F9118BA4-C399-4049-AF39-6E978CB5411C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F9118BA4-C399-4049-AF39-6E978CB5411C}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{F9118BA4-C399-4049-AF39-6E978CB5411C}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{F9118BA4-C399-4049-AF39-6E978CB5411C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F9118BA4-C399-4049-AF39-6E978CB5411C}.Release|Any CPU.Build.0 = Release|Any CPU
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4}.Release|Any CPU.Build.0 = Release|Any CPU
		{2663721C-B547-4A79-BD7C-73630C3AD074}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2663721C-B547-4A79-BD7C-73630C3AD074}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2663721C-B547-4A79-BD7C-73630C3AD074}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{2663721C-B547-4A79-BD7C-73630C3AD074}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{2663721C-B547-4A79-BD7C-73630C3AD074}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2663721C-B547-4A79-BD7C-73630C3AD074}.Release|Any CPU.Build.0 = Release|Any CPU
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41}.Release|Any CPU.Build.0 = Release|Any CPU
		{B93C2054-5064-410C-9981-4DC43FB10C31}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B93C2054-5064-410C-9981-4DC43FB10C31}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B93C2054-5064-410C-9981-4DC43FB10C31}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B93C2054-5064-410C-9981-4DC43FB10C31}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B93C2054-5064-410C-9981-4DC43FB10C31}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B93C2054-5064-410C-9981-4DC43FB10C31}.Release|Any CPU.Build.0 = Release|Any CPU
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497}.Release|Any CPU.Build.0 = Release|Any CPU
		{C7CD0225-5576-40FC-860A-F28FCE8E418F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C7CD0225-5576-40FC-860A-F28FCE8E418F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C7CD0225-5576-40FC-860A-F28FCE8E418F}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C7CD0225-5576-40FC-860A-F28FCE8E418F}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C7CD0225-5576-40FC-860A-F28FCE8E418F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C7CD0225-5576-40FC-860A-F28FCE8E418F}.Release|Any CPU.Build.0 = Release|Any CPU
		{27F542FB-67CC-4DAC-944D-081E87BC824B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{27F542FB-67CC-4DAC-944D-081E87BC824B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{27F542FB-67CC-4DAC-944D-081E87BC824B}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{27F542FB-67CC-4DAC-944D-081E87BC824B}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{27F542FB-67CC-4DAC-944D-081E87BC824B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{27F542FB-67CC-4DAC-944D-081E87BC824B}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A}.Release|Any CPU.Build.0 = Release|Any CPU
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E}.Release|Any CPU.Build.0 = Release|Any CPU
		{24247038-FFC3-4327-A553-08F147DEE2E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24247038-FFC3-4327-A553-08F147DEE2E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24247038-FFC3-4327-A553-08F147DEE2E7}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{24247038-FFC3-4327-A553-08F147DEE2E7}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{24247038-FFC3-4327-A553-08F147DEE2E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24247038-FFC3-4327-A553-08F147DEE2E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{208A0554-3534-4CFF-91CB-4B00E7FBC567}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{208A0554-3534-4CFF-91CB-4B00E7FBC567}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{208A0554-3534-4CFF-91CB-4B00E7FBC567}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{208A0554-3534-4CFF-91CB-4B00E7FBC567}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{208A0554-3534-4CFF-91CB-4B00E7FBC567}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{208A0554-3534-4CFF-91CB-4B00E7FBC567}.Release|Any CPU.Build.0 = Release|Any CPU
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D}.Release|Any CPU.Build.0 = Release|Any CPU
		{ACE65369-0B03-4906-B17F-368A29E42559}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ACE65369-0B03-4906-B17F-368A29E42559}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ACE65369-0B03-4906-B17F-368A29E42559}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{ACE65369-0B03-4906-B17F-368A29E42559}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{ACE65369-0B03-4906-B17F-368A29E42559}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ACE65369-0B03-4906-B17F-368A29E42559}.Release|Any CPU.Build.0 = Release|Any CPU
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3306660-F615-46D6-8D2C-9BF90A3D696F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3306660-F615-46D6-8D2C-9BF90A3D696F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3306660-F615-46D6-8D2C-9BF90A3D696F}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{A3306660-F615-46D6-8D2C-9BF90A3D696F}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{A3306660-F615-46D6-8D2C-9BF90A3D696F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3306660-F615-46D6-8D2C-9BF90A3D696F}.Release|Any CPU.Build.0 = Release|Any CPU
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6}.Release|Any CPU.Build.0 = Release|Any CPU
		{4F165CFE-46DA-4954-9571-B778F669FFB0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4F165CFE-46DA-4954-9571-B778F669FFB0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4F165CFE-46DA-4954-9571-B778F669FFB0}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{4F165CFE-46DA-4954-9571-B778F669FFB0}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{4F165CFE-46DA-4954-9571-B778F669FFB0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4F165CFE-46DA-4954-9571-B778F669FFB0}.Release|Any CPU.Build.0 = Release|Any CPU
		{C200FA70-EF37-427E-B168-09D067789B72}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C200FA70-EF37-427E-B168-09D067789B72}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C200FA70-EF37-427E-B168-09D067789B72}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{C200FA70-EF37-427E-B168-09D067789B72}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{C200FA70-EF37-427E-B168-09D067789B72}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C200FA70-EF37-427E-B168-09D067789B72}.Release|Any CPU.Build.0 = Release|Any CPU
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A}.Release|Any CPU.Build.0 = Release|Any CPU
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C}.Release|Any CPU.Build.0 = Release|Any CPU
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697}.Release|Any CPU.Build.0 = Release|Any CPU
		{8B465D25-D0B2-4143-BD72-142AF72B2168}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8B465D25-D0B2-4143-BD72-142AF72B2168}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8B465D25-D0B2-4143-BD72-142AF72B2168}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{8B465D25-D0B2-4143-BD72-142AF72B2168}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{8B465D25-D0B2-4143-BD72-142AF72B2168}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8B465D25-D0B2-4143-BD72-142AF72B2168}.Release|Any CPU.Build.0 = Release|Any CPU
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE}.Release|Any CPU.Build.0 = Release|Any CPU
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A}.Quickly|Any CPU.ActiveCfg = Quickly|Any CPU
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A}.Quickly|Any CPU.Build.0 = Quickly|Any CPU
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{9F638471-879A-4602-A417-18B18BCCF6DE} = {E7D3F41C-D0A9-4CA1-BA63-C87EDA1050E8}
		{006461A9-5699-4AE3-AA06-9A587887CB34} = {9F638471-879A-4602-A417-18B18BCCF6DE}
		{4433EF75-7336-4345-A937-F165D96A4B63} = {9F638471-879A-4602-A417-18B18BCCF6DE}
		{C7E65EC3-3396-427A-91C6-FE425F24E2D3} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{366D8D7A-B27C-4CCD-A667-0ACBA7D1E2A5} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{5B36390A-FE9F-4170-9286-50060F759E4D} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{3E14E364-C511-4A51-AB3D-97113244E55B} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{FD3E5918-B6EF-4B2E-8B11-F225B35721D8} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{E854C305-B084-4C8F-8B90-DC048598BE32} = {E7D3F41C-D0A9-4CA1-BA63-C87EDA1050E8}
		{251C9FAB-8367-4810-99AF-C9CCC00A6676} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{44BB7D43-77C4-4585-93E6-B74DC362D0D0} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{F652F1E6-E0B3-4EA8-B602-E26FFC8AC43F} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{A8B6112B-5ED2-4D21-8F36-31F2D9F83436} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{7F4FBDD4-996D-485A-84F3-24929AD7EA64} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{B4CA04CF-A45F-444A-B122-5821B353FF0C} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{33A64402-D445-4FC5-94AF-2CC93E1F246A} = {E7D3F41C-D0A9-4CA1-BA63-C87EDA1050E8}
		{2485B8A1-7868-430E-BD97-2B20A651A83C} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{F4C1998D-10FE-4C8B-B628-0F3C88B80817} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{CCFC8667-2D2C-473C-93F4-CA8DF79B13DB} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{A63788C5-6C95-466F-875F-B07A24535FB3} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{AFF22358-3748-4114-B6B6-3CDB5CE90EB2} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{44E3EC37-8309-4415-A947-E1D86D699F5B} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{B8020CCC-EBC8-4634-8972-7921FD2ADF00} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{C2DC7367-1D83-4A7A-8D14-3E25A7D97C98} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{374F3A89-21E4-48EB-A9F3-F564EEABFC4A} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{74E3475F-598D-44A2-863F-EE1C98A94747} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{19511181-670B-4494-AF8C-27508344DF2C} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{E218ECCA-EE8A-41AD-A404-2FCFCC6B4F90} = {D9AC39F5-618F-49D3-8F2B-4FB12B20C669}
		{D5857E78-C4E5-42DD-B878-1582A7D76536} = {E854C305-B084-4C8F-8B90-DC048598BE32}
		{EE49C1C8-BD79-40CA-B5CD-FA71B8FA7317} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{E2115F64-1B86-4AF3-8C04-D1367B19940A} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{EED08377-3393-478E-AEC5-FE293A2B5072} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{25B09CC5-534D-453E-88BE-3BB57219C87F} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{37A682A3-6280-430E-A53C-65CF92EE3F37} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{B3A278A9-DD6C-4F27-A312-263DD5CE9954} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{EEB299CB-DC03-4A1E-8EF3-8CBA759CC801} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{BCD968D8-245E-48D0-BFA6-0B55AB3F5C8D} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{3F969BC8-5D69-47C7-BE1E-4EA37E8F6925} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{1D6923B4-3A61-4A66-86A4-78EDDC33814C} = {CCFC8667-2D2C-473C-93F4-CA8DF79B13DB}
		{F7412AE4-9186-4039-9A2B-0D825EE7F0D8} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{8D6C143B-14DC-452E-BEF3-C5F7E45B68DF} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{CD1440E9-26DD-41F0-A6AD-4C685874444D} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{7029AB24-285C-40DA-8EFF-6C68F3CC8295} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{615A75D5-991E-48C3-B6C2-3F8C7D5F506C} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{D81DF3D2-1AE6-4445-8647-5E190C3589F9} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{C7596C4F-DB55-4716-9380-F9F6BC10AD33} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{CE4B9DE7-734D-41D6-B015-326C9E077BB6} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{09CC9881-FA45-49DC-B1C5-BB16330E4C92} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{C9C5C980-7DCA-41F1-B51E-AF50AB65D052} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{B99B6C63-F1AA-47ED-8B26-8CC88905536B} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{9EE1883D-A2D6-44D1-A773-00FA793D6C15} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{471401A2-E83C-4B43-9F0B-E582FD034BE0} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{C2E0970C-3467-443B-989E-DAD1D3F7F1F4} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{F9118BA4-C399-4049-AF39-6E978CB5411C} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{4D0684CB-AEB3-4BA5-81E2-64908F018BE7} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{C0864337-B3BB-4419-B5CC-64BB5CDDCDDE} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{1E2C554C-641E-4D53-865A-818F3DD8E2B4} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{2663721C-B547-4A79-BD7C-73630C3AD074} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{50DAA4E4-2FB7-4536-AB95-0DB29AD6CA41} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{B93C2054-5064-410C-9981-4DC43FB10C31} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{339F1BFC-03EE-400C-AC18-8DDE1AD7A497} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{C7CD0225-5576-40FC-860A-F28FCE8E418F} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{27F542FB-67CC-4DAC-944D-081E87BC824B} = {C0864337-B3BB-4419-B5CC-64BB5CDDCDDE}
		{EE121BA0-6B45-49E5-9AC1-B3A8C10ACC7A} = {8D6C143B-14DC-452E-BEF3-C5F7E45B68DF}
		{40A1C9BD-5C65-4DEB-AD79-CDC411FB3E38} = {F4C1998D-10FE-4C8B-B628-0F3C88B80817}
		{3B463BE9-C312-4EA5-B505-F8CBB0111257} = {33A64402-D445-4FC5-94AF-2CC93E1F246A}
		{D1E102D3-5341-4A82-86EF-9CBD6AD4087E} = {3B463BE9-C312-4EA5-B505-F8CBB0111257}
		{24247038-FFC3-4327-A553-08F147DEE2E7} = {3B463BE9-C312-4EA5-B505-F8CBB0111257}
		{208A0554-3534-4CFF-91CB-4B00E7FBC567} = {3B463BE9-C312-4EA5-B505-F8CBB0111257}
		{B918A11F-BFFB-4CCD-89FD-3505DF65513D} = {3B463BE9-C312-4EA5-B505-F8CBB0111257}
		{ACE65369-0B03-4906-B17F-368A29E42559} = {3B463BE9-C312-4EA5-B505-F8CBB0111257}
		{0D7D1B5D-CC9C-4117-BA40-4A75700D7A1D} = {3B463BE9-C312-4EA5-B505-F8CBB0111257}
		{A3306660-F615-46D6-8D2C-9BF90A3D696F} = {4433EF75-7336-4345-A937-F165D96A4B63}
		{0BF43D1C-CEA1-4237-8CEB-FC2842A627A6} = {19511181-670B-4494-AF8C-27508344DF2C}
		{4F165CFE-46DA-4954-9571-B778F669FFB0} = {19511181-670B-4494-AF8C-27508344DF2C}
		{C200FA70-EF37-427E-B168-09D067789B72} = {19511181-670B-4494-AF8C-27508344DF2C}
		{B37E32DA-C637-428D-A863-6C4CBB27ED9A} = {19511181-670B-4494-AF8C-27508344DF2C}
		{BD89DEF2-8059-4A48-B7DD-5D311E51EA6C} = {19511181-670B-4494-AF8C-27508344DF2C}
		{78CF7DFA-0FBB-46BA-8981-D9A7662C6697} = {19511181-670B-4494-AF8C-27508344DF2C}
		{8B465D25-D0B2-4143-BD72-142AF72B2168} = {19511181-670B-4494-AF8C-27508344DF2C}
		{2A8962A4-02BE-452C-AA1A-CB2F0B5E14AE} = {19511181-670B-4494-AF8C-27508344DF2C}
		{8D3AB7FC-A649-4EDD-B2B2-60B1751854DE} = {006461A9-5699-4AE3-AA06-9A587887CB34}
		{F7041D17-6554-4C43-8E61-1209B1B5CF8A} = {4433EF75-7336-4345-A937-F165D96A4B63}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8B57F97C-22A0-461C-8B0B-E22024E686D7}
	EndGlobalSection
EndGlobal

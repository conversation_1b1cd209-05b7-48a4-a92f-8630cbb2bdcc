<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>判断项目信息/Judge project</span>
        </div>
      </template>

      <ApprovalLog :logs="approvalLogs" v-if="approvalLogs!=null&&approvalLogs.length>0"/>
      <FormCanvas :formSchema="formSchema" render-mode="view"/>
      <similarity-projects
          ref="similarityProjectsRef"
          v-if="formSchema"
          :businessId="String(businessId)"
          :formDefinition="formSchema"/>
    </el-card>

    <el-dialog
        v-model="visible" :title="action=='assign'?'分配':action=='reject'?'退回':action=='terminate'?'终止项目':''"

    >
      <el-form
          scroll-to-error
          :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
          ref="formRef"
          :model="judgeProjectDto"
          :rules="rules"
          label-width="200px"
      >


        <el-form-item
            label="请指派高级审核员"
            prop="userId"
            v-if="action=='assign'"
        >
          <el-select
              v-model="judgeProjectDto.userId"
              filterable
              clearable
              :teleported="true"
              popper-class="project-judge-select-popper"
              :popper-options="{ strategy: 'fixed' }"
              placeholder="请选择"
              style="width: 500px !important;"
          >
            <el-option
                v-for="user in secondaryAuditors"
                :key="user.key+'-'+user.positionCode"
                :label="user.realName + '[' + user.username + ']'+'['+user.organizationNamePath+']'+'['+user.positionName+']'"
                :value="user.key"
            >
              <template #default>
                <div>
                  <div>{{ user.realName }} [{{ user.username }}]{{ user.organizationNamePath }} [{{
                      user.positionName
                    }}]
                  </div>
                  <div class="summary">
                    待分配:{{ getUserStatistics(user.key, 'Level2PendingAssign') }} 
                    待审核:{{ getUserStatistics(user.key, 'Level2PendingReview') }}
                  </div>
                </div>
              </template>

            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="终止原因" prop="terminationCause" v-if="action=='terminate'">
          <el-select v-model="judgeProjectDto.terminationCause"
                     :teleported="true"
                     popper-class="project-judge-select-popper"
                     :popper-options="{ strategy: 'fixed' }"

                     @change="judgeProjectDto.description=(judgeProjectDto.terminationCause=='NonTraditionalProject'?'非传统医学项目':judgeProjectDto.terminationCause=='MultipleSubmissions'?'重复提交':'') "
          >
            <el-option label="非传统医学项目" value="NonTraditionalProject"/>
            <el-option label="重复提交" value="MultipleSubmissions"/>
            <el-option label="其他" value="Other"/>
          </el-select>
        </el-form-item>
        <el-form-item label="审核意见" prop="description">
          <el-input
              type="textarea"
              v-model="judgeProjectDto.description"
              :rows="8"
              clearable
              placeholder="请填写审核意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div style="text-align: center">
          <el-button @click="handleAssign" type="primary" :loading="loading" v-if="action=='assign'"
          >分配/Assign
          </el-button
          >
          <el-button @click="handleReject" type="warning" :loading="loading" v-if="action=='reject'">
            退回给发起人/Return
          </el-button>

          <el-button @click="handleTerminate" type="danger" :loading="loading" v-if="action=='terminate'">
            终止项目/Terminate
          </el-button>
        </div>
      </template>
    </el-dialog>

  </div>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button @click="action='assign';judgeProjectDto.description='';rules=confirmRules;visible=true;" type="primary"
               :loading="loading"
    >分配/Assign
    </el-button
    >
    <el-button @click="action='reject';judgeProjectDto.description='';rules=rejectRules;visible=true;" type="warning"
               :loading="loading">
      退回给发起人/Return
    </el-button>

    <el-button
        @click="action='terminate';judgeProjectDto.description='';judgeProjectDto.terminationCause='';rules=terminateRules;visible=true;"
        type="danger" :loading="loading">
      终止项目/Terminate
    </el-button>


    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, computed, nextTick} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules, ElMessageBox} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {getProject} from "@/api/itmctr";
import {getSummarizedData, judgeProject, judgeRejectProject, judgeTerminateProject} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";

import {UserDto} from "@/dtos/rbac-mgt.dto";

import {getManagedPositionUsers} from "@/api/rbac-mgt";
import {JudgeProjectDto} from "@/dtos/itmctr";
import ApprovalLog from "@/views/project/components/ApprovalLog.vue";
import SimilarityProjects from "@/views/project/components/SimilarityProjects.vue";
import {getValueByDefinition} from "@/utils/dynamic-form";
import {Loading} from "@element-plus/icons-vue";

const router = useRouter();
const route = useRoute();

const publicTitle = ref<any>({});

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const secondaryAuditors = ref<UserDto[]>([]);

// 用户统计数据状态管理
const userStatistics = ref<Map<string, Map<string, number | string>>>(new Map());

const judgeProjectDto = ref<JudgeProjectDto>({
  userId: undefined,
});

const action = ref<string>("judge");

const visible = ref(false);

const formRef = ref<FormInstance>();


const confirmRules: FormRules = {
  traditionalProject: [
    {required: true, message: "请选择是否为传统项目", trigger: "blur"},
  ],
  userId: [{required: true, message: "请选择高级审核员", trigger: "blur"}],
};

const rejectRules: FormRules = {
  description: [{required: true, message: "请填写审核意见", trigger: "blur"}],
};
const terminateRules: FormRules = {
  description: [{required: true, message: "请填写审核意见", trigger: "blur"}],
  terminationCause: [{required: true, message: "请选择终止原因", trigger: "blur"}]
};

const rules = ref(confirmRules);

const businessId = computed(() => {
  return route.params.businessId;
});
const version = ref<string>('');

// 获取用户统计数据的显示值
const getUserStatistics = (userId: string, statisticsCode: string): string | number => {
  const userStats = userStatistics.value.get(userId);
  if (!userStats) {
    return '-';
  }
  const value = userStats.get(statisticsCode);
  return value !== undefined ? value : '-';
};

// 异步加载单个用户的统计数据
const loadUserStatistics = async (userId: string) => {
  const positionCode = 'CHECKER_LEVEL_2';
  const statisticsCodes = ['Level2PendingAssign', 'Level2PendingReview'];
  
  // 初始化用户统计数据
  if (!userStatistics.value.has(userId)) {
    userStatistics.value.set(userId, new Map());
  }
  
  const userStats = userStatistics.value.get(userId)!;
  
  // 设置初始加载状态
  statisticsCodes.forEach(code => {
    userStats.set(code, '...');
  });
  
  // 异步加载每个统计数据，不等待所有数据加载完成
  statisticsCodes.forEach(async (statisticsCode) => {
    try {
      const data = await getUserSummarizedData(userId, positionCode, statisticsCode);
      userStats.set(statisticsCode, data || 0);
    } catch (error) {
      console.error(`获取用户 ${userId} 的 ${statisticsCode} 统计数据失败:`, error);
      userStats.set(statisticsCode, 0);
    }
  });
};

// 异步加载所有用户的统计数据
const loadAllUserStatistics = () => {
  if (secondaryAuditors.value.length === 0) return;
  
  // 逐个加载用户统计数据，让每个用户的数据加载完成后立即更新显示
  for (const user of secondaryAuditors.value) {
    // 不等待前一个用户加载完成，直接开始加载下一个用户
    loadUserStatistics(user.key);
  }
};

const loadSecondaryAuditors = async () => {
  try {
    const response = await getManagedPositionUsers("CHECKER_LEVEL_2");
    secondaryAuditors.value = response.data.flatMap(q => q.users.map(u => {
      //如果namePath开头有"/"则去掉开头的"/"
      u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
      u.positionName = "高级审核员";
      u.positionCode = "CHECKER_LEVEL_2";
      return u;
    }));
    
    // 异步加载用户统计数据，不阻塞主流程
    loadAllUserStatistics();
  } catch (error) {
    console.error("获取高级审核员失败", error);
    ElMessage.error("获取高级审核员失败");
  }
};

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;
      publicTitle.value = getValueByDefinition(response.data, 'public_title');
      version.value = response.data.version;
      loading.value = false;
      if (loadingInstance) loadingInstance.close();
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
    loading.value = false;
    if (loadingInstance) loadingInstance.close();
  }
};
const handleTerminate = async () => {

  rules.value = terminateRules;
  await nextTick();

  loading.value = true;
  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }


  try {
    let judgeTerminateProjectDto = {
      description: judgeProjectDto.value.description
    };
    await judgeTerminateProject(String(businessId.value), judgeTerminateProjectDto);
    router.go(-1);
  } catch (error) {
    console.error("终止项目失败", error);
    ElMessage.error("终止项目失败");
  } finally {
    loading.value = false;
  }
};
const handleReject = async () => {

  rules.value = rejectRules;
  await nextTick();

  loading.value = true;
  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }


  try {
    let judgeRejectProjectDto = {
      description: judgeProjectDto.value.description
    };
    await judgeRejectProject(String(businessId.value), judgeRejectProjectDto);
    router.go(-1);
  } catch (error) {
    console.error("退回项目失败", error);
    ElMessage.error("退回项目失败");
  } finally {
    loading.value = false;
  }
}
const handleBack = () => {
  router.go(-1);
};
const similarityProjectsRef = ref();

const handleAssign = async () => {

  rules.value = confirmRules;
  await nextTick();

  loading.value = true;
  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  if (!similarityProjectsRef.value.initialized) {
    await ElMessageBox.confirm('相似项目清单暂未加在完成，是否继续提交？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).catch((error) => {
      loading.value = false;
      throw error;
    });
  }
  if (similarityProjectsRef.value.cnProjects.length > 0 || similarityProjectsRef.value.enProjects.length > 0) {
    await ElMessageBox.confirm('存在相似项目，是否继续提交？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }).catch((error) => {
      loading.value = false;
      throw error;
    });
  }

  try {
    await judgeProject(String(businessId.value), judgeProjectDto.value);
    router.go(-1);
  } catch (error) {
    console.error("判断项目失败", error);
    ElMessage.error("判断项目失败");
  } finally {
    loading.value = false;
  }
};
const approvalLogs = computed(() => {
  try {
    return JSON.parse(formSchema.value?.formData?.ApprovalHistory || "[]");
  } catch {
    return [];
  }
});
const getUserSummarizedData = async (userId: string, positionCode: string, statisticsCode: string) => {
  try {
    const response = await getSummarizedData(userId, positionCode, statisticsCode);
    return response.data;
  } catch (error) {
    console.error("获取项目摘要失败", error);
    ElMessage.error("获取项目摘要失败");
  }
};

// 组件挂载时加载表单
onMounted(async () => {
  // 并行加载项目数据和用户列表，不相互阻塞
  await Promise.all([loadProject(), loadSecondaryAuditors()]);
});
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}

</style>

<!-- 使用非 scoped 样式，确保弹层类名能作用到挂载在 body 下的 popper 元素 -->
<style lang="scss">
.project-judge-select-popper {
  z-index: 9999 !important; // 高于 el-dialog 的 2000+，避免被遮挡
}

.el-select-dropdown__item {
  height: 100% !important;
}

.summary {
  line-height: 18px;
  font-size: 12px;
  color: #606266;
}
</style>

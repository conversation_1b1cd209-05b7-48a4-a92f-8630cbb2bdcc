<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">
    <el-card class="table-card">
      <template #header>
        <div class="card-header">
          <span>分审项目信息/Assign and review project</span>
        </div>
      </template>
      <ApprovalLog :logs="approvalLogs"/>
      <FormCanvas :formSchema="formSchema" render-mode="view"/>
      <el-form
          scroll-to-error
          :scroll-into-view-options="{ behavior: 'smooth', block: 'center' }"
          ref="formRef"
          :model="assignReviewProjectDto"
          :rules="rules"
          label-width="400px"
      >
        <el-form-item label="当前初级审核员" prop="currentFourthApprovalUser" v-if="currentFourthApprovalUserId">
          <span>{{ currentFourthApprovalUserName }} ({{ currentFourthApprovalUserAccount }})</span>
        </el-form-item>
        <el-form-item
            label="请指派初级审核员"
            prop="userId"
        >
          <el-select
              v-model="assignReviewProjectDto.userId"
              filterable
              clearable
              placeholder="请选择"
              style="width:500px !important;"
          >
            <el-option
                v-for="user in auditors"
                :key="user.key"
                :label="user.realName + '[' + user.username + ']'+'['+user.organizationNamePath+']'+'['+user.positionName+']'"
                :value="user.key"
            >
              <template #default>
                <div>
                  <div>{{ user.realName }} [{{ user.username }}]{{ user.organizationNamePath }} [{{
                      user.positionName
                    }}]
                  </div>
                  <div class="summary">待审核:{{ getUserStatistics(user.key, 'Level4PendingReview') }}</div>
                </div>
              </template>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="审核意见" prop="description">
          <el-input
              type="textarea"
              ref="descriptionRef"
              v-model="assignReviewProjectDto.description"
              :rows="8"
              clearable
              placeholder="请填写审核意见"
          />
        </el-form-item>
      </el-form>
    </el-card>
  </div>
  <!-- 悬浮操作按钮 -->
  <div class="floating-actions">
    <el-button @click="handleSave" type="primary" :loading="loading"
    >保存/Save
    </el-button
    >
    <el-button @click="handleBack" :loading="loading">返回/Back</el-button>
  </div>
</template>

<script setup lang="ts">
import {ref, onMounted, computed} from "vue";
import {useRouter, useRoute} from "vue-router";
import {ElMessage, ElLoading, FormInstance, FormRules} from "element-plus";
import FormCanvas from "@/views/form-management/form-list/components/FormCanvas.vue";

import {getProject} from "@/api/itmctr";
import {assignReviewProject, getSummarizedData} from "@/api/itmctr-mgt";
import {FormDefinitionDto} from "@/dtos/dynamic-form.dto";


import {getManagedPositionUsers} from "@/api/rbac-mgt";
import {AssignReviewProjectDto} from "@/dtos/itmctr";
import ApprovalLog from "@/views/project/components/ApprovalLog.vue";

const router = useRouter();
const route = useRoute();

// 表单数据
const formSchema = ref<FormDefinitionDto>();

const loading = ref<boolean>(false);

const auditors = ref<any[]>([]);

// 用户统计数据状态管理
const userStatistics = ref<Map<string, Map<string, number | string>>>(new Map());

const currentFourthApprovalUserName = ref<string>("");
const currentFourthApprovalUserAccount = ref<string>("");
const currentFourthApprovalUserId = ref<string>("");


const assignReviewProjectDto = ref<AssignReviewProjectDto>({
  userId: undefined,
  description: undefined
});

const formRef = ref<FormInstance>();
const rules: FormRules = {
  userId: [{required: true, message: "请选择初级审核员", trigger: "blur"}],
};

const businessId = computed(() => {
  return route.params.businessId;
});

// 获取用户统计数据的显示值
const getUserStatistics = (userId: string, statisticsCode: string): string | number => {
  const userStats = userStatistics.value.get(userId);
  if (!userStats) {
    return '-';
  }
  const value = userStats.get(statisticsCode);
  return value !== undefined ? value : '-';
};

// 异步加载单个用户的统计数据
const loadUserStatistics = async (userId: string) => {
  const positionCode = 'CHECKER_LEVEL_4';
  const statisticsCode = 'Level4PendingReview';
  
  // 初始化用户统计数据
  if (!userStatistics.value.has(userId)) {
    userStatistics.value.set(userId, new Map());
  }
  
  const userStats = userStatistics.value.get(userId)!;
  
  // 设置初始加载状态
  userStats.set(statisticsCode, '...');
  
  try {
    const data = await getUserSummarizedData(userId, positionCode, statisticsCode);
    userStats.set(statisticsCode, data || 0);
  } catch (error) {
    console.error(`获取用户 ${userId} 的 ${statisticsCode} 统计数据失败:`, error);
    userStats.set(statisticsCode, 0);
  }
};

// 异步加载所有用户的统计数据
const loadAllUserStatistics = () => {
  if (auditors.value.length === 0) return;
  
  // 逐个加载用户统计数据，让每个用户的数据加载完成后立即更新显示
  for (const user of auditors.value) {
    // 不等待前一个用户加载完成，直接开始加载下一个用户
    loadUserStatistics(user.key);
  }
};

// 获取用户统计数据的方法
const getUserSummarizedData = async (userId: string, positionCode: string, statisticsCode: string) => {
  try {
    const response = await getSummarizedData(userId, positionCode, statisticsCode);
    return response.data;
  } catch (error) {
    console.error("获取项目摘要失败", error);
    ElMessage.error("获取项目摘要失败");
  }
};

const approvalLogs = computed(() => {
  try {
    return JSON.parse(formSchema.value?.formData?.ApprovalHistory || "[]");
  } catch {
    return [];
  }
});
const loadAuditors = async () => {

  let loadingInstance: any;
  try {

    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取四级审核员
    const fourthAuditors = await getManagedPositionUsers("CHECKER_LEVEL_4");

    // 合并四级审核员
    auditors.value = [

      ...(fourthAuditors.data.flatMap(q => q.users.map(u => {
        u.organizationCode = q.organization.code;
        u.organizationNamePath = q.organization.namePath.startsWith("/") ? q.organization.namePath.substring(1) : q.organization.namePath;
        u.positionName = "初级审核员";
        u.positionCode = "CHECKER_LEVEL_4";
        return u;
      })) || []),
    ];

    loading.value = false;
    if (loadingInstance) loadingInstance.close();
    
    // 异步加载用户统计数据，不阻塞主流程
    loadAllUserStatistics();
  } catch (error: any) {
    console.error("获取初级审核员失败:", error);
    ElMessage.error(`获取初级审核员失败: ${error.message || error}`);
    loading.value = false;
    if (loadingInstance) loadingInstance.close();
  }
};

const loadProject = async () => {
  let loadingInstance: any;
  try {
    loading.value = true;
    loadingInstance = ElLoading.service({
      lock: true,
      text: "正在加载...",
      background: "rgba(0, 0, 0, 0.3)",
    });
    // 获取表单定义，formCode固定为test
    const response = await getProject(String(businessId.value));
    console.log("获取表单定义成功:", response);

    // 如果后端返回了表单定义，则更新formSchema
    if (response.data && response.data) {
      formSchema.value = response.data;

      currentFourthApprovalUserName.value = formSchema.value.formData["fourthApprovalUserName"];
      currentFourthApprovalUserAccount.value = formSchema.value.formData["FourthApprovalUserAccount"];
      currentFourthApprovalUserId.value = formSchema.value.formData["FourthApprovalUserId"];
      loading.value = false;
      if (loadingInstance) loadingInstance.close();
    }
  } catch (error: any) {
    console.error("获取表单定义失败:", error);
    ElMessage.error(`获取表单定义失败: ${error.message || error}`);
    loading.value = false;
    if (loadingInstance) loadingInstance.close();
  }
};
const handleBack = () => {
  router.go(-1);
};
const handleSave = async () => {
  loading.value = true;
  if (!formRef.value) {
    return;
  }

  try {
    await formRef.value.validate();
  } catch (error) {
    loading.value = false;
    return;
  }

  try {


    await assignReviewProject(String(businessId.value), assignReviewProjectDto.value);
    router.go(-1);
  } catch (error) {
    console.error("分审项目失败", error);
    ElMessage.error("分审项目失败");
  } finally {
    loading.value = false;
  }
};

// 组件挂载时加载表单
onMounted(async () => {
  // 并行加载用户列表和项目数据，不相互阻塞
  await Promise.all([loadAuditors(), loadProject()]);
});
</script>

<style lang="scss" scoped>


.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
  }

  .form-actions {
    display: flex;
    gap: 10px;
  }
}

.card-header {
  color: #d77680;
  font-weight: bolder;
}
.el-select-dropdown__item {
  height: 100% !important;
}
.summary {
  line-height: 18px;
  font-size: 12px;
  color: #606266;
}
</style>

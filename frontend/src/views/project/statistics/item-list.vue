<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="app-container">


    <!-- 选择项目弹窗 -->
    <SelectionDialog
        v-model="selectionDialogVisible"
        title="选择项目"
        :table-columns="selectionTableColumns"
        :search-form-items="selectionSearchFormItems"
        :fetch-data="fetchProjectData"
        @confirm="handleSelectionConfirm"
        :table-height="380"
        width="1400px"
    />

    <!-- 搜索表单 -->
    <SearchForm
        :form-items="searchFormItems"
        :initial-values="queryParams"
        @search="handleSearch"
        @reset="handleReset"
    />

    <!-- 数据表格 -->
    <DataTable
        title="统计明细"
        :data="list"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :max-height="540"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
        @sort-change="handleSortChange"
        :default-sort="{ prop: queryParams.$sortBy!, order: queryParams.$orderBy === 'asc' ? 'ascending' : 'descending' }"
    >
      <template #toolbar>
        <el-button @click="handleBack">
          <el-icon>
            <ArrowLeft/>
          </el-icon>
          返回
        </el-button>
        <el-button type="default" @click="selectionDialogVisible=true;">
          <el-icon>
            <Plus/>
          </el-icon>
          添加
        </el-button>
        <el-button
            type="primary"
            @click="handleExport"
        >
          <el-icon>
            <Download/>
          </el-icon>
          导出
        </el-button>
        <el-button type="danger" @click="handleReStatistics">
          <el-icon>
            <Refresh/>
          </el-icon>
          重新统计
        </el-button>
        <el-button type="danger" @click="handleReExtractProjectAll">
          <el-icon>
            <Refresh/>
          </el-icon>
          强制重新抽取
        </el-button>

      </template>

      <template #status="{row}">
        <el-tag v-if="row.item.status==1" type="info">未开始</el-tag>
        <el-tag v-else-if="row.item.status==2" type="info">抽取中</el-tag>
        <el-tag v-else-if="row.item.status==3" type="success">已抽取</el-tag>
        <el-tag v-else-if="row.item.status==4" type="danger">抽取失败</el-tag>
      </template>
      <template #release_time="{row}">
        <template v-if="row.project!=null">
          <el-text
              type="danger"
              v-if="row.project.release_time>batchInfo.endTime||row.project.release_time<batchInfo.startTime">
            {{ formatDateTimeOffsetToLocal(row.project.release_time) }}
          </el-text>
          <el-text v-else>
            {{ formatDateTimeOffsetToLocal(row.project.release_time) }}
          </el-text>

        </template>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button
            type="danger"
            link
            @click="handleRemoveBatchItem(row)"
        >
          <el-icon>
            <Delete/>
          </el-icon>
          删除
        </el-button>
        <el-button
            type="primary"
            link
            @click="handleReExtractProject(row)"
        >
          <el-icon>
            <Refresh/>
          </el-icon>
          重新抽取
        </el-button>

      </template>
    </DataTable>


  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted, computed} from "vue";
import {Plus, Edit, Delete, ArrowLeft, Download, Refresh} from "@element-plus/icons-vue";
import {ElMessage} from "element-plus";
import SearchForm, {FormItem} from "@/components/common/SearchForm.vue";
import DataTable, {TableColumn} from "@/components/common/DataTable.vue";
import SelectionDialog from "@/components/common/SelectionDialog.vue";
import {
  addBatchItem,
  exportStatistics, getBatch,
  getBatchItemsPage,
  getProjectStatisticsQuery, reExtractProject,
  removeBatchItem,
  reStatistics
} from "@/api/itmctr-mgt";
import {
  ProjectInfoKey,
  ProjectStatisticsDto,
  ProjectStatisticsItemDto,
  ProjectStatisticsItemQueryParams
} from "@/dtos/itmctr";
import {formatDateTimeOffsetToLocal} from "@/utils/date";
import {getDownloadToken} from "@/api/files";
import {getAuthDownloadUrl} from "@/utils/files";


// 搜索表单项配置
const searchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "注册号",
    prop: "registrationNumber",
    placeholder: "",
  },
  {
    type: "input",
    label: "注册题目(中文)",
    prop: "PublicTitleZh",
    placeholder: "",
  },
  {
    type: "input",
    label: "注册题目(英文)",
    prop: "PublicTitleEn",
    placeholder: "",
  },
]);

// 选择弹窗的搜索表单项配置
const selectionSearchFormItems = ref<FormItem[]>([
  {
    type: "input",
    label: "注册号",
    prop: "registrationNumber",
    placeholder: "请输入注册号",
  },
  {
    type: "input",
    label: "注册题目(中文)",
    prop: "publicTitleZh",
    placeholder: "请输入中文标题",
  },
  {
    type: "input",
    label: "注册题目(英文)",
    prop: "publicTitleEn",
    placeholder: "请输入英文标题",
  },
]);

// 选择弹窗的表格列配置
const selectionTableColumns = ref<TableColumn[]>([
  // {prop: "business_id", label: "项目ID", width: 150},
  {prop: "version", label: "版本号", width: 150},
  {prop: "registration_number", label: "注册号", width: 150},
  {prop: "publictitle_zh", label: "注册标题(中文)", width: 200},
  {prop: "publictitle_en", label: "注册标题(英文)", width: 200},
  {
    prop: "first_submit_time",
    label: "首次提交时间",
    width: 150,
    formatter: (row => formatDateTimeOffsetToLocal(row.first_submit_time))
  },
  {
    prop: "send_number_time",
    label: "注册时间",
    width: 150,
    formatter: (row => formatDateTimeOffsetToLocal(row.send_number_time))
  },
  {
    prop: "release_time",
    label: "发布时间",
    width: 150,
    formatter: (row => formatDateTimeOffsetToLocal(row.release_time))
  },
]);

// 表格列配置
const tableColumns = ref<TableColumn[]>([

  // {prop: "business_id", label: "项目ID", width: 150},
  {prop: "item.version", label: "版本号", width: 150},
  {prop: "project.registration_number", label: "注册号", sortable: true, width: 150},
  {prop: "project.publictitle_zh", label: "注册标题(中文)", width: 150},
  {prop: "project.publictitle_en", label: "注册标题(英文)", width: 150},
  {
    prop: "project.first_submit_time",
    sortable: true,
    label: "首次提交时间",
    width: 150,
    formatter: (row => row.project == null ? "" : formatDateTimeOffsetToLocal(row.project.first_submit_time))
  },
  {
    prop: "project.send_number_time",
    label: "注册时间",
    sortable: true,
    width: 150,
    formatter: (row => row.project == null ? "" : formatDateTimeOffsetToLocal(row.project.send_number_time))
  },
  {
    prop: "project.release_time",
    label: "发布时间",
    width: 150,
    slot: 'release_time'
  },
  {
    prop: "item.status",
    sortable: true,
    label: "抽取状态",
    width: 80,
    slot: 'status'
  },

]);

const loading = ref(false);
const total = ref(0);
const list = ref<ProjectStatisticsItemDto[]>([]);
const route = useRoute();
const router = useRouter();
const selectionDialogVisible = ref(false);
const batchInfo = ref<ProjectStatisticsDto>({} as ProjectStatisticsDto);

const queryParams = reactive<ProjectStatisticsItemQueryParams>({
  $pageIndex: 1,
  $pageSize: 100,
  publicTitle: "",
  registrationNumber: "",
  $sortBy: "project.registration_number",
  $orderBy: "desc",
});

onMounted(async () => {
  await getBatchInfo();
  await getList();
});
const getBatchInfo = async () => {
  await getBatch(route.params.batchId).then((response) => {
    batchInfo.value = response.data;
  });
};

// 获取项目数据（用于选择弹窗）
const fetchProjectData = async (params: any) => {
  // 这里需要根据实际的API接口来实现
  // 示例：调用获取项目列表的API
  try {

    const response = await getProjectStatisticsQuery(route.params.batchId as string, params);

    const {data} = response;

    return data;

  } catch (error) {
    console.error('获取项目数据失败', error);
    return {
      rows: [],
      totals: 0
    };
  }
};

// 处理选择确认
const handleSelectionConfirm = async (selectedRows: any[]) => {

  const projectInfoKeys = selectedRows.map((row: any) => {
    return {
      businessId: row.business_id,
      version: row.version
    } as ProjectInfoKey;
  });
  await addBatchItem(route.params.batchId as string, projectInfoKeys);

  await getList();
};

const getList = async () => {
  try {
    loading.value = true;
    const response = await getBatchItemsPage(route.params.batchId as string, queryParams);

    const {data} = response;
    list.value = data.rows || [];

    // 尝试多种方式转换
    let totalNumber = 0;

    if (data.totals !== undefined && data.totals !== null) {
      if (typeof data.totals === "number") {
        totalNumber = data.totals;
      } else if (typeof data.totals === "string") {
        // 尝试将字符串转换为数字
        totalNumber = parseInt(data.totals, 10) || 0;
      }
    }

    total.value = totalNumber;
  } catch (error) {
    console.error("获取列表失败", error);
    ElMessage.error("获取列表失败");
  } finally {
    loading.value = false;
  }
};

const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData);
  queryParams.$pageIndex = 1;
  getList();
};

const handleReset = (formData: any) => {
  Object.assign(queryParams, formData);
  getList();
};

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  getList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  getList();
};

const handleRemoveBatchItem = async (row: ProjectStatisticsItemDto) => {
  await ElMessageBox.confirm("确认要删除该统计项目吗？");

  await removeBatchItem(route.params.batchId as string, row.item.key).then((response) => {
    ElMessage.success("删除成功");
    getList();
  });
}
const handleExport = async () => {

  await ElMessageBox.confirm("确认要导出该批次吗？");

  let response = await exportStatistics(route.params.batchId as string);
  const fileDto = response.data;

  const downloadTokenResponse = await getDownloadToken(
      fileDto.fileId!
  );
  const downloadToken = downloadTokenResponse.data;
  const fileUrl = getAuthDownloadUrl(
      fileDto.fileId!,
      fileDto.fileName!,
      downloadToken
  );

  const a = document.createElement("a");
  a.href = fileUrl;
  a.target = "_blank";
  a.download = fileDto.fileName!;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}

const handleReStatistics = async () => {
  await ElMessageBox.confirm("确认要重新统计该批次吗？");
  await reStatistics(route.params.batchId as string).then((response) => {
    ElMessage.success("重新统计成功");
    getList();
  });
}
const handleBack = async () => {
  await router.push({path: `/project-statistics/list`});
}

const handleReExtractProject = async (row) => {
  await ElMessageBox.confirm("确认要重新抽取该项目吗？");
  await reExtractProject(route.params.batchId as string, true, [row.item.key]).then((response) => {
    ElMessage.success("重新抽取成功");
    getList();
  });
}
const handleReExtractProjectAll = async () => {
  await ElMessageBox.confirm("确认要重新抽取所有项目吗？");
  await reExtractProject(route.params.batchId as string, true, null).then((response) => {
    ElMessage.success("重新抽取成功");
    getList();
  });
}

// 处理排序事件
const handleSortChange = async (sort: { prop: string; order: "ascending" | "descending" | null } | undefined) => {
  if (sort && sort.prop && sort.order) {
    queryParams.$sortBy = sort.prop;
    queryParams.$orderBy = sort.order === "ascending" ? "asc" : "desc";
  } else {
    queryParams.$sortBy = "";
    queryParams.$orderBy = "asc";
  }
  await getList();
};
</script>

<style lang="scss" scoped>

</style>

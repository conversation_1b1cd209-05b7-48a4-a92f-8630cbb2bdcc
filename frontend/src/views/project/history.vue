<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <PublicNav :show-user-name="false" :show-lang="true"/>
  <div class="app-container">
    <!-- 搜索表单 -->


    <!-- 数据表格 -->
    <DataTable
        title="历史版本/Historical Versions"
        :data="projectSearchResultList"
        :columns="tableColumns"
        :loading="loading"
        :total="total"
        :current-page-prop="queryParams.$pageIndex"
        :page-size-prop="queryParams.$pageSize"
        @page-change="handleCurrentChange"
        @size-change="handleSizeChange"
    >
      <template #toolbar>
        <el-button type="text" @click="handleBack">
          <el-icon>
            <ArrowLeft/>
          </el-icon>
          返回/Back
        </el-button>
      </template>

      <!-- 状态列 -->
      <template #status="{ row }">
        <el-tag :type="getStatusTagType(row.status)">
          {{ $t(getStatusText(row.status)) }}
        </el-tag>
      </template>

      <!-- 操作列 -->
      <template #action="{ row }">
        <el-button type="primary" link @click="handleView(row.key)">
          <el-icon>
            <View/>
          </el-icon>
          查看/View
        </el-button>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, onMounted} from "vue";
import {useRouter, useRoute} from "vue-router";
import DataTable, {TableColumn} from "@/components/common/DataTable.vue";
import {View} from "@element-plus/icons-vue";
import {getHistoryPage} from "@/api/itmctr";
import {ProjectSearchResultDto} from "@/dtos/project.dto";
import {
  getStatusTagType,
  getStatusText,
  getFormDataValue,
} from "@/utils/dynamic-form";
import {formatDateString} from "@/utils/date";
import Navbar from '@/components/layout/Navbar.vue'
import PublicNav from "@/components/layout/PublicNav.vue";

const router = useRouter();
const route = useRoute();
const handleBack = () => {
  router.go(-1);
};
// props
const props = defineProps<{
  title: string;
  columns: Array<{
    prop: string;
    label: string;
    width?: number;
    placeholder?: string;
    search?: boolean;
  }>;
}>();


const tableColumns = ref<TableColumn[]>([
  {
    prop: "registration_number",
    label: {zh: "注册号", en: "Registration number"},
    width: 120,
    formatter: (row: any) =>
        formatProjectResultValue(row, "registration_number"),
  },
  {
    prop: "version",
    label: {zh: "版本号", en: "Version"},
    width: 120,
  },

  {
    prop: "release_time",
    label: {zh: "版本创建时间", en: "Date of Creation"},
    width: 100,
    formatter(row: any) {
      return formatDateString(getFormDataValue(row, "release_time"));
    },
  },
]);

// 查询参数
const queryParams = reactive<any>({
  $pageIndex: 1,
  $pageSize: 10,
  formCode: "PROJECT",
  dynamicQueries: {},
  businessId: route.params.businessId
});

const loading = ref(false);
const total = ref(0);

const projectSearchResultList = ref<ProjectSearchResultDto[]>([]);


onMounted(() => {
  fetchHistoryList();
});

const fetchHistoryList = async () => {
  try {
    loading.value = true;
    const response = await getHistoryPage(queryParams);
    const {data} = response;
    projectSearchResultList.value = data.rows || [];
    total.value = data.totals;
  } catch (error) {
    // ElMessage.error("获取表单实例列表失败");
  } finally {
    loading.value = false;
  }
};

function formatProjectResultValue(row: ProjectSearchResultDto, prop: keyof ProjectSearchResultDto) {
  return row[prop] ?? '';
}

const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val;
  fetchHistoryList();
};

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val;
  fetchHistoryList();
};

const handleView = (key: number) => {
  router.push({name: 'publicProjectView', params: {key: key}});
};

</script>

<style lang="scss" scoped>

</style>

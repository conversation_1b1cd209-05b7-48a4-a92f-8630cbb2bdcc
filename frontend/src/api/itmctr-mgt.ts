import {
    JudgeProjectDto,
    AssignProjectDto,
    AssignReviewProjectDto,
    AnnotationValue, ProjectStatisticsQueryParams, ProjectStatisticsDto, ProjectStatisticsItemDto,
    ProjectStatisticsItemQueryParams, FileDto, StatisticsQueryParams, ProjectInfoKey
} from "@/dtos/itmctr";
import request from '@/utils/request'
import {EditProjectRejectOperationDto, SendNumberDto} from "@/dtos/dynamic-form-mgt.dto";
import {getApiBaseUrl} from '@/config/env';
import {FormDefinitionDto, InternalPageQueryParams} from "@/dtos/dynamic-form.dto";
import {FormInstanceDataDto} from "@/api/itmctr";
import {PageResult} from "@/dtos";
import {ProjectSearchResultDto} from "@/dtos/project.dto";

const baseURL = getApiBaseUrl('itmctr-mgt');

export function judgeProject(businessId: string, data: JudgeProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/judge/${businessId}`,
        method: 'post',
        data
    })
}

export function judgeRejectProject(businessId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Project/judge/${businessId}/reject`,
        method: 'post',
        data
    })
}

export function judgeTerminateProject(businessId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Project/judge/${businessId}/termination`,
        method: 'post',
        data
    })
}

export function assignProject(businessId: string, data: AssignProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/assign/${businessId}`,
        method: 'post',
        data
    })
}

export function returnProjectLevel2(businessId: string, data: AssignProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/return-level2/${businessId}`,
        method: 'post',
        data
    })
}


export function assignReviewProject(businessId: string, data: AssignReviewProjectDto) {
    return request<boolean>({
        url: `${baseURL}/Project/assign-review/${businessId}`,
        method: 'post',
        data
    })
}

export function approvalProjectLevel4(businessId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level4`,
        method: 'post',
        data
    })
}

export function rejectProjectLevel4(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level4`,
        method: 'post',
        data: annotationValues
    })
}

export function saveProjectLevel4(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/save/${businessId}/level4`,
        method: 'post',
        data: annotationValues
    })
}

export function approvalProjectLevel3(businessId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level3`,
        method: 'post',
        data
    })
}

export function fixProjectLevel4(businessId: string) {
    return request<string>({
        url: `${baseURL}/Project/approval/${businessId}/level4/fix`,
        timeout: 100000,
        method: 'post',
    })
}


export function rejectProjectLevel3(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level3`,
        method: 'post',
        data: annotationValues
    })
}


export function approvalProjectLevel2(businessId: string, data: any) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level2`,
        method: 'post',
        data
    })
}

export function rejectProjectLevel2(businessId: string, annotationValues: Record<string, AnnotationValue | null>) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level2`,
        method: 'post',
        data: annotationValues
    })
}


export function approvalProjectLevel1(businessId: string, input: SendNumberDto) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level1`,
        method: 'post',
        data: input
    })
}

export function rejectProjectLevel1(businessId: string, input: SendNumberDto) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level1`,
        method: 'post',
        data: input
    })
}

export function approvalProjectLevel1edit(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/approval/${businessId}/level1/edit`,
        method: 'post'
    })
}

export function rejectProjectLevel1edit(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/reject/${businessId}/level1/edit`,
        method: 'post'
    })
}


export function editConfirmedProject(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/edit-confirmed/${businessId}`,
        method: 'post'
    })
}

export function editRejectedProject(businessId: string, dto: EditProjectRejectOperationDto) {
    return request<boolean>({
        url: `${baseURL}/Project/edit-rejected/${businessId}`,
        method: 'post',
        data: dto
    })
}

export function RecallProject(businessId: string) {
    return request<boolean>({
        url: `${baseURL}/Project/recall/${businessId}`,
        method: 'post'
    })
}

export function getPage(base: string, params: InternalPageQueryParams) {
    return request<FormInstanceDataDto>({
        url: `${baseURL}/Project/${base}`,
        method: 'get',
        params
    })
}

export function saveProject(businessId: string, version: string, formDefinition: FormDefinitionDto) {
    return request<boolean>({
        url: `${baseURL}/Project/save/${businessId}/${version}`,
        method: 'post',
        data: formDefinition
    })
}

export function getProjectWithVersion(businessId: string, version: string) {
    return request<FormDefinitionDto>({
        url: `${baseURL}/Project/named/${businessId}/${version}`,
        method: 'get'
    })
}

export function createBatch(category: string, start: Date, end: Date) {
    return request<string>({
        url: `${baseURL}/ProjectStatistics/create-batch`,
        method: 'post',
        data: {
            "category": category,
            "start": start,
            "end": end
        }
    })
}

export function getBatch(batchId: number) {
    return request<ProjectStatisticsDto>({
        url: `${baseURL}/ProjectStatistics/${batchId}`,
        method: 'get'
    })
}

export function getBatchPage(params: ProjectStatisticsQueryParams) {
    return request<PageResult<ProjectStatisticsDto>>({
        url: `${baseURL}/ProjectStatistics/get-batches`,
        method: 'get',
        params
    })
}

export function getBatchItemsPage(batchId: string, params: ProjectStatisticsItemQueryParams) {
    return request<PageResult<ProjectStatisticsItemDto>>({
        url: `${baseURL}/ProjectStatistics/get-batch-items/${batchId}`,
        method: 'get',
        params
    })
}


export function getProjectStatisticsQuery(batchId: string, params: StatisticsQueryParams) {
    return request<PageResult<ProjectSearchResultDto>>({
        url: `${baseURL}/ProjectStatistics/query/${batchId}`,
        method: 'get',
        params
    })
}

export function removeBatchItem(batchId: string, batchItemId: string) {
    return request<boolean>({
        url: `${baseURL}/ProjectStatistics/remove-item`,
        method: 'post',
        data: {
            "batchId": batchId,
            "itemId": batchItemId
        }
    })
}

export function addBatchItem(batchId: string, projectIds: ProjectInfoKey[]) {
    return request<boolean>({
        url: `${baseURL}/ProjectStatistics/add-item`,
        method: 'post',
        data: {
            "projectIds": projectIds,
            "batchId": batchId
        }
    })
}

export function reStatistics(batchId: string) {
    return request<boolean>({
        url: `${baseURL}/ProjectStatistics/re-statistics/${batchId}`,
        method: 'post'
    })
}

export function exportStatistics(batchId: string) {
    return request<FileDto>({
        url: `${baseURL}/ProjectStatistics/export/${batchId}`,
        method: 'post'
    })
}

export function reExtractProject(batchId: string, force: boolean, itemIds: string[]) {
    return request<boolean>({
        url: `${baseURL}/ProjectStatistics/create-extract-project-task`,
        method: 'post',
        data: {
            "batchId": batchId,
            "force": force,
            "itemIds": itemIds
        }
    })
}

export function getSummarizedData(userId: string, positionCode: string, statisticsCode: string) {
    return request<number>({
        url: `${baseURL}/Project/summarized-data/${userId}/${statisticsCode}/${positionCode}`,
        method: 'get'
    })
}
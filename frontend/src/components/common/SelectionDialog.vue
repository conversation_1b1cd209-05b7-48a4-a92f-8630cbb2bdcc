<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <el-dialog
      v-model="dialogVisible"
      :title="title"
      :width="width"
      :close-on-click-modal="false"
      @close="handleClose"
  >
    <div class="dialog-content-wrapper" :style="{ maxHeight: bodyMaxHeight }">
      <!-- 搜索表单 -->
      <SearchForm
          v-if="showSearch"
          :form-items="searchFormItems"
          :initial-values="queryParams"
          @search="handleSearch"
          @reset="handleReset"
      />

      <!-- 数据表格 -->
      <DataTable
          :data="list"
          :columns="tableColumns"
          :loading="loading"
          :total="total"
          :max-height="tableHeight"
          :current-page-prop="queryParams.$pageIndex"
          :page-size-prop="queryParams.$pageSize"
          :show-selection="true"
          :selected-rows="selectedRows"
          @page-change="handleCurrentChange"
          @size-change="handleSizeChange"
          @selection-change="handleSelectionChange"
      />
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :disabled="selectedRows.length === 0">
          确定 ({{ selectedRows.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, reactive, watch, computed} from 'vue'
import {ElMessage} from 'element-plus'
import SearchForm, {FormItem} from '@/components/common/SearchForm.vue'
import DataTable, {TableColumn} from '@/components/common/DataTable.vue'

// 定义组件属性
interface Props {
  modelValue: boolean
  title?: string
  width?: string
  tableHeight?: number
  showSearch?: boolean
  searchFormItems?: FormItem[]
  tableColumns: TableColumn[]
  fetchData: (params: any) => Promise<{ rows: any[], totals: number }>
  initialQueryParams?: Record<string, any>
  selectedKeys?: string[] // 已选择的键值数组
  rowKey?: string // 行键值字段名，默认为 'key'
  bodyMaxHeight?: string // 内容区最大高度，支持 px / vh
}

// 定义组件事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void

  (e: 'confirm', selectedRows: any[]): void

  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '选择项目',
  width: '800px',
  tableHeight: 300,
  showSearch: true,
  searchFormItems: () => [],
  initialQueryParams: () => ({}),
  selectedKeys: () => [],
  rowKey: 'key',
  bodyMaxHeight: '60vh'
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const loading = ref(false)
const total = ref(0)
const list = ref<any[]>([])
const selectedRows = ref<any[]>([])

// 查询参数
const queryParams = reactive({
  $pageIndex: 1,
  $pageSize: 20,
  ...props.initialQueryParams
})

// 监听弹窗显示状态
watch(() => props.modelValue, (newVal) => {
  if (newVal) {
    // 弹窗打开时重置选择状态并加载数据
    selectedRows.value = []
    getList()
  }
})

// 监听已选择的键值变化
watch(() => props.selectedKeys, (newKeys) => {
  if (newKeys && newKeys.length > 0 && list.value.length > 0) {
    // 根据已选择的键值设置选中状态
    selectedRows.value = list.value.filter(item =>
        newKeys.includes(item[props.rowKey])
    )
  }
}, {immediate: true})

// 获取列表数据
const getList = async () => {
  try {
    loading.value = true
    const response = await props.fetchData(queryParams)

    list.value = response.rows || []

    // 处理总数
    let totalNumber = 0
    if (response.totals !== undefined && response.totals !== null) {
      if (typeof response.totals === 'number') {
        totalNumber = response.totals
      } else if (typeof response.totals === 'string') {
        totalNumber = parseInt(response.totals, 10) || 0
      }
    }
    total.value = totalNumber

    // 恢复已选择的状态
    if (props.selectedKeys && props.selectedKeys.length > 0) {
      selectedRows.value = list.value.filter(item =>
          props.selectedKeys!.includes(item[props.rowKey])
      )
    }
  } catch (error) {
    console.error('获取列表失败', error)
    ElMessage.error('获取列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = (formData: any) => {
  Object.assign(queryParams, formData)
  queryParams.$pageIndex = 1
  getList()
}

// 重置处理
const handleReset = (formData: any) => {
  Object.assign(queryParams, formData)
  queryParams.$pageIndex = 1
  getList()
}

// 分页处理
const handleSizeChange = (val: number) => {
  queryParams.$pageSize = val
  queryParams.$pageIndex = 1
  getList()
}

const handleCurrentChange = (val: number) => {
  queryParams.$pageIndex = val
  getList()
}

// 选择变化处理
const handleSelectionChange = (rows: any[]) => {
  selectedRows.value = rows
}

// 关闭弹窗
const handleClose = () => {
  emit('close')
  emit('update:modelValue', false)
}

// 确认选择
const handleConfirm = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一项')
    return
  }

  emit('confirm', selectedRows.value)
  emit('update:modelValue', false)
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}



.search-card {
  margin-bottom: 5px;
}

.search-card :deep(.el-card__body) {
  padding: 10px 0;
}

.search-card :deep(.el-form-item) {
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 10px;
}

.table-card {
  margin-bottom: 10px;
}

.table-card :deep().el-card__header {
  padding-top: 10px;
  padding-bottom: 10px;
}

.table-card :deep().el-card__body {
  padding: 10px
}
</style>
